#!/usr/bin/env python3
import pandas as pd
import numpy as np

def analyze_sl_exit():
    """Analyze the specific SL exit that shows incorrect PnL"""
    
    print("=== DEBUGGING SL EXIT ANOMALY ===")
    print("Log shows:")
    print("2025-07-02 01:17:43+00:00: EXIT 347.5110 u S @ 2.15730 (R: SL, Trig: 2.15666)")
    print("PnL: 3.02, Fee: 0.3748, EQ: $10003.02")
    print()
    
    # Load the processed data
    try:
        df = pd.read_parquet('parquet_processed/XRPUSDC/1s/2025-07-02.parquet')
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        df.set_index('timestamp', inplace=True)
        print("✅ Data loaded successfully")
    except Exception as e:
        print(f"❌ Error loading data: {e}")
        return
    
    # Analyze the exit time
    exit_time = '2025-07-02 01:17:43'
    start_time = '2025-07-02 01:17:30'
    end_time = '2025-07-02 01:18:00'
    
    try:
        exit_data = df.loc[start_time:end_time]
        print(f"✅ Found {len(exit_data)} data points around exit time")
    except Exception as e:
        print(f"❌ Error filtering data: {e}")
        return
    
    print()
    print("=== OHLC DATA AROUND EXIT TIME ===")
    for idx, row in exit_data.iterrows():
        timestamp_str = idx.strftime('%H:%M:%S')
        print(f'{timestamp_str} | O: {row["open"]:8.5f} | H: {row["high"]:8.5f} | L: {row["low"]:8.5f} | C: {row["close"]:8.5f}')
    
    print()
    print("=== ANOMALY CHECK ===")
    anomalies_found = False
    for idx, row in exit_data.iterrows():
        # Check for zeros
        if row['open'] <= 0 or row['high'] <= 0 or row['low'] <= 0 or row['close'] <= 0:
            print(f'❌ ZERO VALUES at {idx}: O={row["open"]} H={row["high"]} L={row["low"]} C={row["close"]}')
            anomalies_found = True
            
        # Check OHLC relationships
        if row['high'] < max(row['open'], row['close']):
            print(f'❌ HIGH < max(O,C) at {idx}: H={row["high"]} vs max(O,C)={max(row["open"], row["close"])}')
            anomalies_found = True
            
        if row['low'] > min(row['open'], row['close']):
            print(f'❌ LOW > min(O,C) at {idx}: L={row["low"]} vs min(O,C)={min(row["open"], row["close"])}')
            anomalies_found = True
    
    if not anomalies_found:
        print('✅ No obvious data anomalies found in this time window')
    
    print()
    print("=== PNL CALCULATION ANALYSIS ===")
    
    # From log: SHORT position 347.5110 units, exit price 2.15730, SL trigger 2.15666
    position_size = 347.5110
    exit_price = 2.15730
    sl_trigger = 2.15666
    reported_pnl = 3.02
    
    print(f"Position: SHORT {position_size} units")
    print(f"Exit price: {exit_price}")
    print(f"SL trigger: {sl_trigger}")
    print(f"Reported PnL: ${reported_pnl}")
    print()
    
    # For SHORT position, PnL = (entry_price - exit_price) * position_size
    # If we don't know entry price, but we know it's SL exit, 
    # then entry_price should be LOWER than exit_price for profit
    # But SL for SHORT should trigger when price goes UP (against us)
    
    print("🔍 Analysis:")
    print(f"- SHORT position exited at {exit_price}")
    print(f"- SL trigger was {sl_trigger}")
    print(f"- For SHORT: SL should trigger when price goes UP (loss)")
    print(f"- Exit price ({exit_price}) > SL trigger ({sl_trigger}) = LOSS expected")
    print(f"- But reported PnL is +${reported_pnl} (GAIN) ❌")
    print()
    
    # Check if there are any zero values at the exact exit time
    exit_timestamp = pd.to_datetime(exit_time)
    if exit_timestamp in df.index:
        exit_row = df.loc[exit_timestamp]
        print(f"=== EXACT EXIT ROW DATA ===")
        print(f"Timestamp: {exit_timestamp}")
        print(f"Open: {exit_row['open']} | High: {exit_row['high']} | Low: {exit_row['low']} | Close: {exit_row['close']}")
        
        if exit_row['open'] <= 0 or exit_row['high'] <= 0 or exit_row['low'] <= 0 or exit_row['close'] <= 0:
            print("❌ FOUND ZERO VALUES AT EXIT TIME - THIS IS THE PROBLEM!")
            print("Zero values in OHLC data cause incorrect PnL calculations")
        else:
            print("✅ No zero values at exact exit time")
    else:
        print("⚠️  Exact exit timestamp not found in data")
    
    print()
    print("=== RECOMMENDATION ===")
    print("If zero values are found, the data quality fix needs to be enhanced")
    print("to handle all edge cases, not just the 06:38:35 timestamp we fixed earlier.")

if __name__ == "__main__":
    analyze_sl_exit()