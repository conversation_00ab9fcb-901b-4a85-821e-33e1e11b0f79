# 🔧 Prepočítanie Features s Pln<PERSON> Históriou

Tento balík scriptov rieši problém s chý<PERSON>j<PERSON><PERSON><PERSON> indikátormi (ADX, DMP, DMN, atď.) na začiatku dní tým, že prepočíta všetky features s plnou históriou z predchádzajúcich dní.

## 📋 Problém

**Pôvodný problém:**
- Aprílové dáta: chýbajú ADX_14, DMP_14, DMN_14 indikátory
- Júlov<PERSON> dáta: všetky indikátory prítomné
- Indikátory potrebujú históriu z predchádzajúcich dní pre správny výpočet

**Riešenie:**
- Načítanie všetkých dostupných dát naraz (nie po dňoch)
- **Automatický výpočet** minimálneho lookback na základe indikátorov
- Výpočet indikátorov s roz<PERSON><PERSON><PERSON><PERSON> históriou (obvykle 2-3 dni)
- Uloženie výsledkov po dňoch

## 🚀 Rýchle Spustenie

### Jednoduchý spôsob (odporúčaný):
```bash
./fix_all_indicators.sh
```

Tento script automaticky:
- Prepočíta features pre oba timeframes (1s a 5m)
- **Automaticky vypočíta** minimálny potrebný lookback (obvykle 2-3 dni)
- Spracuje všetky dostupné dáta efektívne

## 🔧 Manuálne Spustenie

### 1. Batch prepočítanie (všetky dostupné dáta):
```bash
# Pre oba timeframes
python batch_recompute_features.py --preset both --raw-data-dir parquet_raw --output-dir parquet_processed

# Len pre 1s
python batch_recompute_features.py --preset 1s --raw-data-dir parquet_raw --output-dir parquet_processed

# Len pre 5m  
python batch_recompute_features.py --preset 5m --raw-data-dir parquet_raw --output-dir parquet_processed
```

### 2. Prepočítanie konkrétneho obdobia:
```bash
python recompute_all_features.py \
    --config strategyConfig_scalp_1s_fixed.json \
    --raw-data-dir parquet_raw \
    --output-dir parquet_processed \
    --start 2025-04-01 \
    --end 2025-04-30 \
    # --lookback-days sa vypočíta automaticky
```

## 📊 Súbory

### Hlavné scripty:
- **`recompute_all_features.py`** - Základný script pre prepočítanie s históriou
- **`batch_recompute_features.py`** - Batch spracovanie všetkých dostupných dát
- **`fix_all_indicators.sh`** - Jednoduchý wrapper pre rýchle spustenie

### Konfigurácie:
- **`strategyConfig_scalp_1s_fixed.json`** - Pre 1s timeframe
- **`strategyConfig_scalp_5m_fixed.json`** - Pre 5m timeframe

## ⚙️ Parametre

### `recompute_all_features.py`:
- `--config` - Cesta k JSON config súboru
- `--raw-data-dir` - Adresár so surovými dátami (default: parquet_raw)
- `--output-dir` - Výstupný adresár (default: parquet_processed)
- `--start` - Začiatočný dátum (YYYY-MM-DD)
- `--end` - Koncový dátum (YYYY-MM-DD)
- `--lookback-days` - Počet dní histórie (default: automatický výpočet)

### `batch_recompute_features.py`:
- `--preset` - Prednastavené konfigurácie: `1s`, `5m`, `both`
- `--max-range-days` - Max dní na jedno spustenie (default: 30)
- Ostatné parametre rovnaké ako vyššie

## 🔍 Ako to funguje

1. **Automatický lookback**: Script analyzuje konfiguráciu a vypočíta minimálny potrebný lookback
2. **Rozšírený rozsah**: Načíta dáta od `start_date - lookback_days` do `end_date`
3. **Plná história**: Indikátory sa počítajú s kompletnou históriou
4. **Filtrovanie výstupu**: Uložia sa len dáta pre cieľový rozsah `start_date` až `end_date`
5. **Po dňoch**: Výsledky sa uložia po dňoch ako pôvodne

### 🧮 Automatický výpočet lookback:
- **RSI_14, ATR_14**: 14 periód
- **ADX_14**: 28 periód (2× kvôli vyhladzovaniu)
- **EMA_21**: 21 periód
- **HMM window 200**: 200 periód (najväčší pre 5m = ~17h)
- **Výsledok**: Obvykle 2-3 dni namiesto 30!

## ✅ Výsledok

Po spustení budú mať všetky dáta:
- ✅ Kompletné ADX_14, DMP_14, DMN_14 indikátory
- ✅ Správne hodnoty na začiatku dní (nie NaN)
- ✅ Konzistentné features medzi rôznymi obdobiami
- ✅ Kompatibilita s existujúcimi simuláciami

## 🧪 Testovanie

Po prepočítaní môžeš otestovať:

```bash
# Test aprílových dát (predtým problematické)
python simulate_trading_new.py --cfg strategyConfig_scalp_1s.json --start 2025-04-01 --end 2025-04-02

# Porovnanie s júlovými dátami
python simulate_trading_new.py --cfg strategyConfig_scalp_1s.json --start 2025-07-01 --end 2025-07-02
```

## 📝 Poznámky

- **Automatický lookback** - vypočíta sa presne na základe indikátorov (2-3 dni)
- **Efektívne spracovanie** - načíta len nevyhnutné množstvo dát
- **Batch spracovanie** rozdeľuje veľké rozsahy na menšie časti
- **Automatická detekcia** dostupných dátumov v raw data adresári
- **Graceful handling** chýbajúcich súborov a chýb

## 🆘 Riešenie problémov

### Chyba "Žiadne dáta nájdené":
- Skontroluj, či existuje `parquet_raw/ohlcv/1s/` alebo `parquet_raw/ohlcv/5m/`
- Skontroluj formát súborov (YYYY-MM-DD.parquet)

### Chyba "Cannot convert numpy.ndarray":
- Toto je problém s pandas/pyarrow verziami, nie s týmto scriptom
- Script by mal fungovať aj s problematickými súbormi

### Pomalé spracovanie:
- Zníž `--max-range-days` na menšiu hodnotu (napr. 7)
- Automatický lookback je už optimalizovaný na minimum
