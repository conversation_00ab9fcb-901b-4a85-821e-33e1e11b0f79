#!/usr/bin/env python3
"""
Test script to verify live trading fixes.
"""
import os
import sys
import logging
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s [%(levelname)s] %(message)s")
log = logging.getLogger("test_fixes")

def test_config_loading():
    """Test config loading and environment variable substitution."""
    log.info("🔍 Testing config loading...")
    
    # Test environment variables
    os.environ["COINAPI_KEY"] = "test_coinapi_key_12345"
    os.environ["BINANCE_API_KEY"] = "test_binance_key_12345"
    os.environ["BINANCE_API_SECRET"] = "test_binance_secret_12345"
    
    try:
        from live_trading import load_config
        
        # Test with existing config
        config_path = Path("strategyConfig_scalp_1s.json")
        if config_path.exists():
            cfg = load_config(config_path)
            log.info(f"✅ Config loaded successfully for symbol: {cfg.get('symbol', 'N/A')}")
            
            # Check API key substitution
            coinapi_key = cfg.get("coinapi", {}).get("apiKey", "")
            if coinapi_key and not coinapi_key.startswith("${"):
                log.info(f"✅ CoinAPI key properly substituted: {coinapi_key[:8]}...")
            else:
                log.warning(f"⚠️ CoinAPI key not substituted: {coinapi_key}")
                
            return True
        else:
            log.warning(f"⚠️ Config file not found: {config_path}")
            return False
            
    except Exception as e:
        log.error(f"❌ Config loading failed: {e}")
        return False

def test_feature_dimensions():
    """Test feature dimension calculations."""
    log.info("🔍 Testing feature dimensions...")
    
    try:
        import json
        from pathlib import Path
        
        config_path = Path("strategyConfig_scalp_1s.json")
        if not config_path.exists():
            log.warning(f"⚠️ Config file not found: {config_path}")
            return False
            
        with open(config_path, 'r') as f:
            cfg = json.load(f)
            
        feature_cols = cfg["envSettings"]["feature_columns"]
        lookback = cfg["envSettings"].get("state_lookback", 30)
        
        # Calculate expected observation space like in the fixed code
        expected_obs_size = len(feature_cols) * lookback + 11  # 11 meta features
        
        log.info(f"📊 Feature dimensions:")
        log.info(f"   Feature columns: {len(feature_cols)}")
        log.info(f"   Lookback: {lookback}")
        log.info(f"   Expected observation space: {expected_obs_size}")
        
        # Check if this matches common model sizes
        common_sizes = [1451, 1597, 2051]  # Common sizes from training
        if expected_obs_size in common_sizes:
            log.info(f"✅ Observation space matches common model size: {expected_obs_size}")
        else:
            log.warning(f"⚠️ Observation space {expected_obs_size} doesn't match common sizes: {common_sizes}")
            log.info(f"   This may cause VecNormalize shape mismatch")
            
        return True
        
    except Exception as e:
        log.error(f"❌ Feature dimension test failed: {e}")
        return False

def test_model_loading():
    """Test model loading without running full live trading."""
    log.info("🔍 Testing model loading...")
    
    try:
        import torch
        from pathlib import Path
        from popart_sac import PopArtSAC
        
        # Find available model
        model_files = list(Path(".").glob("sac_*_steps.zip"))
        if not model_files:
            log.warning("⚠️ No model files found")
            return False
            
        # Use latest model
        model_files.sort(key=lambda x: int(x.stem.split('_')[1]))
        model_path = model_files[-1]
        
        log.info(f"📦 Testing model: {model_path}")
        
        # Test loading with minimal custom objects
        custom_objects = {
            "replay_buffer_kwargs": dict(handle_timeout_termination=False),
            "optimize_memory_usage": False,
            "learning_rate": 0.0001,
            "lr_schedule": None,
        }
        
        device = "cuda" if torch.cuda.is_available() else "cpu"
        model = PopArtSAC.load(model_path, device=device, custom_objects=custom_objects)
        
        log.info(f"✅ Model loaded successfully: {type(model).__name__}")
        log.info(f"   Device: {device}")
        
        # Test observation space
        if hasattr(model, 'observation_space'):
            obs_shape = model.observation_space.shape
            log.info(f"   Model observation space: {obs_shape}")
        
        return True
        
    except Exception as e:
        log.error(f"❌ Model loading test failed: {e}")
        return False

def main():
    """Run all tests."""
    log.info("🚀 Starting live trading fixes test...")
    
    tests = [
        ("Config Loading", test_config_loading),
        ("Feature Dimensions", test_feature_dimensions),
        ("Model Loading", test_model_loading),
    ]
    
    results = []
    for test_name, test_func in tests:
        log.info(f"\n{'='*50}")
        log.info(f"Running test: {test_name}")
        log.info(f"{'='*50}")
        
        try:
            result = test_func()
            results.append((test_name, result))
            
            if result:
                log.info(f"✅ {test_name}: PASSED")
            else:
                log.warning(f"⚠️ {test_name}: FAILED")
                
        except Exception as e:
            log.error(f"❌ {test_name}: ERROR - {e}")
            results.append((test_name, False))
    
    # Summary
    log.info(f"\n{'='*50}")
    log.info("TEST SUMMARY")
    log.info(f"{'='*50}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        log.info(f"  {test_name:<20}: {status}")
    
    log.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        log.info("🎉 All tests passed! Live trading fixes look good.")
        return 0
    else:
        log.warning("⚠️ Some tests failed. Check the issues above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())