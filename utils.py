from __future__ import annotations
import os, json, re, warnings
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Any

import numpy as np
import pandas as pd
from sklearn.preprocessing import StandardScaler

# ------------------------------------------------------------
# Timeframe helpers
# ------------------------------------------------------------
def parse_timeframe_minutes(tf: str) -> int:
    if tf == "trades":
        return 0
    m = re.match(r"^(\d+)([smhdw])$", str(tf).lower())
    if not m:
        warnings.warn(f"Unknown timeframe '{tf}', defaulting to 1m")
        return 1
    val, unit = int(m.group(1)), m.group(2)
    mult = {"s": 1/60, "m": 1, "h": 60, "d": 1440, "w": 10080}[unit]
    return int(val * mult)

def get_required_timeframes(cfg: Dict[str, Any], prim_tf: str) -> List[str]:
    tf_set = {prim_tf}

    for k, v in cfg.get("indicatorSettings", {}).items():
        if isinstance(v, dict) and v.get("enabled", True):
            tf_set.add(v.get("tf", prim_tf))

    # objednávame podľa dĺžky
    return sorted(tf_set, key=parse_timeframe_minutes)

# ------------------------------------------------------------
# Buffer – koľko dát treba načítať
# ------------------------------------------------------------
def calculate_data_buffer(cfg: Dict[str, Any], prim_minutes: int) -> timedelta:
    look = cfg["envSettings"].get("state_lookback", 30)
    max_per = 0
    for v in cfg["indicatorSettings"].values():
        if isinstance(v, dict) and v.get("enabled", True):
            for key in ["basePeriod","baseFastPeriod","baseSlowPeriod","window","length"]:
                if key in v and isinstance(v[key], int):
                    max_per = max(max_per, v[key])
    minutes = (look + max_per + 10) * max(prim_minutes,1)
    return timedelta(minutes=minutes)

# ------------------------------------------------------------
# Škálovanie funkcií (model‑fit helper)
# ------------------------------------------------------------
def _get_columns_to_exclude_from_scaling(cols: List[str]) -> List[str]:
    pat = re.compile(r"^(timestamp|open|high|low|close|volume|.*hmm_state.*|.*hurst.*)$", re.I)
    return [c for c in cols if pat.match(c)]

def _prepare_data_for_fitting(df: pd.DataFrame,
                              feats: List[str]) -> pd.DataFrame:
    X = df[feats].dropna().astype("float32")
    return X

# ------------------------------------------------------------
# HMM (len fit‑helper, samotný výpočet je v indicators.py)
# ------------------------------------------------------------
def _prepare_hmm_feature(df: pd.DataFrame,
                         cfg: Dict[str, Any],
                         hmm_cfg: Dict[str, Any],
                         prim_tf: str,
                         ctx: str) -> pd.DataFrame:
    # delegujeme na indicators._hmm_state → jednoduchosť
    from indicators import _hmm_state
    feat, _ = _hmm_state(df, cfg, hmm_cfg, prim_tf, None, None)
    return feat.to_frame("feature")

# ------------------------------------------------------------
# Config loader + kontrola
# ------------------------------------------------------------
def load_config(path: str) -> Dict[str, Any]:
    with open(path, encoding="utf-8") as f:
        cfg = json.load(f)
    if "symbol" not in cfg or "primaryTimeframe" not in cfg:
        raise ValueError("Config must contain symbol and primaryTimeframe")
    return cfg

def parse_dates(cfg: Dict[str, Any]) -> tuple[datetime, datetime]:
    b = cfg["backtestSettings"]
    s = datetime.fromisoformat(b["startTime"].replace("Z", "+00:00")).astimezone(timezone.utc)
    e = datetime.fromisoformat(b["endTime"].replace("Z", "+00:00")).astimezone(timezone.utc)
    if s >= e:
        raise ValueError("startTime must be before endTime")
    return s, e

# ------------------------------------------------------------
# Kontrola, že pre TSL existuje ATR
# ------------------------------------------------------------
def check_tsl_atr_dependency(cfg: Dict[str,Any],
                             feat_cols: List[str],
                             prim_tf: str) -> bool:
    if not cfg.get("trailingStopLoss", {}).get("enabled", False):
        return True
    # nájsť prvý povolený ATR
    for k,v in cfg["indicatorSettings"].items():
        if k.lower().startswith("atr") and v.get("enabled", True):
            p  = v.get("basePeriod", 14)
            tf = v.get("tf", prim_tf)
            name = f"ATR_{p}" if tf==prim_tf else f"ATR_{p}_{tf}"
            return name in feat_cols
    return False