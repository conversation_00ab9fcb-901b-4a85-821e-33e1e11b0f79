#!/usr/bin/env python3
"""
Test verzia batch porovnania - krat<PERSON><PERSON> obdobie a menej threshold hodnôt.
"""

import os
import json
import glob
import subprocess
import pandas as pd
from pathlib import Path
import datetime
import sys
from typing import List, Dict, <PERSON><PERSON>

def find_sac_models() -> List[Tuple[str, str]]:
    """Nájde všetky dostupné SAC modely a ich VecNormalize súbory."""
    models = []
    
    # Hľadáme .zip súbory s názvom sac_*_steps.zip
    model_files = glob.glob("sac_*_steps.zip")
    
    for model_file in sorted(model_files):
        # Vytvoríme názov pre vecnorm súbor
        base_name = model_file.replace("_steps.zip", "")
        vecnorm_file = f"{base_name}.vecnorm.pkl"
        
        # Skontrolujeme či vecnorm súbor existuje
        if os.path.exists(vecnorm_file):
            models.append((model_file, vecnorm_file))
            print(f"✅ Nájdený model: {model_file} + {vecnorm_file}")
        else:
            print(f"⚠️ Chýba VecNormalize súbor pre: {model_file}")
    
    return models

def get_test_thresholds() -> List[float]:
    """Test verzía s menej threshold hodnôt."""
    return [0.005, 0.01, 0.05, 0.1, 0.5]  # Menší počet pre test

def create_test_config(base_config_path: str, model_path: str, vecnorm_path: str, 
                      threshold: float, output_dir: str) -> str:
    """Vytvorí testovaciu konfiguráciu s upraveným modelom a threshold."""
    
    # Načítame základnú konfiguráciu
    with open(base_config_path, 'r') as f:
        config = json.load(f)
    
    # Upravíme model paths a threshold
    config["trainingSettings"]["modelSavePath"] = model_path.replace(".zip", "")
    config["tradeParams"]["entryActionThreshold"] = threshold
    config["tradeParams"]["exitActionThreshold"] = threshold
    config["tradeParams"]["longEntryThreshold"] = threshold
    config["tradeParams"]["shortEntryThreshold"] = threshold
    
    # Vytvoríme názov súboru
    model_name = Path(model_path).stem
    config_name = f"test_config_{model_name}_thresh_{threshold}.json"
    config_path = os.path.join(output_dir, config_name)
    
    # Uložíme konfiguráciu
    os.makedirs(output_dir, exist_ok=True)
    with open(config_path, 'w') as f:
        json.dump(config, f, indent=2)
    
    return config_path

def run_backtest(config_path: str, start_date: str, end_date: str, 
                model_name: str, threshold: float, results_dir: str) -> Dict:
    """Spustí backtest a vráti základné metriky."""
    
    print(f"\n🚀 Spúšťam backtest: {model_name} s threshold {threshold}")
    
    # Vytvoríme názov pre výstupné súbory
    safe_model_name = model_name.replace("_steps", "").replace("sac_", "")
    trades_file = f"{results_dir}/trades_{safe_model_name}_thresh_{threshold}.csv"
    equity_file = f"{results_dir}/equity_{safe_model_name}_thresh_{threshold}.csv"
    
    # Spustíme backtest
    cmd = [
        "python", "simulate_trading.py",
        "--cfg", config_path,
        "--start", start_date,
        "--end", end_date,
        "--use-1s-decisions",
        "--log-level", "WARNING",  # Znížime logging pre čistejší výstup
        "--out-trades", trades_file,
        "--out-equity", equity_file
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=1800)  # 30min timeout pre test
        
        if result.returncode == 0:
            print(f"✅ Backtest dokončený: {model_name} thresh={threshold}")
            
            # Načítame a analyzujeme výsledky
            if os.path.exists(trades_file):
                trades_df = pd.read_csv(trades_file)
                return analyze_backtest_results(trades_df, equity_file, model_name, threshold)
            else:
                print(f"❌ Neboli vygenerované obchody: {trades_file}")
                return create_empty_result(model_name, threshold, "No trades generated")
        else:
            print(f"❌ Backtest zlyhal: {model_name} thresh={threshold}")
            print(f"Error output: {result.stderr[:500]}...")  # Skrátime error pre čitateľnosť
            return create_empty_result(model_name, threshold, f"Backtest failed")
            
    except subprocess.TimeoutExpired:
        print(f"⏰ Backtest timeout: {model_name} thresh={threshold}")
        return create_empty_result(model_name, threshold, "Timeout")
    except Exception as e:
        print(f"💥 Chyba pri backtest: {model_name} thresh={threshold}: {e}")
        return create_empty_result(model_name, threshold, str(e))

def analyze_backtest_results(trades_df: pd.DataFrame, equity_file: str, 
                           model_name: str, threshold: float) -> Dict:
    """Analyzuje výsledky backtestu a vráti kľúčové metriky."""
    
    try:
        if len(trades_df) == 0:
            return create_empty_result(model_name, threshold, "No trades")
        
        # Základné štatistiky
        total_trades = len(trades_df)
        winning_trades = len(trades_df[trades_df['pnl'] > 0])
        losing_trades = len(trades_df[trades_df['pnl'] <= 0])
        win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0
        
        total_pnl = trades_df['pnl'].sum()
        avg_win = trades_df[trades_df['pnl'] > 0]['pnl'].mean() if winning_trades > 0 else 0
        avg_loss = trades_df[trades_df['pnl'] <= 0]['pnl'].mean() if losing_trades > 0 else 0
        
        # Profit factor calculation
        total_wins = trades_df[trades_df['pnl'] > 0]['pnl'].sum()
        total_losses = abs(trades_df[trades_df['pnl'] <= 0]['pnl'].sum())
        profit_factor = total_wins / total_losses if total_losses > 0 else float('inf')
        
        max_win = trades_df['pnl'].max()
        max_loss = trades_df['pnl'].min()
        
        # Equity curve analýza
        equity_stats = {}
        if os.path.exists(equity_file):
            try:
                equity_df = pd.read_csv(equity_file)
                if len(equity_df) > 0:
                    initial_equity = equity_df['equity'].iloc[0]
                    final_equity = equity_df['equity'].iloc[-1]
                    total_return = ((final_equity - initial_equity) / initial_equity * 100)
                    
                    # Drawdown analýza
                    equity_df['peak'] = equity_df['equity'].cummax()
                    equity_df['drawdown'] = (equity_df['equity'] - equity_df['peak']) / equity_df['peak'] * 100
                    max_drawdown = equity_df['drawdown'].min()
                    
                    equity_stats = {
                        'initial_equity': initial_equity,
                        'final_equity': final_equity,
                        'total_return_pct': total_return,
                        'max_drawdown_pct': max_drawdown
                    }
            except Exception as e:
                print(f"⚠️ Chyba pri načítaní equity: {e}")
        
        return {
            'model_name': model_name,
            'threshold': threshold,
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'win_rate_pct': win_rate,
            'total_pnl': total_pnl,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'profit_factor': profit_factor,
            'max_win': max_win,
            'max_loss': max_loss,
            'status': 'Success',
            'error': None,
            **equity_stats
        }
        
    except Exception as e:
        print(f"❌ Chyba pri analýze výsledkov: {e}")
        return create_empty_result(model_name, threshold, str(e))

def create_empty_result(model_name: str, threshold: float, error: str) -> Dict:
    """Vytvorí prázdny výsledok pre neúspešné testy."""
    return {
        'model_name': model_name,
        'threshold': threshold,
        'total_trades': 0,
        'winning_trades': 0,
        'losing_trades': 0,
        'win_rate_pct': 0,
        'total_pnl': 0,
        'avg_win': 0,
        'avg_loss': 0,
        'profit_factor': 0,
        'max_win': 0,
        'max_loss': 0,
        'status': 'Failed',
        'error': error,
        'initial_equity': 100,
        'final_equity': 100,
        'total_return_pct': 0,
        'max_drawdown_pct': 0
    }

def generate_comparison_report(results: List[Dict], output_dir: str):
    """Vygeneruje súhrnný report s porovnaním všetkých testov."""
    
    df = pd.DataFrame(results)
    
    # Uložíme do CSV
    report_path = os.path.join(output_dir, "test_comparison_report.csv")
    df.to_csv(report_path, index=False)
    
    print(f"\n📊 Report uložený do: {report_path}")
    
    # Vypíšeme najlepšie výsledky na konzolu
    successful_results = df[df['status'] == 'Success'].copy()
    
    if len(successful_results) > 0:
        print(f"\n📈 ÚSPEŠNÉ TESTY: {len(successful_results)}")
        
        # Top 3 by total return
        if 'total_return_pct' in successful_results.columns:
            top_3 = successful_results.nlargest(3, 'total_return_pct')
            print(f"\n🏆 TOP 3 PODĽA TOTAL RETURN:")
            for idx, row in top_3.iterrows():
                print(f"   {row['model_name']} | Thresh: {row['threshold']} | Return: {row['total_return_pct']:.2f}% | Trades: {row['total_trades']}")
        
        # Top 3 by profit factor
        finite_pf = successful_results[successful_results['profit_factor'] != float('inf')].copy()
        if len(finite_pf) > 0:
            top_3_pf = finite_pf.nlargest(3, 'profit_factor')
            print(f"\n🎯 TOP 3 PODĽA PROFIT FACTOR:")
            for idx, row in top_3_pf.iterrows():
                print(f"   {row['model_name']} | Thresh: {row['threshold']} | PF: {row['profit_factor']:.2f} | Return: {row['total_return_pct']:.2f}%")
    
    failed_results = df[df['status'] == 'Failed']
    if len(failed_results) > 0:
        print(f"\n❌ NEÚSPEŠNÉ TESTY: {len(failed_results)}")
        for idx, row in failed_results.iterrows():
            print(f"   {row['model_name']} | Thresh: {row['threshold']} | Error: {row['error']}")

def main():
    """Hlavná funkcia pre test verziu."""
    
    # Konfigurácia pre test
    base_config = "strategyConfig_scalp_1s_debug.json"
    start_date = "2025-01-01"
    end_date = "2025-01-03"  # Iba 3 dni pre test
    
    # Vytvoríme adresár pre výsledky
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = f"test_comparison_{timestamp}"
    os.makedirs(output_dir, exist_ok=True)
    
    print(f"🔍 Test Batch Model Comparison")
    print(f"📅 Testované obdobie: {start_date} - {end_date}")
    print(f"📁 Výstupný adresár: {output_dir}")
    
    # Nájdeme dostupné modely
    models = find_sac_models()
    thresholds = get_test_thresholds()
    
    print(f"\n📊 Nájdených {len(models)} modelov")
    print(f"🎯 Testovaných {len(thresholds)} threshold hodnôt")
    print(f"🔢 Celkovo {len(models) * len(thresholds)} kombinácií")
    
    if len(models) == 0:
        print("❌ Neboli nájdené žiadne SAC modely!")
        return
    
    # Spustíme testy pre všetky kombinácie
    results = []
    total_combinations = len(models) * len(thresholds)
    current_combination = 0
    
    for model_file, vecnorm_file in models:
        model_name = Path(model_file).stem
        
        for threshold in thresholds:
            current_combination += 1
            print(f"\n📈 Progress: {current_combination}/{total_combinations}")
            
            # Vytvoríme testovaciu konfiguráciu
            test_config = create_test_config(
                base_config, model_file, vecnorm_file, threshold, output_dir
            )
            
            # Spustíme backtest
            result = run_backtest(
                test_config, start_date, end_date, model_name, threshold, output_dir
            )
            results.append(result)
    
    # Vygenerujeme súhrnný report
    print(f"\n📊 Generujem súhrnný report...")
    generate_comparison_report(results, output_dir)
    
    print(f"\n✅ Test comparison dokončený!")
    print(f"📁 Všetky výsledky v adresári: {output_dir}")

if __name__ == "__main__":
    main()