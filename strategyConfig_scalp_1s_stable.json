{"primaryTimeframe": "5m", "use_1s_decisions": true, "tradeParams": {"longEntryThreshold": 0.35, "shortEntryThreshold": 0.35, "exitActionThreshold": 0.25, "entryActionThreshold": 0.35, "feePercentage": 0.1, "slippagePercentage": 0.0, "minSLDistancePercent": 0.1, "minSLDistanceATR": 1.0, "minAtrPercentOfPrice": 0.05, "rrTarget": 2.0}, "account": {"initialEquity": 10000, "maxRiskPerTrade": 0.02, "maxDailyLoss": 0.05, "maxDailyTrades": 50}, "tsConfig": {"enableTSL": true, "tslType": "atr", "atrPeriod": 14, "atrMultiplier": 1.5, "tslActivationRR": 0.8, "tslUpdateFrequency": 1}, "indicators": {"rsi": {"period": 21, "oversold": 25, "overbought": 75}, "ema": {"fast_period": 8, "slow_period": 21}, "atr": {"period": 14}, "bollinger": {"period": 20, "std_dev": 2.0}, "macd": {"fast_period": 12, "slow_period": 26, "signal_period": 9}, "obv_sma": {"period": 10}, "vwap": {"enabled": true}, "support_resistance": {"lookback_period": 50, "min_touches": 2}, "volume_profile": {"bins": 20, "value_area_percentage": 70}, "volatility": {"atr_period": 14, "rolling_std_period": 20}}, "dataProvider": {"provider": "binance", "symbol": "XRPUSDC", "useTestnet": false}, "coinapi": {"apiKey": "${COINAPI_KEY}"}, "riskManagement": {"dailyStopLoss": 5.0, "maxPositionSize": 2.0, "maxDrawdown": 10.0}, "binance": {"apiKey": "${BINANCE_API_KEY}", "apiSecret": "${BINANCE_API_SECRET}", "futures": true, "leverage": 10, "marginType": "ISOLATED"}, "testing": {"enabled": false, "start_date": "2025-01-07", "end_date": "2025-01-08"}}