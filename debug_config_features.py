#!/usr/bin/env python3
"""
Debug script na overenie konfigurácie features
"""
import json
import sys
from pathlib import Path

def debug_config_features():
    """Debug konfigurácie features"""
    print("=== DEBUG CONFIG FEATURES ===")
    
    # Check if config file exists
    config_path = Path("strategyConfig_scalp_1s.json")
    if not config_path.exists():
        print(f"❌ Config file not found: {config_path}")
        return False
    
    print(f"✅ Config file found: {config_path}")
    
    # Load config exactly like live_trading.py does
    try:
        with open(config_path, 'r') as f:
            content = f.read()
            # Replace environment variables (like in live_trading.py)
            import os
            import re
            def replace_env_var(match):
                var_name = match.group(1)
                return os.environ.get(var_name, match.group(0))
            content = re.sub(r'\$\{([^}]+)\}', replace_env_var, content)
            cfg = json.loads(content)
        
        print("✅ Config loaded successfully")
        
    except Exception as e:
        print(f"❌ Error loading config: {e}")
        return False
    
    # Check feature_columns
    if "envSettings" not in cfg:
        print("❌ envSettings not found in config")
        return False
    
    if "feature_columns" not in cfg["envSettings"]:
        print("❌ feature_columns not found in envSettings")
        return False
    
    feature_columns = cfg["envSettings"]["feature_columns"]
    print(f"✅ feature_columns found: {len(feature_columns)} features")
    
    # Check type
    print(f"Type: {type(feature_columns)}")
    
    # Show first and last features
    print(f"First 5: {feature_columns[:5]}")
    print(f"Last 5: {feature_columns[-5:]}")
    
    # Check if it's exactly 48
    if len(feature_columns) == 48:
        print("✅ Correct length: 48 features")
    else:
        print(f"❌ Wrong length: {len(feature_columns)} features (expected 48)")
        
        # Show all features if not too many
        if len(feature_columns) <= 100:
            print("All features:")
            for i, feat in enumerate(feature_columns):
                print(f"  {i+1:2d}. {feat}")
    
    # Check other relevant settings
    lookback = cfg["envSettings"].get("state_lookback", 30)
    print(f"Lookback: {lookback}")
    
    expected_obs_size = len(feature_columns) * lookback + 11
    print(f"Expected observation space: {expected_obs_size}")
    
    # Test if config can be modified
    print("\n--- Testing config mutability ---")
    original_len = len(feature_columns)
    
    # Try to modify (this should not affect the original)
    test_features = feature_columns.copy()
    test_features.append("test_feature")
    print(f"After adding test feature to copy: {len(test_features)}")
    print(f"Original feature_columns length: {len(feature_columns)}")
    
    if len(feature_columns) == original_len:
        print("✅ Config is not accidentally modified")
    else:
        print("❌ Config was accidentally modified!")
    
    return len(feature_columns) == 48

def debug_live_trading_import():
    """Debug importu live_trading modulu"""
    print("\n=== DEBUG LIVE_TRADING IMPORT ===")
    
    try:
        # Import live_trading module
        import live_trading
        print("✅ live_trading module imported successfully")
        
        # Check if load_config function exists
        if hasattr(live_trading, 'load_config'):
            print("✅ load_config function found")
            
            # Test load_config function
            config_path = Path("strategyConfig_scalp_1s.json")
            cfg = live_trading.load_config(config_path)
            
            feature_columns = cfg["envSettings"]["feature_columns"]
            print(f"✅ Config loaded via live_trading.load_config: {len(feature_columns)} features")
            
            if len(feature_columns) == 48:
                print("✅ Correct length via live_trading.load_config")
                return True
            else:
                print(f"❌ Wrong length via live_trading.load_config: {len(feature_columns)}")
                return False
        else:
            print("❌ load_config function not found")
            return False
            
    except Exception as e:
        print(f"❌ Error importing live_trading: {e}")
        return False

def main():
    """Main debug function"""
    print("🔍 DEBUGGING CONFIG FEATURES")
    print("=" * 50)
    
    # Test direct config loading
    direct_ok = debug_config_features()
    
    # Test live_trading import
    import_ok = debug_live_trading_import()
    
    print("=" * 50)
    print("🏁 DEBUG RESULTS")
    print("=" * 50)
    
    print(f"Direct config loading: {'✅ OK' if direct_ok else '❌ FAIL'}")
    print(f"Live trading import: {'✅ OK' if import_ok else '❌ FAIL'}")
    
    if direct_ok and import_ok:
        print("\n✅ CONFIG IS CORRECT!")
        print("The problem must be elsewhere in the live trading code.")
        print("Check for:")
        print("  1. Global variable overwrites")
        print("  2. Reference modifications")
        print("  3. Dynamic config changes")
    else:
        print("\n❌ CONFIG PROBLEM FOUND!")
        print("Fix the config loading before proceeding.")
    
    return direct_ok and import_ok

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
