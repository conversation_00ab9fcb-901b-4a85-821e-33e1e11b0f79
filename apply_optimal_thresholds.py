#!/usr/bin/env python3
"""
Aplikuje optimálne thresholdy na existujúce konfiguračné súbory.
"""

import json
import sys
from pathlib import Path

def load_optimal_thresholds(threshold_file: str) -> dict:
    """Načíta optimálne thresholdy z JSON súboru."""
    with open(threshold_file, 'r') as f:
        return json.load(f)

def apply_thresholds_to_config(config_path: str, model_name: str, optimal_thresholds: dict, output_path: str = None):
    """Aplikuje optimálne thresholdy na konfiguračný súbor."""
    
    # Načítame config
    with open(config_path, 'r') as f:
        config = json.load(f)
    
    # Nájdeme správny model v optimal_thresholds
    model_config = None
    for model, thresholds in optimal_thresholds.items():
        if model_name in model:
            model_config = thresholds
            break
    
    if model_config is None:
        print(f"❌ Nenájdené optimálne thresholdy pre model: {model_name}")
        return None
    
    # Aplikujeme thresholdy
    config["tradeParams"]["entryActionThreshold"] = model_config["entryActionThreshold"]
    config["tradeParams"]["exitActionThreshold"] = model_config["exitActionThreshold"] 
    config["tradeParams"]["longEntryThreshold"] = model_config["longEntryThreshold"]
    config["tradeParams"]["shortEntryThreshold"] = model_config["shortEntryThreshold"]
    
    # Určíme output path
    if output_path is None:
        base_name = Path(config_path).stem
        output_path = f"{base_name}_optimized.json"
    
    # Uložíme
    with open(output_path, 'w') as f:
        json.dump(config, f, indent=2)
    
    print(f"✅ Optimalizovaný config uložený: {output_path}")
    print(f"   Entry threshold: {model_config['entryActionThreshold']}")
    print(f"   Exit threshold: {model_config['exitActionThreshold']}")
    
    return output_path

def main():
    if len(sys.argv) < 3:
        print("Usage: python apply_optimal_thresholds.py <optimal_thresholds.json> <model_name> [config_file]")
        print("Example: python apply_optimal_thresholds.py optimal_thresholds_20250207.json sac_1999360 strategyConfig_scalp_1s.json")
        sys.exit(1)
    
    threshold_file = sys.argv[1]
    model_name = sys.argv[2]
    config_file = sys.argv[3] if len(sys.argv) > 3 else "strategyConfig_scalp_1s.json"
    
    if not Path(threshold_file).exists():
        print(f"❌ Threshold súbor neexistuje: {threshold_file}")
        sys.exit(1)
    
    if not Path(config_file).exists():
        print(f"❌ Config súbor neexistuje: {config_file}")
        sys.exit(1)
    
    try:
        optimal_thresholds = load_optimal_thresholds(threshold_file)
        apply_thresholds_to_config(config_file, model_name, optimal_thresholds)
    except Exception as e:
        print(f"❌ Chyba: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()