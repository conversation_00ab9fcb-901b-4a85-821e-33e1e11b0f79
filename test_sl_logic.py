#!/usr/bin/env python3

def test_sl_logic():
    print("=== QUICK SL LOGIC TEST ===")
    
    # Known values from debug
    known_entry = 2.16599
    known_sl_trigger = 2.15666
    known_sl_distance = abs(known_entry - known_sl_trigger)  # 0.00933
    
    print(f"Known entry: {known_entry}")
    print(f"Known SL trigger: {known_sl_trigger}")
    print(f"Known SL distance: {known_sl_distance:.5f}")
    
    print("\n=== TESTING SL CALCULATION LOGIC ===")
    
    # Test current logic from simulate_trading_new.py line 1666:
    # sl_price = sim_entry_price - min_sl_dist_points if triggered_pos == 1 else sim_entry_price + min_sl_dist_points
    
    sim_entry_price = known_entry
    min_sl_dist_points = known_sl_distance
    
    # Test LONG (triggered_pos = 1)
    triggered_pos = 1
    sl_price_long = sim_entry_price - min_sl_dist_points if triggered_pos == 1 else sim_entry_price + min_sl_dist_points
    print(f"\nLONG (triggered_pos=1):")
    print(f"  SL = {sim_entry_price:.5f} - {min_sl_dist_points:.5f} = {sl_price_long:.5f}")
    print(f"  Matches known SL? {abs(sl_price_long - known_sl_trigger) < 0.001}")
    
    # Test SHORT (triggered_pos = -1)
    triggered_pos = -1
    sl_price_short = sim_entry_price - min_sl_dist_points if triggered_pos == 1 else sim_entry_price + min_sl_dist_points
    print(f"\nSHORT (triggered_pos=-1):")
    print(f"  SL = {sim_entry_price:.5f} + {min_sl_dist_points:.5f} = {sl_price_short:.5f}")
    print(f"  Matches known SL? {abs(sl_price_short - known_sl_trigger) < 0.001}")
    
    print("\n=== CONCLUSION ===")
    if abs(sl_price_long - known_sl_trigger) < 0.001:
        print("🚨 BUG CONFIRMED: The SL calculation uses LONG logic (triggered_pos=1)")
        print("   This means the position was incorrectly identified as LONG instead of SHORT")
        print("   OR the entry_sig was positive instead of negative")
    elif abs(sl_price_short - known_sl_trigger) < 0.001:
        print("✅ SL calculation is correct for SHORT position")
    else:
        print("❓ Neither LONG nor SHORT logic matches - different issue")
        
    print("\n=== TESTING THRESHOLD LOGIC ===")
    # Test different entry_sig values to see what would trigger what
    long_entry_thr = 0.5
    short_entry_thr = 0.5
    
    test_signals = [0.6, 0.3, -0.3, -0.6, 0.0]
    
    for entry_sig in test_signals:
        triggered_pos = 0
        if entry_sig > long_entry_thr:
            triggered_pos = 1
            pos_type = "LONG"
        elif entry_sig < -short_entry_thr:
            triggered_pos = -1
            pos_type = "SHORT"
        else:
            pos_type = "NONE"
            
        print(f"entry_sig={entry_sig:+.1f} → {pos_type} (triggered_pos={triggered_pos})")

if __name__ == "__main__":
    test_sl_logic()