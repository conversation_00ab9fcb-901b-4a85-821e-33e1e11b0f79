#!/usr/bin/env python3
"""
Configuration loader with environment variable support and API key management.
Handles secure loading of API keys and trading configuration.
"""

import os
import json
import logging
from pathlib import Path
from typing import Dict, Any, Optional
from dotenv import load_dotenv

log = logging.getLogger("ConfigLoader")

class ConfigLoader:
    """Secure configuration loader with environment variable substitution."""
    
    def __init__(self, env_file: Optional[str] = None):
        """Initialize config loader and load environment variables."""
        # Load environment variables from .env file if it exists
        env_path = env_file or ".env"
        if os.path.exists(env_path):
            load_dotenv(env_path)
            log.info(f"Loaded environment variables from {env_path}")
        else:
            log.warning(f"Environment file {env_path} not found - using system environment only")
    
    def load_config(self, config_path: str) -> Dict[str, Any]:
        """
        Load configuration file with environment variable substitution.
        
        Args:
            config_path: Path to JSON configuration file
            
        Returns:
            Configuration dictionary with substituted values
        """
        config_file = Path(config_path)
        if not config_file.exists():
            raise FileNotFoundError(f"Configuration file not found: {config_path}")
        
        # Load JSON config
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # Substitute environment variables
        config = self._substitute_env_vars(config)
        
        # Validate critical API keys
        self._validate_api_keys(config)
        
        log.info(f"Configuration loaded from {config_path}")
        return config
    
    def _substitute_env_vars(self, obj: Any) -> Any:
        """Recursively substitute environment variables in configuration."""
        if isinstance(obj, dict):
            return {key: self._substitute_env_vars(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [self._substitute_env_vars(item) for item in obj]
        elif isinstance(obj, str) and obj.startswith("${") and obj.endswith("}"):
            # Extract environment variable name
            env_var = obj[2:-1]
            value = os.getenv(env_var)
            if value is None:
                log.warning(f"Environment variable {env_var} not found, keeping placeholder")
                return obj
            return value
        else:
            return obj
    
    def _validate_api_keys(self, config: Dict[str, Any]) -> None:
        """Validate that required API keys are present."""
        # Check CoinAPI key
        coinapi_key = config.get("coinapi", {}).get("apiKey", "")
        if not coinapi_key or coinapi_key.startswith("${"):
            log.error("CoinAPI key not configured or environment variable not set")
            log.error("Please set COINAPI_KEY environment variable or update .env file")
        
        # Check Binance keys
        binance_config = config.get("binance", {})
        api_key = binance_config.get("apiKey", "")
        api_secret = binance_config.get("apiSecret", "")
        
        if not api_key or api_key.startswith("${"):
            log.error("Binance API key not configured or environment variable not set")
            log.error("Please set BINANCE_API_KEY environment variable or update .env file")
        
        if not api_secret or api_secret.startswith("${"):
            log.error("Binance API secret not configured or environment variable not set") 
            log.error("Please set BINANCE_API_SECRET environment variable or update .env file")
    
    def get_trading_mode(self) -> str:
        """Get trading mode from environment or default to test."""
        return os.getenv("TRADING_MODE", "test").lower()
    
    def should_use_fallback_data(self) -> bool:
        """Check if fallback data sources should be used."""
        return os.getenv("USE_BINANCE_FALLBACK", "true").lower() == "true"
    
    def should_use_cached_data(self) -> bool:
        """Check if cached data should be used."""
        return os.getenv("USE_CACHED_DATA", "true").lower() == "true"

def load_config_secure(config_path: str, env_file: Optional[str] = None) -> Dict[str, Any]:
    """
    Convenience function to load configuration securely.
    
    Args:
        config_path: Path to JSON configuration file
        env_file: Optional path to .env file
        
    Returns:
        Configuration dictionary with substituted values
    """
    loader = ConfigLoader(env_file)
    return loader.load_config(config_path)

if __name__ == "__main__":
    # Test configuration loading
    try:
        config = load_config_secure("strategyConfig_scalp_1s.json")
        print("✅ Configuration loaded successfully")
        print(f"Test mode: {config.get('testMode', 'unknown')}")
        print(f"Symbol: {config.get('dataProvider', {}).get('symbol', 'unknown')}")
    except Exception as e:
        print(f"❌ Configuration loading failed: {e}")