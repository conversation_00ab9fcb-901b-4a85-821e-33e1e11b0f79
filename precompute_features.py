#!/usr/bin/env python3
"""
Script na predpočítanie features pre simuláciu tradingu.
Spracováva surové OHLCV, orderbook a trade dáta na features potrebné pre agent.
"""

import json
import logging
import pandas as pd
from pathlib import Path
from datetime import datetime, timezone
import numpy as np
from indicators import calculate_and_merge_indicators
import warnings

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def validate_ohlc_data(df: pd.DataFrame) -> tuple:
    """
    Validuje OHLC dáta a vráti počet neplatných hodnôt.
    
    Args:
        df: DataFrame s OHLC dátami
        
    Returns:
        tuple: (invalid_open, invalid_high, invalid_low, invalid_close)
    """
    invalid_open = (df['open'] <= 0).sum()
    invalid_high = (df['high'] <= 0).sum()
    invalid_low = (df['low'] <= 0).sum()
    invalid_close = (df['close'] <= 0).sum()
    
    return invalid_open, invalid_high, invalid_low, invalid_close

def fix_ohlc_data(df: pd.DataFrame) -> pd.DataFrame:
    """
    Opraví neplatné OHLC dáta pomocou forward-fill a OHLC vztahov.
    
    Args:
        df: DataFrame s OHLC dátami
        
    Returns:
        DataFrame s opravenými dátami
    """
    df = df.copy()
    
    # Najprv forward-fill pre všetky stĺpce
    df['open'] = df['open'].replace(0, np.nan).ffill()
    df['high'] = df['high'].replace(0, np.nan).ffill()
    df['low'] = df['low'].replace(0, np.nan).ffill()
    df['close'] = df['close'].replace(0, np.nan).ffill()
    
    # Ak stále existujú NaN hodnoty na začiatku, použijeme backward-fill
    df['open'] = df['open'].bfill()
    df['high'] = df['high'].bfill()
    df['low'] = df['low'].bfill()
    df['close'] = df['close'].bfill()
    
    # Validácia OHLC vztahov
    # High musí byť >= max(open, close)
    df['high'] = np.maximum(df['high'], np.maximum(df['open'], df['close']))
    
    # Low musí byť <= min(open, close)
    df['low'] = np.minimum(df['low'], np.minimum(df['open'], df['close']))
    
    # Volume a count nesmú byť záporné
    if 'volume' in df.columns:
        df['volume'] = df['volume'].clip(lower=0)
    if 'count' in df.columns:
        df['count'] = df['count'].clip(lower=0)
    
    return df

def check_and_fix_data_quality(df: pd.DataFrame, file_path: str = "unknown") -> pd.DataFrame:
    """
    Skontroluje a opraví kvalitu OHLC dát.
    
    Args:
        df: DataFrame s OHLC dátami
        file_path: Cesta k súboru pre logovanie
        
    Returns:
        DataFrame s opravenými dátami
    """
    # Kontrola pred opravou
    invalid_before = validate_ohlc_data(df)
    
    if sum(invalid_before) > 0:
        logger.warning(f"Nájdené neplatné OHLC hodnoty v {file_path}:")
        logger.warning(f"  open: {invalid_before[0]} neplatných hodnôt")
        logger.warning(f"  high: {invalid_before[1]} neplatných hodnôt")
        logger.warning(f"  low: {invalid_before[2]} neplatných hodnôt")
        logger.warning(f"  close: {invalid_before[3]} neplatných hodnôt")
        
        # Oprava dát
        df_fixed = fix_ohlc_data(df)
        
        # Kontrola po oprave
        invalid_after = validate_ohlc_data(df_fixed)
        
        if sum(invalid_after) == 0:
            logger.info(f"✅ Všetky neplatné OHLC hodnoty boli opravené v {file_path}")
        else:
            logger.warning(f"⚠️ Zostávajúce neplatné hodnoty v {file_path}:")
            logger.warning(f"  open: {invalid_after[0]}, high: {invalid_after[1]}, low: {invalid_after[2]}, close: {invalid_after[3]}")
        
        return df_fixed
    else:
        logger.info(f"✅ Žiadne neplatné OHLC hodnoty v {file_path}")
        return df

def fill_missing_features(features_df: pd.DataFrame, expected_features: list) -> pd.DataFrame:
    """
    Doplní chýbajúce features z konfigurácie pomocou placeholder hodnôt.
    
    Args:
        features_df: DataFrame s existujúcimi features
        expected_features: Zoznam všetkých features z konfigurácie
        
    Returns:
        DataFrame s všetkými očakávanými features
    """
    # Zoznam orderbook features, ktoré nastavíme na 0
    orderbook_features = [
        "spread", "mid_price", "tob_imbalance", "depth_imbalance5", "depth_slope5",
        "ob_bid_vol_l1", "ob_ask_vol_l1", "ob_bid_vol_l2", "ob_ask_vol_l2",
        "ob_bid_vol_l3", "ob_ask_vol_l3", "ob_bid_vol_l4", "ob_ask_vol_l4",
        "ob_bid_vol_l5", "ob_ask_vol_l5", "dvol_bid_l1", "dvol_ask_l1"
    ]
    
    # Zoznam order flow features, ktoré nastavíme na 0
    order_flow_features = [
        "volume_delta_30s", "volume_delta_2m", "trade_count_delta_100t",
        "trade_count_delta_500t", "cvd_reset_daily", "trade_dir_sum_1s",
        "trade_skew_1s", "dt_since_buy", "dt_since_sell"
    ]
    
    # Trade volume features - použijeme základný volume ak chýbajú
    if "buy_volume" not in features_df.columns:
        features_df["buy_volume"] = features_df["volume"] * 0.5
    if "sell_volume" not in features_df.columns:
        features_df["sell_volume"] = features_df["volume"] * 0.5
    if "trade_count" not in features_df.columns:
        features_df["trade_count"] = 100  # Konštantná hodnota
    
    # Doplnenie chýbajúcích orderbook features
    for feature in orderbook_features:
        if feature not in features_df.columns:
            if feature == "spread":
                features_df[feature] = 0.0001  # Malý spread
            elif feature == "mid_price":
                features_df[feature] = features_df["close"]
            else:
                features_df[feature] = 0.0
        else:
            # Ak feature existuje ale má NaN, nahradíme vhodnou hodnotou
            if feature == "spread":
                features_df[feature] = features_df[feature].fillna(0.0001)
            elif feature == "mid_price":
                features_df[feature] = features_df[feature].fillna(features_df["close"])
            else:
                features_df[feature] = features_df[feature].fillna(0.0)
    
    # Doplnenie chýbajúcich order flow features
    for feature in order_flow_features:
        if feature not in features_df.columns:
            features_df[feature] = 0.0
        else:
            # Ak feature existuje ale má NaN, nahradíme nulou
            features_df[feature] = features_df[feature].fillna(0.0)
    
    # Doplnenie HMM state ak chýba
    if "hmm_state_3c_volatility_5m" not in features_df.columns:
        features_df["hmm_state_3c_volatility_5m"] = 1  # Neurálny stav
    else:
        features_df["hmm_state_3c_volatility_5m"] = features_df["hmm_state_3c_volatility_5m"].fillna(1)
    
    # NENAHRÁDZAME technické indikátory nulami! Iba forward-fill
    technical_indicators = [
        "ATR_14", "RSI_14", "EMA_9", "EMA_21", "ADX_14", "DMP_14", "DMN_14",
        "bollinger_bands_upper_20_2.0", "bollinger_bands_middle_20_2.0",
        "bollinger_bands_lower_20_2.0", "bollinger_bands_width_20_2.0", "vwap"
    ]
    
    # Pre technické indikátory použijeme iba forward-fill (nie nuly!)
    for indicator in technical_indicators:
        if indicator in features_df.columns:
            nan_count_before = features_df[indicator].isna().sum()
            if nan_count_before > 0:
                features_df[indicator] = features_df[indicator].ffill()
                nan_count_after = features_df[indicator].isna().sum()
                logger.info(f"Technický indikátor {indicator}: forward-fill {nan_count_before} -> {nan_count_after} NaN")
    
    # Špeciálne ošetrenie pre VWAP_pta - ak má všetky NaN, nahradíme close cenou
    if "VWAP_pta" in features_df.columns:
        nan_count = features_df["VWAP_pta"].isna().sum()
        if nan_count > 0:
            if nan_count == len(features_df):
                # Všetky NaN - trade dáta chýbajú úplne, použijeme close ako proxy
                features_df["VWAP_pta"] = features_df["close"]
                logger.info(f"VWAP_pta: nahradené všetky {nan_count} NaN hodnotami close ceny (trade dáta chýbajú)")
            else:
                # Čiastočné NaN - iba forward-fill
                features_df["VWAP_pta"] = features_df["VWAP_pta"].ffill()
                logger.info(f"VWAP_pta: forward-fill {nan_count} -> {features_df['VWAP_pta'].isna().sum()} NaN")
    
    # Pre ostatné features (nie technické indikátory) môžeme použiť nuly
    for col in features_df.columns:
        if col not in technical_indicators and features_df[col].isna().any():
            if col in expected_features:
                logger.info(f"Nahrádzam NaN hodnoty v non-technical feature {col}")
                features_df[col] = features_df[col].fillna(0.0)
    
    # Zoradenie stĺpcov podľa očakávaného poradia
    available_features = [f for f in expected_features if f in features_df.columns]
    missing_features = [f for f in expected_features if f not in features_df.columns]
    
    if missing_features:
        logger.warning(f"Stále chýba {len(missing_features)} features: {missing_features[:5]}...")
        for feature in missing_features:
            features_df[feature] = 0.0
    
    # Vrátime DataFrame s features v správnom poradí
    return features_df[expected_features]

def precompute_features_for_simulation(config_path: str, raw_data_dir: str, output_dir: str,
                                     start_date: str, end_date: str):
    """
    Predpočíta features z konfigurácie a uloží ich do parquet súborov.
    
    Args:
        config_path: Cesta k JSON konfiguračnému súboru
        raw_data_dir: Adresár so surovými dátami (OHLCV, orderbooks, trades)
        output_dir: Adresár pre výstupné parquet súbory s features
        start_date: Začiatočný dátum (YYYY-MM-DD)
        end_date: Koncový dátum (YYYY-MM-DD)
    """
    
    # Načítanie konfigurácie
    with open(config_path, 'r') as f:
        config = json.load(f)
    
    symbol = config["symbol"]
    primary_tf = config["primaryTimeframe"]
    
    logger.info(f"Predpočítavam features pre {symbol}, timeframe {primary_tf}")
    logger.info(f"Obdobie: {start_date} - {end_date}")
    
    # Načítanie surových dát
    raw_data_path = Path(raw_data_dir)
    data_dict = {}
    
    # 1. Načítanie OHLCV dát (primary timeframe)
    ohlcv_dir = raw_data_path / "ohlcv" / primary_tf
    if ohlcv_dir.exists():
        logger.info(f"Načítavam OHLCV dáta z {ohlcv_dir}")
        ohlcv_files = sorted(ohlcv_dir.glob("*.parquet"))
        
        start_dt = datetime.strptime(start_date, "%Y-%m-%d").replace(tzinfo=timezone.utc)
        end_dt = datetime.strptime(end_date, "%Y-%m-%d").replace(tzinfo=timezone.utc)
        
        # Filtrovanie súborov podľa dátumu
        relevant_files = []
        for file in ohlcv_files:
            file_date_str = file.stem  # napr. "2025-06-24"
            try:
                file_date = datetime.strptime(file_date_str, "%Y-%m-%d").replace(tzinfo=timezone.utc)
                if start_dt <= file_date <= end_dt:
                    relevant_files.append(file)
            except ValueError:
                continue
        
        if relevant_files:
            ohlcv_dfs = []
            for file in relevant_files:
                df = pd.read_parquet(file)
                if "timestamp" in df.columns:
                    df.set_index("timestamp", inplace=True)
                df.index = pd.to_datetime(df.index, utc=True)
                
                # Aplikácia data quality fix
                df = check_and_fix_data_quality(df, str(file))
                
                ohlcv_dfs.append(df)
            
            data_dict[primary_tf] = pd.concat(ohlcv_dfs).sort_index()
            logger.info(f"Načítaných {len(data_dict[primary_tf])} OHLCV riadkov")
        else:
            logger.error(f"Žiadne OHLCV súbory pre obdobie {start_date} - {end_date}")
            return
    else:
        logger.error(f"OHLCV adresár neexistuje: {ohlcv_dir}")
        return
    
    # 2. Načítanie orderbook dát (ak existujú)
    orderbook_dir = raw_data_path / "orderbooks"
    if orderbook_dir.exists():
        logger.info(f"Načítavam orderbook dáta z {orderbook_dir}")
        # Podobná logika ako pre OHLCV
        # data_dict["orderbooks"] = load_orderbook_data(orderbook_dir, start_date, end_date)
    
    # 3. Načítanie trade dát (ak existujú)
    trades_dir = raw_data_path / "trades"
    if trades_dir.exists():
        logger.info(f"Načítavam trade dáta z {trades_dir}")
        # data_dict["trades"] = load_trades_data(trades_dir, start_date, end_date)
    
    # 4. Výpočet features
    logger.info("Začínam výpočet features...")
    try:
        features_df, feature_names = calculate_and_merge_indicators(
            data=data_dict,
            cfg=config,
            skip_hmm=False
        )
        
        logger.info(f"Vypočítaných {len(feature_names)} features: {feature_names[:10]}...")
        logger.info(f"Features DataFrame shape: {features_df.shape}")
        
        # Doplnenie chýbajúcich features na požadovaných 67
        expected_features = config["envSettings"]["feature_columns"]
        logger.info(f"Doplňujem chýbajúce features na celkovo {len(expected_features)} features...")
        
        features_df = fill_missing_features(features_df, expected_features)
        logger.info(f"Finálny features DataFrame shape: {features_df.shape}")
        
    except Exception as e:
        logger.error(f"Chyba pri výpočte features: {e}")
        return
    
    # 5. Uloženie do parquet súborov
    output_path = Path(output_dir)
    output_symbol_dir = output_path / symbol / primary_tf
    output_symbol_dir.mkdir(parents=True, exist_ok=True)
    
    logger.info(f"Ukladám features do {output_symbol_dir}")
    
    # Rozdelenie podľa dní a uloženie
    features_df_grouped = features_df.groupby(features_df.index.date)
    
    for date, day_df in features_df_grouped:
        output_file = output_symbol_dir / f"{date}.parquet"
        # Reset index to make timestamp a column (required by simulate_trading.py)
        day_df_with_timestamp = day_df.reset_index()
        day_df_with_timestamp.to_parquet(output_file, engine='pyarrow', index=False)
        logger.info(f"Uložený súbor {output_file} s {len(day_df_with_timestamp)} riadkami")
    
    logger.info("✅ Predpočítanie features dokončené!")
    
    # 6. Validácia výsledkov
    logger.info("Validujem výsledky...")
    expected_features = config["envSettings"]["feature_columns"]
    missing_features = [f for f in expected_features if f not in features_df.columns]
    
    if missing_features:
        logger.warning(f"Chýbajúce features (budú nahradené nulami): {missing_features}")
    else:
        logger.info("✅ Všetky potrebné features sú prítomné")
    
    extra_features = [f for f in features_df.columns if f not in expected_features]
    if extra_features:
        logger.info(f"Extra features (nepoužijú sa): {extra_features[:5]}...")


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Predpočíta features pre simuláciu tradingu")
    parser.add_argument("--config", required=True, help="Cesta k JSON config súboru")
    parser.add_argument("--raw-data-dir", required=True, help="Adresár so surovými dátami")
    parser.add_argument("--output-dir", required=True, help="Výstupný adresár pre features")
    parser.add_argument("--start", required=True, help="Začiatočný dátum (YYYY-MM-DD)")
    parser.add_argument("--end", required=True, help="Koncový dátum (YYYY-MM-DD)")
    
    args = parser.parse_args()
    
    precompute_features_for_simulation(
        config_path=args.config,
        raw_data_dir=args.raw_data_dir,
        output_dir=args.output_dir,
        start_date=args.start,
        end_date=args.end
    )