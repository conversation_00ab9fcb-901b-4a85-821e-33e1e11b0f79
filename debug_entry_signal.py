#!/usr/bin/env python3

import pandas as pd
import numpy as np
import sys
import os
sys.path.append('.')

import json

def debug_entry_signal():
    # Load the exact data around the problematic trade
    parquet_file = "/Users/<USER>/Projects/scalpel_new/parquet_processed/XRPUSDC/1s/2025-07-02.parquet"
    df = pd.read_parquet(parquet_file)
    df['datetime'] = pd.to_datetime(df['timestamp'], unit='s')
    
    # Focus on the exact entry time
    target_time = pd.Timestamp('2025-07-02 01:17:42', tz='UTC')
    
    # Find the exact row
    entry_row = df[df['datetime'] == target_time]
    if entry_row.empty:
        print(f"ERROR: No data found for {target_time}")
        return
        
    print("=== DEBUG: Entry Signal Analysis ===")
    print(f"Target time: {target_time}")
    
    # Load strategy config to get thresholds
    with open('temp_config_1s.json', 'r') as f:
        config = json.load(f)
    
    # Extract entry thresholds
    long_entry_thr = config.get('longEntryThreshold', 0.5)
    short_entry_thr = config.get('shortEntryThreshold', 0.5)
    
    print(f"Long entry threshold: {long_entry_thr}")
    print(f"Short entry threshold: {short_entry_thr}")
    
    # Get the entry signal value
    entry_row_data = entry_row.iloc[0]
    
    # Check if there's an entry_sig column
    if 'entry_sig' in entry_row_data:
        entry_sig = entry_row_data['entry_sig']
        print(f"entry_sig: {entry_sig}")
        
        # Determine triggered_pos based on the logic from simulate_trading_new.py
        triggered_pos = 0
        if entry_sig > long_entry_thr:
            triggered_pos = 1
            print(f"→ LONG triggered (entry_sig {entry_sig} > long_thr {long_entry_thr})")
        elif entry_sig < -short_entry_thr:
            triggered_pos = -1
            print(f"→ SHORT triggered (entry_sig {entry_sig} < -short_thr {-short_entry_thr})")
        else:
            print(f"→ NO TRIGGER (entry_sig {entry_sig} not meeting thresholds)")
            
        print(f"triggered_pos: {triggered_pos}")
        
        # Show what SL calculation would be
        entry_price = entry_row_data['close']
        min_sl_dist_points = 0.00933  # From our previous debug
        
        if triggered_pos == 1:
            sl_price = entry_price - min_sl_dist_points
            print(f"LONG SL: {entry_price:.5f} - {min_sl_dist_points:.5f} = {sl_price:.5f}")
        elif triggered_pos == -1:
            sl_price = entry_price + min_sl_dist_points
            print(f"SHORT SL: {entry_price:.5f} + {min_sl_dist_points:.5f} = {sl_price:.5f}")
            
        # Compare with known values
        known_sl = 2.15666
        print(f"Known SL trigger: {known_sl:.5f}")
        
        if triggered_pos == 1 and abs((entry_price - min_sl_dist_points) - known_sl) < 0.001:
            print("🚨 FOUND THE BUG! The position was triggered as LONG instead of SHORT!")
            print("This explains why SL is below entry - it's calculated for LONG position!")
            
    else:
        print("ERROR: 'entry_sig' column not found in data")
        print("Available columns:", list(entry_row_data.index))

if __name__ == "__main__":
    debug_entry_signal()