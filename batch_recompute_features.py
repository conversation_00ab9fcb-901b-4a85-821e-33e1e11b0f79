#!/usr/bin/env python3
"""
Batch script na prepočítanie features pre všetky potrebné obdobia.
Automaticky detekuje dostupné dáta a prepočíta features s plnou históriou.
"""

import subprocess
import logging
import json
from pathlib import Path
from datetime import datetime, timedelta
from typing import List, Tuple
import argparse

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def find_all_available_dates(raw_data_dir: str, timeframe: str) -> List[str]:
    """Nájde všetky dostupné dátumy v raw data."""
    ohlcv_dir = Path(raw_data_dir) / "ohlcv" / timeframe
    if not ohlcv_dir.exists():
        return []
    
    dates = []
    for file in ohlcv_dir.glob("*.parquet"):
        try:
            date_str = file.stem
            datetime.strptime(date_str, "%Y-%m-%d")
            dates.append(date_str)
        except ValueError:
            continue
    
    return sorted(dates)

def group_dates_into_ranges(dates: List[str], max_range_days: int = 30) -> List[Tuple[str, str]]:
    """Zoskupí dátumy do rozsahov pre efektívne spracovanie."""
    if not dates:
        return []
    
    ranges = []
    current_start = dates[0]
    current_end = dates[0]
    
    for i in range(1, len(dates)):
        current_date = datetime.strptime(dates[i], "%Y-%m-%d")
        range_start_date = datetime.strptime(current_start, "%Y-%m-%d")
        
        # Ak je rozdiel menší ako max_range_days, pokračuj v rozsahu
        if (current_date - range_start_date).days < max_range_days:
            current_end = dates[i]
        else:
            # Ukonči aktuálny rozsah a začni nový
            ranges.append((current_start, current_end))
            current_start = dates[i]
            current_end = dates[i]
    
    # Pridaj posledný rozsah
    ranges.append((current_start, current_end))
    
    return ranges

def run_recompute_command(config_path: str, raw_data_dir: str, output_dir: str,
                         start_date: str, end_date: str, lookback_days: int = None) -> bool:
    """Spustí recompute_all_features.py pre daný rozsah."""
    
    cmd = [
        "python", "recompute_all_features.py",
        "--config", config_path,
        "--raw-data-dir", raw_data_dir,
        "--output-dir", output_dir,
        "--start", start_date,
        "--end", end_date
    ]

    # Pridaj lookback-days len ak je špecifikovaný
    if lookback_days is not None:
        cmd.extend(["--lookback-days", str(lookback_days)])
    
    logger.info(f"🔄 Spúšťam: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=3600)  # 1 hour timeout
        
        if result.returncode == 0:
            logger.info(f"✅ Úspešne dokončené: {start_date} až {end_date}")
            # Log posledných pár riadkov výstupu
            output_lines = result.stdout.strip().split('\n')
            for line in output_lines[-3:]:
                if line.strip():
                    logger.info(f"   {line}")
            return True
        else:
            logger.error(f"❌ Chyba pri spracovaní {start_date} až {end_date}")
            logger.error(f"   Return code: {result.returncode}")
            logger.error(f"   STDERR: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        logger.error(f"❌ Timeout pri spracovaní {start_date} až {end_date}")
        return False
    except Exception as e:
        logger.error(f"❌ Neočakávaná chyba: {e}")
        return False

def batch_recompute_all_features(config_path: str, raw_data_dir: str, output_dir: str,
                                max_range_days: int = 30, lookback_days: int = None):
    """
    Batch prepočítanie features pre všetky dostupné dáta.
    
    Args:
        config_path: Cesta k config súboru
        raw_data_dir: Adresár so surovými dátami
        output_dir: Výstupný adresár
        max_range_days: Maximálny rozsah dní na jedno spustenie
        lookback_days: Počet dní histórie pre indikátory
    """
    logger.info("🚀 Spúšťam batch prepočítanie features")
    
    # Načítanie konfigurácie pre zistenie timeframe
    with open(config_path, 'r') as f:
        config = json.load(f)
    
    symbol = config["symbol"]
    primary_tf = config["primaryTimeframe"]
    
    logger.info(f"   Symbol: {symbol}")
    logger.info(f"   Timeframe: {primary_tf}")
    logger.info(f"   Max range: {max_range_days} dní")
    if lookback_days:
        logger.info(f"   Manuálny lookback: {lookback_days} dní")
    else:
        logger.info(f"   Automatický lookback na základe indikátorov")
    
    # Nájdenie všetkých dostupných dátumov
    all_dates = find_all_available_dates(raw_data_dir, primary_tf)
    
    if not all_dates:
        logger.error(f"❌ Žiadne dáta nájdené v {raw_data_dir}/ohlcv/{primary_tf}")
        return
    
    logger.info(f"📅 Nájdených {len(all_dates)} dní dát: {all_dates[0]} až {all_dates[-1]}")
    
    # Zoskupenie do rozsahov
    date_ranges = group_dates_into_ranges(all_dates, max_range_days)
    
    logger.info(f"📦 Rozdelené do {len(date_ranges)} rozsahov:")
    for i, (start, end) in enumerate(date_ranges, 1):
        days_count = (datetime.strptime(end, "%Y-%m-%d") - datetime.strptime(start, "%Y-%m-%d")).days + 1
        logger.info(f"   {i}. {start} až {end} ({days_count} dní)")
    
    # Spracovanie každého rozsahu
    successful = 0
    failed = 0
    
    for i, (start_date, end_date) in enumerate(date_ranges, 1):
        logger.info(f"\n📊 Spracovávam rozsah {i}/{len(date_ranges)}: {start_date} až {end_date}")
        
        success = run_recompute_command(
            config_path, raw_data_dir, output_dir, 
            start_date, end_date, lookback_days
        )
        
        if success:
            successful += 1
        else:
            failed += 1
            logger.error(f"❌ Zlyhalo spracovanie rozsahu {start_date} až {end_date}")
    
    # Súhrn
    logger.info(f"\n🎉 BATCH PREPOČÍTANIE DOKONČENÉ!")
    logger.info(f"   ✅ Úspešné: {successful}/{len(date_ranges)}")
    logger.info(f"   ❌ Neúspešné: {failed}/{len(date_ranges)}")
    
    if failed > 0:
        logger.warning(f"⚠️  {failed} rozsahov zlyhalo - skontroluj logy vyššie")
    else:
        logger.info("🎊 Všetky rozsahy úspešne spracované!")

def main():
    parser = argparse.ArgumentParser(description="Batch prepočítanie features pre všetky dostupné dáta")
    parser.add_argument("--config", required=True, help="Cesta k JSON config súboru")
    parser.add_argument("--raw-data-dir", required=True, help="Adresár so surovými dátami")
    parser.add_argument("--output-dir", required=True, help="Výstupný adresár pre features")
    parser.add_argument("--max-range-days", type=int, default=30, help="Max dní na jedno spustenie (default: 30)")
    parser.add_argument("--lookback-days", type=int, default=None, help="Počet dní histórie (default: automatický výpočet)")
    
    # Predefined configs pre rýchle spustenie
    parser.add_argument("--preset", choices=["1s", "5m", "both"], help="Prednastavené konfigurácie")
    
    args = parser.parse_args()
    
    if args.preset:
        configs = []
        if args.preset in ["1s", "both"]:
            configs.append("strategyConfig_scalp_1s_fixed.json")
        if args.preset in ["5m", "both"]:
            configs.append("strategyConfig_scalp_5m_fixed.json")
        
        for config in configs:
            if Path(config).exists():
                logger.info(f"\n{'='*60}")
                logger.info(f"🔧 Spracovávam konfiguráciu: {config}")
                logger.info(f"{'='*60}")
                
                batch_recompute_all_features(
                    config_path=config,
                    raw_data_dir=args.raw_data_dir,
                    output_dir=args.output_dir,
                    max_range_days=args.max_range_days,
                    lookback_days=args.lookback_days
                )
            else:
                logger.error(f"❌ Konfigurácia neexistuje: {config}")
    else:
        batch_recompute_all_features(
            config_path=args.config,
            raw_data_dir=args.raw_data_dir,
            output_dir=args.output_dir,
            max_range_days=args.max_range_days,
            lookback_days=args.lookback_days
        )

if __name__ == "__main__":
    main()
