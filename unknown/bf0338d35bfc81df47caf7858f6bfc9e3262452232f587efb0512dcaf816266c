from __future__ import annotations

import json
import logging
import multiprocessing as mp
import os
import sys
import time
from contextlib import contextmanager
from datetime import datetime, timedelta, timezone
from pathlib import Path
from typing import Any, Callable, Dict, List, Tuple

import gymnasium as gym  # environment API
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.nn.utils as nn_utils
from tqdm.auto import tqdm

from stable_baselines3 import SAC
from stable_baselines3.common.callbacks import (
    BaseCallback,
    CheckpointCallback,
    EvalCallback,
)
from stable_baselines3.common.env_util import make_vec_env
from stable_baselines3.common.torch_layers import BaseFeaturesExtractor
from stable_baselines3.common.vec_env import DummyVecEnv, VecNormalize
from stable_baselines3.common.buffers import ReplayBuffer

# ----------------------------------------------------------------------------
#  ENVIRONMENT
# ----------------------------------------------------------------------------

# Set thread count for worker processes
# Each worker gets 1 thread for optimal parallelism
torch.set_num_threads(1)  # 1 thread per worker process

# Also set environment variables to ensure BLAS libraries use single-threaded mode
for v in ("OMP_NUM_THREADS", "OPENBLAS_NUM_THREADS", "MKL_NUM_THREADS", "NUMEXPR_NUM_THREADS"):
    os.environ[v] = "1"

class ScalpingEnv(gym.Env):
    """High‑frequency scalping environment with optimizations to reduce micro-trading.

    Key improvements:
    1. Soft cost penalty for each trade to discourage excessive micro-trades
    2. More aggressive drawdown stopping at 0.5% instead of 25%
    3. Enhanced Sharpe monitoring for better performance tracking
    """

    metadata = {"render_modes": []}

    _FRAME_MIN, _FRAME_MAX = -5.0, 5.0
    _PNL_NORM_DENOM = 0.01
    _MIN_PRICE = 1e-6
    
    # Time-of-day blackout: disallow trading during low-liquidity hours (UTC)
    DISALLOWED_HOURS = {0, 1, 2, 3}  # 00:00-03:59 UTC

    def __init__(self, df: pd.DataFrame, cfg: Dict[str, Any]):
        super().__init__()
        self.cfg = cfg
        self.debug = bool(cfg.get("debug", False))
        self.look = cfg["envSettings"]["state_lookback"]
        self.cols = cfg["envSettings"]["feature_columns"]
        self.max_ep_len = cfg["envSettings"].get("max_ep_len", 3_000)
        self.inactivity_lim = cfg["envSettings"].get("inactivity_limit", 600)

        # Initialize total equity that persists across episodes
        self.equity_total = cfg["account"]["initialEquity"]
        # Initialize max_equity to track maximum equity across episodes
        self.max_equity = self.equity_total

        # Trade cost penalty configuration (optimization #1)
        self.trade_cost_penalty = cfg["trainingSettings"]["rewardStructure"].get("tradeCostPenalty", 0.05)

        # The dataset is already at 1-second intervals
        # We'll iterate every 1 second but recalculate 5-minute metrics only when needed

        # raw data
        self.df = df.copy()  # Make a copy to avoid SettingWithCopyWarning
        self._mat = df[self.cols].astype(np.float32).to_numpy(copy=False)
        self._price = df["close"].astype(np.float32).to_numpy(copy=False)
        if not np.isfinite(self._mat).all():
            raise ValueError("NaN/Inf in input data – check parquet preprocessing")

        # Create a 5-minute period identifier for each row
        # This will help us determine when we've reached a new 5-minute period
        self.df['minute_5'] = self.df.index.floor('5min')
        self.current_5min = None  # Track the current 5-minute period

        # trailing‑SL helpers --------------------------------------------------
        tsl = cfg.get("trailingStopLoss", {})
        self.tsl_enabled = tsl.get("enabled", False)
        self.tsl_activate_mult = tsl.get("activateATRMultiplier", 1.0)  # Changed from 0.2 to 1.0
        self.tsl_trail_mult = tsl.get("trailATRMultiplier", 0.8)  # Changed from 0.0 to 0.8
        # Dynamically find ATR configuration instead of hardcoding timeframe
        atr_config = None
        for key, config in cfg['indicatorSettings'].items():
            if key.startswith('atr_') and isinstance(config, dict) and config.get('enabled', True):
                atr_config = config
                atr_timeframe = key  # e.g., 'atr_1s' or 'atr_5m'
                break
        
        if atr_config:
            self.atr_name = f"ATR_{atr_config['basePeriod']}"
        else:
            # Fallback if no ATR config found
            self.atr_name = "ATR_14"
            logging.warning(f"No ATR config found, using fallback: {self.atr_name}")
        if self.tsl_enabled and self.atr_name in df.columns:
            self.atr = df[self.atr_name].astype(np.float32).to_numpy(copy=False)
        else:
            self.tsl_enabled = False

        # gym spaces ---------------------------------------------------------
        self.action_space = gym.spaces.Box(low=-1.0, high=1.0, shape=(4,), dtype=np.float32)
        obs_len = self.look * len(self.cols) + 11  # 7 original + 4 time features
        self.observation_space = gym.spaces.Box(
            low=self._FRAME_MIN, high=self._FRAME_MAX, shape=(obs_len,), dtype=np.float32
        )

        # trade parameters ---------------------------------------------------
        tp = cfg["tradeParams"]
        self.entry_thr = tp["entryActionThreshold"]
        self.exit_thr = tp["exitActionThreshold"]
        self.slip = tp["slippagePercentage"] / 100.0
        self.fee = tp["feePercentage"] / 100.0
        self.rr_target = tp.get("rrTarget", 2.0)  # Default risk-reward target of 2.0
        self.rew = cfg["trainingSettings"]["rewardStructure"]

        # bookkeeping --------------------------------------------------------
        self._reset_account()
        self.idx = self.start_idx_ep = self.inactivity_cnt = 0

    # ---------------- internal helpers -------------------------------------
    def _is_allowed_now(self) -> bool:
        """Check if trading is allowed at current time (respects blackout hours)."""
        cur_hour = self.df.index[self.idx].hour
        return cur_hour not in self.DISALLOWED_HOURS and self.trading_allowed
    
    def _log_event(self, kind: str, extra: Dict[str, Any] | None = None) -> None:
        if extra is None:
            extra = {}
        # Update max_equity based on total equity
        self.max_equity = max(self.max_equity, self.equity_total)
        drawdown_pct = 0.0 if self.max_equity == 0 else (
            (self.max_equity - self.equity_total) / self.max_equity * 100
        )
        self.event_log.append(
            {
                "ts": str(self.df.index[self.idx]),
                "idx": int(self.idx),
                "kind": kind,
                "price": float(self._price[self.idx]),
                "pos": int(self.pos),
                "size": float(self.size),
                "eq": float(self.equity_total),  # Use total equity that persists across episodes
                "equity_ep": float(self.equity_ep),  # Episode-specific equity
                "max_equity": float(self.max_equity),
                "drawdown_pct": float(drawdown_pct),
                **extra,
            }
        )

    def _reset_account(self) -> None:
        # Only reset episode-specific state, not total equity
        self.equity_ep = 0.0  # Episode-specific equity (PnL)
        self.pos = 0
        self.entry = self.size = 0.0
        self.sl = self.tp = np.nan
        self.cooldown_cnt = self.open_idx = 0
        self.trailing_active = False
        self.entry_atr = 0.0
        # Don't reset max_equity or equity_total here
        self.daily_pnl = self.daily_risk_used = 0.0
        self.daily_trade_count = 0
        self.last_day = None
        self.trading_allowed = True
        self.event_log: List[Dict[str, Any]] = []

    # ---------------- observation -----------------------------------------
    def _state(self) -> np.ndarray:
        s0 = max(0, self.idx - self.look + 1)
        frame = self._mat[s0 : self.idx + 1]
        if frame.shape[0] < self.look:
            pad = np.zeros((self.look - frame.shape[0], frame.shape[1]), np.float32)
            frame = np.vstack((pad, frame))
        frame = np.clip(frame, self._FRAME_MIN, self._FRAME_MAX).flatten()

        price = max(self._price[self.idx], self._MIN_PRICE)

        pnl_norm = (
            np.tanh(((price - self.entry) * self.pos) / (price * self._PNL_NORM_DENOM))
            if self.pos
            else 0.0
        )
        trade_duration = 0.0
        if self.pos and self.open_idx >= 0:  # Ensure open_idx is valid
            trade_duration = np.tanh(max(0, self.idx - self.open_idx) / (self.inactivity_lim * 0.5))
        profit_status = np.sign((price - self.entry) * self.pos) if self.pos else 0.0

        tp_distance = sl_distance = 0.0
        if self.pos and np.isfinite(self.tp) and np.isfinite(self.sl):
            tp_distance = (
                np.tanh(abs(self.tp - price) / (price * 0.01))
                * np.sign(self.tp - price)
                * self.pos
            )
            sl_distance = (
                np.tanh(abs(self.sl - price) / (price * 0.01))
                * np.sign(self.sl - price)
                * self.pos
            )

        pnl_atr_ratio = 0.0
        if self.pos and self.tsl_enabled:
            atr_val = self.atr[self.idx]
            if atr_val > 0:
                pnl_atr_ratio = np.tanh(((price - self.entry) * self.pos) / atr_val)

        # Add time features for periodicity awareness
        cur_hour = self.df.index[self.idx].hour
        dow = self.df.index[self.idx].dayofweek
        time_feats = [
            np.sin(cur_hour / 24 * 2 * np.pi),  # Hour sine
            np.cos(cur_hour / 24 * 2 * np.pi),  # Hour cosine
            np.sin(dow / 7 * 2 * np.pi),        # Day-of-week sine
            np.cos(dow / 7 * 2 * np.pi),        # Day-of-week cosine
        ]

        return np.concatenate(
            (
                frame,
                [
                    float(self.pos),
                    pnl_norm,
                    trade_duration,
                    profit_status,
                    tp_distance,
                    sl_distance,
                    pnl_atr_ratio,
                ],
                time_feats,
            )
        ).astype(np.float32)

    # ------------- CLOSE helpers ------------------------------------------
    def _close(self, price_exec: float, reason: str) -> float:
        pnl  = (price_exec - self.entry) * self.pos * self.size
        fee  = (self.entry + price_exec) * self.size * self.fee
        
        # Optimization #1: Proportional cost penalty to discourage micro-trading
        trade_cost = self.size * self.trade_cost_penalty  # Proportional to position size
        net  = pnl - fee - trade_cost

        # Store equity before update for reward calculation
        equity_before = self.equity_total

        # Update both episode-specific and total equity
        self.equity_ep += net
        self.equity_total += net

        # Daily risk tracking
        initial_risk = abs(self.entry - self.sl) or 1e-9
        r_mult = net / (initial_risk * self.size)
        self.daily_risk_used += r_mult
        self.daily_pnl       += net

        # === SLIPPAGE A LATENCY LOGGING ===
        current_price = max(self._price[self.idx], self._MIN_PRICE)
        expected_price = current_price  # Cena bez slippage
        
        # Vypočítaj očakávané PnL bez slippage
        expected_pnl = (expected_price - self.entry) * self.pos * self.size
        expected_fee = (self.entry + expected_price) * self.size * self.fee
        expected_net = expected_pnl - expected_fee - trade_cost  # Include trade cost in expected calculation
        
        # Slippage impact
        slippage_impact = net - expected_net
        slippage_pct = abs(price_exec - expected_price) / expected_price * 100
        
        # Trade duration (latency proxy)
        trade_duration = self.idx - self.open_idx if self.open_idx >= 0 else 0
        
        # Špeciálne kontroly pre TP
        tp_slippage_warning = False
        if reason == "TP" and slippage_impact < -0.001:  # Stratil viac ako 0.1 cent na slippage pri TP
            tp_slippage_warning = True

        # Log the event with equity information + slippage details
        self._log_event(f"CLOSE_{reason}", {
            "pnl": pnl, "fee": fee, "trade_cost": trade_cost, "net": net, "r_mult": r_mult,
            "same_bar": self.idx == self.open_idx,
            "daily_risk_used": self.daily_risk_used,
            "equity_before": equity_before,
            "equity_after": self.equity_total,
            "equity_diff": net,
            "equity_diff_pct": (net / max(equity_before, 1e-6)) * 100,
            "equity_total": self.equity_total,
            "equity_ep": self.equity_ep,
            # Nové slippage/latency metriky
            "price_exec": price_exec,
            "price_current": current_price,
            "expected_net": expected_net,
            "slippage_impact": slippage_impact,
            "slippage_pct": slippage_pct,
            "trade_duration_s": trade_duration,
            "tp_slippage_warning": tp_slippage_warning
        })

        # Enhanced micro-profit filtering with configurable threshold
        min_trade_threshold = self.cfg["trainingSettings"]["rewardStructure"].get("minTradeThreshold", 0.02)
        micro_trade_penalty = self.cfg["trainingSettings"]["rewardStructure"].get("microTradePenalty", -1.0)
        
        if abs(net) < min_trade_threshold:
            # Strong penalty for micro-trades to discourage spam
            reward = micro_trade_penalty * (1.0 - abs(net) / min_trade_threshold)
            self._log_event("MICRO_TRADE_PENALTY", {
                "net": net,
                "penalty_reward": reward,
                "threshold": min_trade_threshold
            })
        else:
            # Log-return reward in basis points - unified scale for PopArt
            reward = np.clip(
                np.sign(net) * np.log1p(abs(net) / max(equity_before, 1e-6)) * 1e4,
                -25, 25
            )
        # Fees and trade cost already in net, no extra penalties needed

        self.pos = 0
        return reward

    def _force_close(self) -> float:
        if self.pos == 0: return 0.0
        px = max(self._price[self.idx], self._MIN_PRICE)
        px_exec = px * (1 - self.slip) if self.pos == 1 else px * (1 + self.slip)
        return self._close(px_exec, "FORCE")

    # ───────────────────────── gym API ─────────────────────────────────────
    def reset(self, *, seed=None, options=None):
        super().reset(seed=seed)
        
        # Safer start index selection for short datasets
        if len(self._price) <= self.look + 1:
            # dataset je veľmi krátky – začni od 0 a zahoď náhodný výber
            self.start_idx_ep = 0
        else:
            max_start = min(len(self._price) - 1,  # nedovoľ preskočiť koniec
                            len(self._price) - self.max_ep_len - 1)
            if max_start <= self.look:
                self.start_idx_ep = self.look
            else:
                self.start_idx_ep = self.np_random.integers(self.look, max_start)
        self.idx = self.start_idx_ep
        self.inactivity_cnt = 0

        # Initialize 5-minute period tracking
        self.current_5min = self.df.loc[self.df.index[self.idx], 'minute_5']
        self.last_5min_update = self.idx

        # Save the event log before resetting
        last = self.event_log.copy()

        # Reset account - this will reset episode-specific state but not equity_total
        self._reset_account()

        # Only update max_equity at the start of a new episode if it's the first step
        # This ensures we track the true maximum across all episodes
        self.max_equity = max(self.max_equity, self.equity_total)

        # Set equity to equity_total for compatibility with existing code
        self.equity = self.equity_total

        return self._state(), {"last_episode_log": last}

    # ─────────────────────────── STEP ──────────────────────────────────────
    def step(self, action: np.ndarray
             ) -> Tuple[np.ndarray, float, bool, bool, Dict[str, Any]]:
        if self.debug:
            assert action.shape == (4,), f"action shape {action.shape} != (4,)"
        entry_sig, a_sl, a_tp, exit_sig = np.clip(action, -1, 1)

        reward: float = 0.0
        done:   bool  = False
        info: Dict[str, Any] = {}
        price = max(self._price[self.idx], self._MIN_PRICE)

        # ───────── Trailing‑SL update ───────────────────────────────────
        if self.pos and self.tsl_enabled:
            self._update_trailing_sl(price)

        # ───────── EXIT logika ──────────────────────────────────────────
        if self.pos:
            reward += self._maybe_exit(price, exit_sig, info)

        # ───────── ENTRY logika ─────────────────────────────────────────
        if self.pos == 0:
            reward += self._maybe_enter(price, entry_sig, a_sl, a_tp, info)

        # ───────── HOLD / FLAT odmeny/penalty ──────────────────────────
        reward += self._hold_flat_rewards(price)

        # --- Trailing SL posun (bez logu) ------------------------------
        if self.pos and self.tsl_enabled:
            self._apply_simple_trailing(price)

        # housekeeping --------------------------------------------------
        self._post_step_updates()

        # koniec epizódy? ----------------------------------------------
        reward, done, info = self._episode_end_checks(reward, info)

        # Remove manual reward clipping - let PopArt handle normalization
        reward_no_clip = reward
        reward = float(reward)  # No manual clipping - PopArt handles this

        # Store raw reward in info dict instead of trying to log it directly
        # This will be accessible to callbacks
        info["raw_reward"] = reward_no_clip

        return self._state(), reward, done, False, info

    # ───────────────────────── členité vnútorné metódy ────────────────────
    def _update_trailing_sl(self, price: float) -> None:
        atr_now = self.atr[self.idx]
        profit  = (price - self.entry) * self.pos
        # Calculate R-multiple of current profit
        initial_risk = abs(self.entry - self.sl) or 1e-9
        r_mult = profit / initial_risk
        # Activate trailing when profit reaches self.tsl_activate_mult R (default: 1.0R)
        if not self.trailing_active and r_mult >= self.tsl_activate_mult:
            self.trailing_active = True
            self.sl = price - self.tsl_trail_mult*atr_now if self.pos == 1 \
                      else price + self.tsl_trail_mult*atr_now
            self._log_event("TRAIL_ACTIVATE", {"sl": self.sl})
        elif self.trailing_active:
            new_sl = (price - self.tsl_trail_mult*atr_now
                      if self.pos == 1 else price + self.tsl_trail_mult*atr_now)
            if (self.pos == 1 and new_sl > self.sl) or (self.pos == -1 and new_sl < self.sl):
                self.sl = new_sl
                self._log_event("TRAIL_UPDATE", {"sl": self.sl})

    def _maybe_exit(self, price: float, exit_sig: float, info: Dict[str, Any]) -> float:
        # Access high/low directly from the dataframe to avoid errors when they're not in feature_columns
        hi = self.df["high"].iat[self.idx]
        lo = self.df["low"].iat[self.idx]
        hit_sl = lo <= self.sl if self.pos == 1 else hi >= self.sl
        hit_tp = hi >= self.tp if self.pos == 1 else lo <= self.tp
        if hit_sl and hit_tp: hit_tp = False                # SL pred TP
        exit_sig_ok = ((exit_sig >  self.exit_thr and self.pos == 1) or
                       (exit_sig < -self.exit_thr and self.pos == -1))

        # Soft-TP: if profit > 1.5*ATR and exit signal is still strong, take profit
        soft_tp_triggered = False
        if self.pos and self.tsl_enabled and exit_sig_ok:
            soft_tp_atr = 1.5
            profit = (price - self.entry) * self.pos
            atr_threshold = soft_tp_atr * self.atr[self.idx]
            if profit > atr_threshold:
                soft_tp_triggered = True
                self._log_event("SOFT_TP_TRIGGERED", {
                    "profit": profit,
                    "atr_threshold": atr_threshold,
                    "exit_sig": exit_sig
                })

        # Implement minimum time in trade (60 seconds) - enhanced anti-micro-trading
        min_duration = self.cfg["tradeParams"].get("minTradeDurationSeconds", 60)
        if self.pos and (self.idx - self.open_idx) < min_duration:
            if exit_sig_ok and not soft_tp_triggered:  # Allow soft-TP to override min time
                self._log_event("MIN_TIME_HOLD", {"time_in_trade": self.idx - self.open_idx, "required": min_duration})
                exit_sig_ok = False

        if not (hit_sl or hit_tp or exit_sig_ok or soft_tp_triggered): return 0.0

        px_exec = self.sl if hit_sl else self.tp if hit_tp else price
        px_exec *= (1 - self.slip) if self.pos == 1 else (1 + self.slip)

        # Determine if this is a profitable or losing exit when using signal
        is_profit = (px_exec - self.entry) * self.pos > 0

        # Tag the exit appropriately - this ensures proper profit factor calculation
        if hit_sl:
            reason = "SL"
        elif hit_tp:
            reason = "TP"
        elif soft_tp_triggered:
            reason = "SOFT_TP"  # New reason for soft take profit
        else:  # exit_sig_ok
            # Tag signal exits as TP or SL based on profitability
            reason = "TP_SIG" if is_profit else "SL_SIG"
        r = self._close(px_exec, reason)
        self.inactivity_cnt = 0
        
        # Add cooloff after SL
        if reason.startswith("SL"):
            self.cooldown_cnt = 60  # 1 minute cooloff after stop loss
            
        info["trade_event"] = self.event_log[-1]
        return r

    def _maybe_enter(self, price: float, entry_sig: float,
                     a_sl: float, a_tp: float, info: Dict[str, Any]) -> float:
        if self.cooldown_cnt or not self.trading_allowed: return 0.0
        
        # Early exit: time-of-day blackout filter
        if not self._is_allowed_now():
            return 0.0
        
        # Early exit: spread filter - don't trade if spread > 0.1% of price
        if "spread" in self.cols:
            spread = self._mat[self.idx, self.cols.index("spread")]
            if spread > price * 0.001:  # spread > 0.1%
                return 0.0
        
        # Early exit: volatility filter - don't trade if ATR < 0.2% of price
        if self.tsl_enabled:
            atr_now = self.atr[self.idx]
            if atr_now < price * 0.002:  # ATR < 0.2%
                return 0.0
        
        # NEW: Minimum expected profit filter
        min_trade_value = self.cfg["tradeParams"].get("minTradeValue", 0.02)
        expected_profit = abs(entry_sig) * price * 0.01  # Estimated profit based on signal strength
        if expected_profit < min_trade_value:
            self._log_event("MIN_PROFIT_FILTER", {
                "expected_profit": expected_profit,
                "min_required": min_trade_value,
                "signal_strength": abs(entry_sig)
            })
            return 0.0
        
        dir_ = 1 if entry_sig > self.entry_thr else -1 if entry_sig < -self.entry_thr else 0
        if dir_ == 0: return 0.0
        self.pos = dir_
        self.trailing_active = False
        if self.tsl_enabled: self.entry_atr = self.atr[self.idx]
        self.entry = price*(1+self.slip) if dir_==1 else price*(1-self.slip)

        atr = (self.atr[self.idx] if self.tsl_enabled
               else price*0.01)

        # Get spread from the data if available, otherwise use a default value
        spread = self._mat[self.idx, self.cols.index("spread")] if "spread" in self.cols else price * 0.0005

        # Ensure spread is positive and reasonable
        spread = max(price * 0.0001, min(spread, price * 0.005))

        # --- ATR a percentuálne minimum ------------------------------------
        atr_now = atr  # Use the already calculated ATR value

        # 1) minimálna vzdialenosť SL v % z ceny
        sl_pct_min = self.entry * self.cfg["tradeParams"]["minSLDistancePercent"]/100

        # 2) SL musí byť aspoň X % z ceny vo forme ATR
        min_atr_pct = self.cfg["tradeParams"].get("minAtrPercentOfPrice", 0.0) / 100
        sl_min_by_atr_pct = self.entry * min_atr_pct  # Percentage of price, not ATR

        # 3) SL musí mať minimálne N-násobok ATR (absolútny násobok)
        min_sl_atr_mult = self.cfg["tradeParams"].get("minSLDistanceATR", 0.0)
        sl_min_by_atr_mult = atr_now * min_sl_atr_mult

        # 4) ATR-based SL distance with action adjustment
        atr_sl = atr * (0.5 + ((a_sl+1)/2) * 2.5)

        # 5) spread + pip ako predtým
        min_pip = price * 0.0001
        spread_safe = max(price * 0.0001, min(spread, price * 0.005))

        # finálny výber - use the maximum of all constraints
        # Note: sl_min_by_atr_mult already handles ATR-based SL, so atr_sl is redundant
        # but we keep it for backward compatibility
        sl_pts = max(sl_pct_min, sl_min_by_atr_pct, sl_min_by_atr_mult,
                     atr_sl, spread_safe * 1.5, min_pip)

        # --- TP podľa konfigurovaného R:R  ---------------------------------
        # Get the risk-reward target from config (default 2.0)
        rr_target = self.cfg["tradeParams"].get("rrTarget", 2.0)

        # Calculate TP based on SL with configurable risk-reward target and action adjustment
        tp_raw = rr_target + a_tp          # a_tp ∈ [-1,1]
        tp_raw = max(tp_raw, 1.0)  # Ensure minimum 1R targets (RR targeting optimization)
        tp_raw = np.clip(tp_raw, 1.0, 4.0)  # Allow agent to choose TP between 1.0-4.0 R
        tp_pts = sl_pts * tp_raw

        self.sl = self.entry - sl_pts if dir_==1 else self.entry + sl_pts
        self.tp = self.entry + tp_pts if dir_==1 else self.entry - tp_pts

        # Calculate risk-reward ratio for logging
        risk_reward_ratio = tp_pts / sl_pts

        # Position sizing with risk management
        # Increase risk percentage based on signal strength for higher confidence trades
        signal_strength = abs(entry_sig)
        risk_multiplier = 1.0 + (signal_strength - self.entry_thr) * 2  # Scale up to 2x for strong signals
        base_risk_pct = self.cfg["riskManagement"]["riskPerTradePercentage"]
        # Use equity_total that persists across episodes for position sizing
        risk_eur = self.equity_total * base_risk_pct/100 * risk_multiplier

        # Adjust position size based on order book imbalance
        imb = self._mat[self.idx, self.cols.index("depth_imbalance5")]
        size_boost = 1.5 if abs(imb) > 0.3 else 1.0

        # Calculate position size based on risk per trade
        calc_size = risk_eur / sl_pts * size_boost

        # Updated minimum position sizing for spam trade reduction (optimization #3)
        min_size_risk_based = 0.05 * self.equity_total / sl_pts  # 5% of equity divided by SL points
        
        # Calculate minimum position size based on spread and fee to ensure profitability
        # Ensure fees don't eat more than 50% of expected profit
        fee_cost = price * self.fee * 2  # Entry and exit fee
        min_fee_coverage = fee_cost * 2  # Ensure fees are at most 50% of expected profit
        min_size_fee = max(1e-3, min_fee_coverage / sl_pts * 1.5)
        
        # Take the maximum of both minimum size constraints
        min_size = max(min_size_risk_based, min_size_fee)

        # Enforce maximum position size as percentage of equity_total
        max_size = self.equity_total * self.cfg["riskManagement"]["maxPositionSizePercentEquity"]/100

        # Apply all constraints - use the calculated size but respect max size
        self.size = np.clip(calc_size, min_size, max_size)

        # Log the risk parameters for analysis
        self._log_event("RISK_PARAMS", {
            "risk_pct": base_risk_pct * risk_multiplier,
            "risk_eur": risk_eur,
            "min_size": min_size,
            "fee_impact": fee_cost / (sl_pts * min_size) * 100  # Fee as % of risk
        })

        if calc_size > max_size:
            self._log_event("SIZE_LIMITED", {"calc": calc_size, "max": max_size})

        # Log risk-reward ratio for analysis (now using configurable target)
        self._log_event("RISK_REWARD", {"ratio": risk_reward_ratio, "sl_pts": sl_pts, "tp_pts": tp_pts, "rr_base": rr_target})

        self.open_idx = self.idx
        self.daily_trade_count += 1  # Increment daily trade counter
        self._log_event("OPEN", {"dir":"LONG" if dir_==1 else "SHORT",
                                 "entry":self.entry,"sl":self.sl,"tp":self.tp,
                                 "size":self.size,"current_equity":self.equity_total,
                                 "equity_total":self.equity_total,"equity_ep":self.equity_ep,
                                 "daily_trade_count":self.daily_trade_count})
        info["trade_event"] = self.event_log[-1]
        self.inactivity_cnt = 0
        return self.rew["inactivityPenalty"]

    def _hold_flat_rewards(self, price: float) -> float:
        if not self.pos:
            self.inactivity_cnt += 1
            return 0.0

        # Calculate unrealized PnL in percentage points
        unreal = (price - self.entry) * self.pos / (self.entry * 0.01)
        unreal_pct = unreal * 0.01

        # Calculate R-multiple for hold bonus
        initial_risk = abs(self.entry - self.sl) if np.isfinite(self.sl) else self.entry * 0.01
        r_mult = ((price - self.entry) * self.pos) / initial_risk if initial_risk > 0 else 0.0

        # Reward for profitable positions (scaled down to avoid excessive holding)
        r = self.rew["profitableHoldBonus"] * np.tanh(max(unreal, 0)) * 0.5

        # Mini-bonus za dlhší hold: +0.05 každých 30s po 0.3R zisku
        if r_mult > 0.3:  # Po dosiahnutí 0.3R zisku
            trade_duration_seconds = self.idx - self.open_idx
            # Každých 30 sekúnd pridaj bonus +0.05
            hold_bonus_intervals = trade_duration_seconds // 30
            if hold_bonus_intervals > 0:
                hold_bonus = hold_bonus_intervals * 0.05
                r += hold_bonus
                # Log pre debugging (občas)
                if trade_duration_seconds % 60 == 0:  # Každú minútu
                    self._log_event("HOLD_BONUS", {
                        "r_mult": r_mult,
                        "duration_s": trade_duration_seconds,
                        "bonus": hold_bonus
                    })

        # Penalty for underwater positions (scaled down to avoid excessive punishment)
        r += self.rew["underwaterPenalty"] * abs(min(unreal_pct, 0)) * 0.5

        # Small constant penalty for holding positions < 15s to prevent spam trading
        trade_duration_seconds = self.idx - self.open_idx  # 1-second intervals
        if trade_duration_seconds < 15:
            r += self.rew["holdPenaltyPerStep"] * 2.0  # Increased penalty for short trades
        else:
            r += self.rew["holdPenaltyPerStep"] * 0.1  # Minimal penalty for normal trades

        # Penalty based on position size to discourage excessive leverage
        # Only apply significant penalty for very large positions
        # Use equity_total directly for more accurate calculation
        size_relative_to_equity = self.size / max(self.equity_total, 1e-6)
        if size_relative_to_equity > 0.1:  # If position is >10% of equity
            r += -0.0005 * abs(self.pos) * self.size

        return r

    def _apply_simple_trailing(self, price: float) -> None:
        if not self.tsl_enabled: return
        atr = self.atr[self.idx]
        new_sl = price - self.tsl_trail_mult*atr if self.pos==1 \
                 else price + self.tsl_trail_mult*atr
        if (self.pos==1 and new_sl>self.sl) or (self.pos==-1 and new_sl<self.sl):
            self.sl = new_sl

    def _post_step_updates(self) -> None:
        if self.cooldown_cnt: self.cooldown_cnt -= 1

        # Update max_equity based on total equity that persists across episodes
        self.max_equity = max(self.max_equity, self.equity_total)

        # Keep equity in sync with equity_total for compatibility with existing code
        self.equity = self.equity_total

        # Daily limits
        cur_day = pd.Timestamp(self.df.index[self.idx]).date()
        if self.last_day is None or cur_day != self.last_day:
            # Reset daily metrics but NOT max_equity (to avoid drawdown NaN)
            self.daily_pnl = self.daily_risk_used = 0.0
            self.daily_trade_count = 0
            self.trading_allowed = True
            self.last_day = cur_day
            self._log_event("NEW_DAY", {"date":str(cur_day)})
        
        # Stop trading if daily risk limit exceeded or too many trades
        atr_now = self.atr[self.idx] if self.tsl_enabled else self._price[self.idx] * 0.01
        if (self.daily_risk_used <= -2.0 or
            self.daily_pnl <= -3 * atr_now or
            self.daily_trade_count >= 300) and self.trading_allowed:
            self.trading_allowed = False
            self._log_event("DAILY_LIMIT_HIT", {
                "risk": self.daily_risk_used,
                "pnl": self.daily_pnl,
                "trade_count": self.daily_trade_count
            })

    def _episode_end_checks(self, reward: float, info: Dict[str, Any]
                            ) -> Tuple[float,bool,Dict[str,Any]]:
        done=False
        # Enhanced drawdown management with episode penalty
        drawdown = (self.max_equity-self.equity_total)/self.max_equity if self.max_equity > 0 else 0.0
        
        # Apply episode penalty for significant drawdowns
        episode_dd_threshold = self.cfg["trainingSettings"]["rewardStructure"].get("drawdownThresholdPercent", 3.0) / 100
        episode_penalty = self.cfg["trainingSettings"]["rewardStructure"].get("episodePenaltyDrawdown", -5.0)
        
        if drawdown >= episode_dd_threshold:
            reward += episode_penalty
            self._log_event("EPISODE_DD_PENALTY", {"dd_pct": drawdown*100, "penalty": episode_penalty})
        
        if drawdown >= 0.25:  # Stop at 25% drawdown
            self._log_event("EQUITY_STOP", {"dd_pct":drawdown*100})
            # Only call _force_close if we're in a position
            if self.pos:
                reward += self._force_close() - 2.0
            else:
                reward -= 2.0
            done=True; info.update(stop_reason="EQUITY_STOP")

        # Simply increment the index by 1 - the data is already at 1-second intervals
        self.idx += 1

        # Check if we've entered a new 5-minute period
        if self.idx < len(self.df):
            new_5min = self.df.loc[self.df.index[self.idx], 'minute_5']
            if new_5min != self.current_5min:
                # We've entered a new 5-minute period
                self._log_event("NEW_5MIN_PERIOD", {"from": str(self.current_5min), "to": str(new_5min)})
                self.current_5min = new_5min
                # Here you would recalculate any 5-minute metrics
                # For example, update ATR or other indicators that should be calculated on 5-minute data

                # Log that we're in a new 5-minute period but still iterating every 1 second
                if self.debug:
                    logging.debug(f"New 5-minute period at index {self.idx}, timestamp {self.df.index[self.idx]}")

        ep_len = self.idx - self.start_idx_ep
        if (ep_len >= self.max_ep_len or
            self.inactivity_cnt >= self.inactivity_lim or
            self.idx >= len(self._price)-1):
            # Only call _force_close if we're in a position
            if self.pos:
                reward += self._force_close()
            done=True
        if done:
            info.update(final_equity=self.equity, episode_log=self.event_log)
        return reward, done, info
