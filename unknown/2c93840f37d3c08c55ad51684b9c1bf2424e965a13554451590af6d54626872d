#!/usr/bin/env python3
"""
Skript na kontrolu parquet dát pre konkrétny čas.
Skontroluje XRPUSDC 1s dáta okolo času 06:38:20-06:38:35 dňa 2025-07-02.
"""

import pandas as pd
import numpy as np
from datetime import datetime, timezone, timedelta

def check_parquet_data():
    """Skontroluje parquet dáta pre podozrivý čas."""
    
    file_path = "parquet_processed/XRPUSDC/1s/2025-07-01.parquet"
    
    try:
        print(f"📁 Načítavam súbor: {file_path}")
        df = pd.read_parquet(file_path)
        
        print(f"✅ Načítaných {len(df)} 1s barov")
        print(f"📊 Stĺpce: {list(df.columns)}")
        
        # Spracovanie timestamp
        if 'timestamp' in df.columns:
            df['timestamp'] = pd.to_datetime(df['timestamp'], utc=True)
            df = df.set_index('timestamp').sort_index()
        elif isinstance(df.index, pd.DatetimeIndex):
            df.index = pd.to_datetime(df.index, utc=True)
        else:
            print("❌ Nenašiel som timestamp!")
            return
        
        print(f"⏰ Časový rozsah: {df.index.min()} -> {df.index.max()}")
        
        # Kritický čas - rozšírené okno
        start_time = pd.Timestamp('2025-07-01 06:37:00', tz='UTC')
        end_time = pd.Timestamp('2025-07-01 06:40:00', tz='UTC')
        
        critical_data = df.loc[start_time:end_time]
        
        if critical_data.empty:
            print(f"❌ Žiadne dáta v kritickom čase {start_time} -> {end_time}")
            return
        
        print(f"\n🔍 ANALÝZA KRITICKÉHO OBDOBIA ({len(critical_data)} barov):")
        print("=" * 80)
        
        # Kontrola na nuly v OHLCV dátach
        print("\n🚨 KONTROLA NÚL V KRITICKÝCH DÁTACH:")
        print("-" * 50)
        
        ohlcv_cols = ['open', 'high', 'low', 'close', 'volume']
        found_zeros = False
        
        for col in ohlcv_cols:
            if col in critical_data.columns:
                zeros = critical_data[col] == 0
                zero_count = zeros.sum()
                if zero_count > 0:
                    found_zeros = True
                    print(f"🔴 {col}: {zero_count} núl z {len(critical_data)} barov")
                    
                    # Ukáž kde sú nuly
                    zero_times = critical_data[zeros].index
                    for zt in zero_times:
                        print(f"   ⚠️  Nula v {col} o {zt.strftime('%H:%M:%S')}")
                else:
                    print(f"✅ {col}: žiadne nuly")
        
        if not found_zeros:
            print("✅ Žiadne nuly v OHLCV dátach!")
        
        # Detailné dáta okolo kritického času
        print(f"\n📋 DETAILNÉ DÁTA OKOLO KRITICKÉHO ČASU:")
        print("-" * 80)
        print("Čas     | Open     | High     | Low      | Close    | Volume")
        print("-" * 80)
        
        for idx, row in critical_data.iterrows():
            timestamp_str = idx.strftime('%H:%M:%S')
            open_val = row.get('open', 0)
            high_val = row.get('high', 0)
            low_val = row.get('low', 0)
            close_val = row.get('close', 0)
            volume_val = row.get('volume', 0)
            
            # Označ podozrivé hodnoty
            flag = ""
            if any(v == 0 for v in [open_val, high_val, low_val, close_val]):
                flag = " ⚠️  NULA!"
            elif high_val < low_val:
                flag = " ❌ HIGH < LOW!"
            elif close_val < low_val or close_val > high_val:
                flag = " ❌ CLOSE MIMO ROZSAHU!"
            
            print(f"{timestamp_str} | {open_val:8.5f} | {high_val:8.5f} | {low_val:8.5f} | {close_val:8.5f} | {volume_val:8.0f}{flag}")
        
        # Kontrola konzistentnosti OHLC
        print(f"\n🔍 KONTROLA KONZISTENTNOSTI OHLC:")
        print("-" * 50)
        
        invalid_hl = (critical_data['high'] < critical_data['low']).sum()
        if invalid_hl > 0:
            print(f"❌ {invalid_hl} barov má HIGH < LOW!")
        else:
            print("✅ Všetky bary majú HIGH >= LOW")
        
        invalid_close = ((critical_data['close'] > critical_data['high']) | 
                        (critical_data['close'] < critical_data['low'])).sum()
        if invalid_close > 0:
            print(f"❌ {invalid_close} barov má CLOSE mimo HIGH-LOW rozsahu!")
        else:
            print("✅ Všetky CLOSE hodnoty sú v HIGH-LOW rozsahu")
        
        invalid_open = ((critical_data['open'] > critical_data['high']) | 
                       (critical_data['open'] < critical_data['low'])).sum()
        if invalid_open > 0:
            print(f"❌ {invalid_open} barov má OPEN mimo HIGH-LOW rozsahu!")
        else:
            print("✅ Všetky OPEN hodnoty sú v HIGH-LOW rozsahu")
        
        # Analýza cenového pohybu v kritickom čase
        print(f"\n📈 ANALÝZA CENOVÉHO POHYBU:")
        print("-" * 50)
        
        if len(critical_data) > 0:
            min_price = critical_data[['open', 'high', 'low', 'close']].min().min()
            max_price = critical_data[['open', 'high', 'low', 'close']].max().max()
            start_price = critical_data['close'].iloc[0]
            end_price = critical_data['close'].iloc[-1]
            
            print(f"📊 Minimálna cena: {min_price:.5f}")
            print(f"📊 Maximálna cena: {max_price:.5f}")
            print(f"📊 Začiatočná cena (06:37:00): {start_price:.5f}")
            print(f"📊 Konečná cena (06:40:00): {end_price:.5f}")
            print(f"📊 Celkový pohyb: {end_price - start_price:.5f}")
            print(f"📊 Cenový rozsah: {max_price - min_price:.5f}")
            
            # Kontrola na veľké skoky
            close_prices = critical_data['close']
            price_changes = close_prices.diff().abs()
            max_change = price_changes.max()
            
            if max_change > 0.01:  # Skok väčší ako 1 cent
                print(f"⚠️  Najväčší cenový skok: {max_change:.5f}")
                big_jump_idx = price_changes.idxmax()
                print(f"    Čas skoku: {big_jump_idx.strftime('%H:%M:%S')}")
                
                # Nájdi predchádzajúcu a nasledujúcu cenu
                prev_idx = critical_data.index.get_loc(big_jump_idx) - 1
                if prev_idx >= 0:
                    prev_price = critical_data['close'].iloc[prev_idx]
                    curr_price = critical_data.loc[big_jump_idx, 'close']
                    print(f"    Z: {prev_price:.5f} -> Na: {curr_price:.5f}")
        
        # Špeciálna kontrola okolo 06:38:35 (čas TP exit z logu)
        target_time = pd.Timestamp('2025-07-01 06:38:35', tz='UTC')
        if target_time in critical_data.index:
            target_bar = critical_data.loc[target_time]
            print(f"\n🎯 DÁTA PRE TP EXIT ČAS (06:38:35):")
            print(f"   OHLC: {target_bar['open']:.5f}/{target_bar['high']:.5f}/{target_bar['low']:.5f}/{target_bar['close']:.5f}")
            
            if target_bar['close'] == 0:
                print("   ⚠️  CLOSE = 0 v čase TP exitu!")
            
        print(f"\n✅ Kontrola dokončená!")
        
        # Return critical data for further analysis
        return critical_data
        
    except Exception as e:
        print(f"❌ Chyba pri analýze: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    critical_data = check_parquet_data()