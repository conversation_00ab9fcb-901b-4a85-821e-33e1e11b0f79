#!/usr/bin/env python3

import pandas as pd
import numpy as np

def analyze_total_pnl():
    print("🔍 FINAL PnL ANALYSIS")
    print("=" * 60)
    
    # Read trades
    trades_df = pd.read_csv('backtest_trades.csv')
    print(f"📊 Trades Summary:")
    print(f"   Number of trades: {len(trades_df)}")
    
    # Calculate total net PnL from trades
    total_trade_pnl = 0
    total_fees = 0
    
    for i, trade in trades_df.iterrows():
        trade_pnl = trade['pnl']
        entry_fee = trade['entry_fee']
        exit_fee = trade['exit_fee']
        net_trade_pnl = trade_pnl - entry_fee - exit_fee
        
        print(f"   Trade {i+1}: PnL={trade_pnl:.2f}, Entry Fee={entry_fee:.2f}, Exit Fee={exit_fee:.2f}")
        print(f"            Net PnL: {net_trade_pnl:.2f}")
        
        total_trade_pnl += trade_pnl
        total_fees += (entry_fee + exit_fee)
    
    net_total_pnl = total_trade_pnl - total_fees
    print(f"\n📈 Calculated Totals:")
    print(f"   Gross PnL: ${total_trade_pnl:.2f}")
    print(f"   Total Fees: ${total_fees:.2f}")
    print(f"   Net Total PnL: ${net_total_pnl:.2f}")
    
    # Read last few lines of equity to see final values
    print(f"\n📊 Reading last equity entries...")
    try:
        # Read just the last few lines of equity file
        with open('backtest_equity.csv', 'r') as f:
            lines = f.readlines()
            
        print(f"   Total equity entries: {len(lines)-1}")  # -1 for header
        
        # Show last 5 entries
        print(f"   Last 5 equity entries:")
        for line in lines[-5:]:
            if 'timestamp' not in line:  # Skip header if repeated
                parts = line.strip().split(',')
                if len(parts) >= 4:
                    timestamp = parts[0]
                    equity_total = float(parts[1])
                    total_pnl = float(parts[2])
                    unrealized_pnl = float(parts[3])
                    print(f"     {timestamp}: Equity=${equity_total:.2f}, Total PnL=${total_pnl:.2f}, Unrealized=${unrealized_pnl:.2f}")
                    
    except Exception as e:
        print(f"   Error reading equity file: {e}")
    
    # Verify against expected calculation
    initial_equity = 10000.00
    expected_final_equity = initial_equity + net_total_pnl
    
    print(f"\n🎯 Verification:")
    print(f"   Initial Equity: ${initial_equity:.2f}")
    print(f"   Expected Final Equity: ${expected_final_equity:.2f}")
    print(f"   Expected Total PnL: ${net_total_pnl:.2f}")

if __name__ == "__main__":
    analyze_total_pnl()