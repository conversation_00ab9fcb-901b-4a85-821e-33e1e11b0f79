#!/usr/bin/env python3
"""
Optimalizovaný threshold finder pre SAC modelov.
Rieši timeout problém použitím krat<PERSON><PERSON><PERSON> testov a inteligentnejšou optimalizáciou.
"""

import os
import json
import subprocess
import pandas as pd
from pathlib import Path
import datetime
import numpy as np
from typing import List, Dict, Tuple
import time

def find_sac_models() -> List[Tuple[str, str]]:
    """Nájde dostupné SAC modely."""
    models = []
    model_files = ["sac_1999360_steps.zip", "sac_9996800_steps.zip"]  # Špecifické modely
    
    for model_file in model_files:
        if os.path.exists(model_file):
            base_name = model_file.replace("_steps.zip", "")
            vecnorm_file = f"{base_name}.vecnorm.pkl"
            
            if os.path.exists(vecnorm_file):
                models.append((model_file, vecnorm_file))
                print(f"✅ Model nájdený: {model_file}")
            else:
                print(f"⚠️ Chýba VecNormalize: {vecnorm_file}")
    
    return models

def get_smart_threshold_range(model_name: str) -> List[float]:
    """Inteligentný výber threshold rozsahu podľa modelu."""
    if "1999360" in model_name:
        # Starší model - vyššie thresholdy
        return [0.05, 0.1, 0.15, 0.2, 0.25, 0.3]
    elif "9996800" in model_name:
        # Pokročilejší model - nižšie thresholdy  
        return [0.001, 0.002, 0.005, 0.01, 0.02, 0.05]
    else:
        # Default range
        return [0.01, 0.05, 0.1, 0.2, 0.3]

def create_fast_test_config(base_config_path: str, model_path: str, 
                           threshold: float, output_dir: str) -> str:
    """Vytvorí konfiguráciu optimalizovanú pre rýchle testovanie."""
    
    with open(base_config_path, 'r') as f:
        config = json.load(f)
    
    # Model a threshold nastavenia
    config["trainingSettings"]["modelSavePath"] = model_path.replace(".zip", "")
    config["tradeParams"]["entryActionThreshold"] = threshold
    config["tradeParams"]["exitActionThreshold"] = threshold * 0.8  # Exit trochu nižší
    config["tradeParams"]["longEntryThreshold"] = threshold
    config["tradeParams"]["shortEntryThreshold"] = threshold
    
    # OPTIMALIZÁCIE PRE RÝCHLOSŤ:
    # 1. Vypneme 1s decisions pre rýchlejšie testovanie
    config["use_1s_decisions"] = False
    
    # 2. Kratšie testové obdobie (len jeden mesiac)
    config["backtestSettings"]["startTime"] = "2025-01-15T00:00:00Z"
    config["backtestSettings"]["endTime"] = "2025-02-15T23:59:59Z"
    
    # 3. Menší initial equity pre rýchlejšie loading
    config["account"]["initialEquity"] = 100
    
    # 4. Agresívnejší risk management pre viac obchodov
    config["riskManagement"]["riskPerTradePercentage"] = 2.0
    
    # Vytvoríme názov súboru
    model_name = Path(model_path).stem
    config_name = f"fast_test_{model_name}_thresh_{threshold}.json"
    config_path = os.path.join(output_dir, config_name)
    
    os.makedirs(output_dir, exist_ok=True)
    with open(config_path, 'w') as f:
        json.dump(config, f, indent=2)
    
    return config_path

def run_fast_backtest(config_path: str, model_name: str, threshold: float) -> Dict:
    """Spustí rýchly backtest s timeout ochranou."""
    
    print(f"🔄 Testovanie: {model_name} threshold={threshold}")
    
    start_time = time.time()
    
    cmd = [
        "python", "simulate_trading.py",
        "--cfg", config_path,
        "--start", "2025-04-15",
        "--end", "2025-04-20", 
        "--log-level", "ERROR",  # Minimálny logging
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)  # 5 min timeout
        
        elapsed = time.time() - start_time
        
        if result.returncode == 0:
            # Parsujeme výsledky z stdout
            metrics = parse_simulation_output(result.stdout)
            metrics.update({
                'model_name': model_name,
                'threshold': threshold,
                'elapsed_time': elapsed,
                'status': 'Success'
            })
            print(f"  ✅ {elapsed:.1f}s - Return: {metrics.get('total_return', 0):.2f}% - Trades: {metrics.get('total_trades', 0)}")
            return metrics
        else:
            print(f"  ❌ {elapsed:.1f}s - Backtest failed")
            return {
                'model_name': model_name,
                'threshold': threshold,
                'status': 'Failed',
                'error': result.stderr[:200],
                'elapsed_time': elapsed
            }
            
    except subprocess.TimeoutExpired:
        print(f"  ⏰ 5min timeout")
        return {
            'model_name': model_name,
            'threshold': threshold,
            'status': 'Timeout',
            'elapsed_time': 300
        }
    except Exception as e:
        print(f"  💥 Error: {e}")
        return {
            'model_name': model_name,
            'threshold': threshold,
            'status': 'Error',
            'error': str(e)
        }

def parse_simulation_output(stdout: str) -> Dict:
    """Parsuje výsledky zo stdout simulate_trading.py."""
    metrics = {}
    
    try:
        lines = stdout.split('\n')
        for line in lines:
            if 'Počet obchodov' in line and ':' in line:
                metrics['total_trades'] = int(line.split(':')[1].strip())
            elif 'Win Rate' in line and ':' in line:
                metrics['win_rate'] = float(line.split(':')[1].strip().replace('%', ''))
            elif 'Celkový PnL (%)' in line and ':' in line:
                metrics['total_return'] = float(line.split(':')[1].strip().replace('%', ''))
            elif 'Profit Factor' in line and ':' in line:
                value = line.split(':')[1].strip()
                if value != 'inf':
                    metrics['profit_factor'] = float(value)
                else:
                    metrics['profit_factor'] = 999.0
            elif 'Maximálny Drawdown (%)' in line and ':' in line:
                metrics['max_drawdown'] = float(line.split(':')[1].strip().replace('%', ''))
    except:
        pass  # Ignore parsing errors
    
    return metrics

def find_optimal_threshold(model_file: str, vecnorm_file: str, output_dir: str) -> Dict:
    """Nájde optimálny threshold pre daný model pomocou smart search."""
    
    model_name = Path(model_file).stem
    print(f"\n🎯 Optimalizujem threshold pre: {model_name}")
    
    # Získame smart range pre tento model
    thresholds = get_smart_threshold_range(model_name)
    print(f"   Testované thresholdy: {thresholds}")
    
    results = []
    
    for threshold in thresholds:
        # Vytvoríme test config
        config_path = create_fast_test_config(
            "strategyConfig_scalp_1s.json", model_file, threshold, output_dir
        )
        
        # Spustíme test
        result = run_fast_backtest(config_path, model_name, threshold)
        results.append(result)
        
        # Early stopping ak máme príliš veľa neúspešných testov
        failed_count = sum(1 for r in results if r['status'] != 'Success')
        if failed_count >= 3:
            print(f"  ⚠️ Príliš veľa neúspešných testov, ukončujem early")
            break
    
    # Najdeme najlepší threshold
    successful = [r for r in results if r['status'] == 'Success' and r.get('total_trades', 0) > 0]
    
    if successful:
        # Optimalizujeme podľa kombinace return a trades count
        for r in successful:
            r['score'] = (r.get('total_return', 0) * 0.7 + 
                         r.get('total_trades', 0) * 0.1 + 
                         r.get('win_rate', 0) * 0.2)
        
        best = max(successful, key=lambda x: x['score'])
        
        print(f"\n🏆 Najlepší threshold pre {model_name}: {best['threshold']}")
        print(f"   Return: {best.get('total_return', 0):.2f}%")
        print(f"   Trades: {best.get('total_trades', 0)}")
        print(f"   Win Rate: {best.get('win_rate', 0):.1f}%")
        
        return {
            'model': model_name,
            'optimal_threshold': best['threshold'],
            'expected_return': best.get('total_return', 0),
            'expected_trades': best.get('total_trades', 0),
            'all_results': results
        }
    else:
        print(f"❌ Žiadny úspešný test pre {model_name}")
        return {
            'model': model_name,
            'optimal_threshold': None,
            'all_results': results
        }

def generate_threshold_report(optimization_results: List[Dict], output_dir: str):
    """Vygeneruje report s optimálnymi thresholdmi."""
    
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    
    report_text = f"""
===============================================
THRESHOLD OPTIMIZATION REPORT
===============================================
Generated: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
Test Period: 2025-01-15 to 2025-02-15 (1 month)
Mode: 5m decisions (fast testing)

OPTIMÁLNE THRESHOLDY:
===============================================
"""
    
    optimal_configs = {}
    
    for result in optimization_results:
        model = result['model']
        threshold = result['optimal_threshold']
        
        if threshold is not None:
            report_text += f"""
{model}:
  Optimálny threshold: {threshold}
  Očakávaný return: {result.get('expected_return', 0):.2f}%
  Očakávaný počet obchodov: {result.get('expected_trades', 0)}
  
  Vysvetlenie: {"Konzervatívny model - vyššie thresholdy" if "1999360" in model else "Pokročilý model - nižšie thresholdy"}
"""
            
            optimal_configs[model] = {
                "entryActionThreshold": threshold,
                "exitActionThreshold": threshold * 0.8,
                "longEntryThreshold": threshold,
                "shortEntryThreshold": threshold
            }
        else:
            report_text += f"""
{model}: 
  ❌ Nepodarilo sa nájsť optimálny threshold
"""

    report_text += f"""

ODPORÚČANIA PRE POUŽITIE:
===============================================
1. Pre starší model (sac_1999360): Použite threshold 0.1-0.3
2. Pre pokročilý model (sac_9996800): Použite threshold 0.001-0.05
3. Začnite s optimálnymi hodnotami a fine-tunujte podľa live výsledkov
4. Monitorujte počet obchodov - príliš nízke thresholdy = príliš veľa obchodov
5. Monitorujte win rate - príliš vysoké thresholdy = málo obchodov

TECHNICKÉ POZNÁMKY:
===============================================
- Testy vykonané na 1-mesačnom datasete pre rýchlosť
- Režim 5m rozhodovanie (nie 1s) pre efektívnosť  
- Odporúčame validáciu na dlhšom období pred live použitím
"""
    
    # Uložíme textový report
    report_path = os.path.join(output_dir, f"threshold_optimization_report_{timestamp}.txt")
    with open(report_path, 'w') as f:
        f.write(report_text)
    
    # Uložíme JSON konfigurácie
    config_path = os.path.join(output_dir, f"optimal_thresholds_{timestamp}.json")
    with open(config_path, 'w') as f:
        json.dump(optimal_configs, f, indent=2)
    
    print(f"\n📊 Report uložený:")
    print(f"   {report_path}")
    print(f"   {config_path}")
    
    return optimal_configs

def main():
    """Hlavná funkcia pre threshold optimalizáciu."""
    
    print("🎯 SAC Model Threshold Optimizer")
    print("================================")
    
    # Vytvoríme output adresár
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = f"threshold_optimization_{timestamp}"
    os.makedirs(output_dir, exist_ok=True)
    
    # Nájdeme modely
    models = find_sac_models()
    
    if not models:
        print("❌ Neboli nájdené žiadne SAC modely!")
        return
    
    print(f"📊 Nájdených {len(models)} modelov")
    print(f"📁 Výstupný adresár: {output_dir}")
    
    # Optimalizujeme každý model
    all_results = []
    
    for model_file, vecnorm_file in models:
        result = find_optimal_threshold(model_file, vecnorm_file, output_dir)
        all_results.append(result)
    
    # Vygenerujeme report
    print(f"\n📊 Generujem report...")
    optimal_configs = generate_threshold_report(all_results, output_dir)
    
    print(f"\n✅ Threshold optimalizácia dokončená!")
    print(f"📁 Výsledky v: {output_dir}")
    
    # Ukážeme summary
    print(f"\n🎯 SUMMARY:")
    for model, config in optimal_configs.items():
        print(f"   {model}: threshold = {config['entryActionThreshold']}")

if __name__ == "__main__":
    main()