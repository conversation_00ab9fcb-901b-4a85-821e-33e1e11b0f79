#!/usr/bin/env python3
"""
Test script pre overenie live trading integrácie
"""
import os
import sys
import json
import logging
from pathlib import Path
from datetime import datetime, timezone

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(name)s] [%(levelname)s] %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S"
)
log = logging.getLogger("LiveTradingTest")

def test_configuration():
    """Test konfigurácie"""
    log.info("=== TESTING CONFIGURATION ===")
    
    config_path = Path("strategyConfig_scalp_1s.json")
    if not config_path.exists():
        log.error(f"Config file not found: {config_path}")
        return False
    
    with open(config_path, 'r') as f:
        config = json.load(f)
    
    # Check critical settings
    critical_settings = {
        'symbol': config.get('symbol'),
        'primaryTimeframe': config.get('primaryTimeframe'),
        'use_1s_decisions': config.get('use_1s_decisions'),
        'testMode': config.get('testMode'),
        'agentExitsEnabled': config.get('tradeParams', {}).get('agentExitsEnabled'),
        'trailingStopLoss.enabled': config.get('trailingStopLoss', {}).get('enabled'),
    }
    
    log.info("Critical settings:")
    for key, value in critical_settings.items():
        log.info(f"  {key}: {value}")
    
    # Check feature columns
    feature_cols = config.get('envSettings', {}).get('feature_columns', [])
    log.info(f"Feature columns: {len(feature_cols)} features")
    
    # Check if we have required environment variables
    required_env_vars = ['COINAPI_KEY', 'BINANCE_API_KEY', 'BINANCE_API_SECRET']
    missing_vars = []
    for var in required_env_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        log.warning(f"Missing environment variables: {missing_vars}")
        log.warning("Live trading will not work without these!")
    else:
        log.info("✅ All required environment variables are set")
    
    return True

def test_model_loading():
    """Test načítania modelu"""
    log.info("=== TESTING MODEL LOADING ===")
    
    try:
        from popart_sac import PopArtSAC
        from agent import SimpleCNN1D, SafeReplayBuffer
        
        # Find latest model
        model_files = list(Path(".").glob("sac_*_steps.zip"))
        if not model_files:
            log.error("No model files found!")
            return False
        
        latest_model = sorted(model_files, key=lambda x: int(x.stem.split('_')[1]))[-1]
        log.info(f"Testing model: {latest_model}")
        
        # Load config for model setup
        with open("strategyConfig_scalp_1s.json", 'r') as f:
            config = json.load(f)
        
        feature_cols = config["envSettings"]["feature_columns"]
        training_settings = config["trainingSettings"]
        
        # Setup custom objects
        policy_kwargs = {
            "net_arch": training_settings["netArch"],
            "features_extractor_class": SimpleCNN1D,
            "features_extractor_kwargs": {
                **training_settings["featureExtractorKwargs"],
                "feature_names": feature_cols,
                "meta_len": 11
            }
        }
        
        custom_objects = {
            "buffer_size": training_settings["bufferSize"],
            "policy_kwargs": policy_kwargs,
            "replay_buffer_class": SafeReplayBuffer,
            "learning_rate": 0.0001
        }
        
        # Try to load model
        model = PopArtSAC.load(latest_model, device="cpu", custom_objects=custom_objects)
        log.info(f"✅ Model loaded successfully: {type(model).__name__}")
        log.info(f"   Observation space: {model.observation_space.shape}")
        
        return True
        
    except Exception as e:
        log.error(f"❌ Model loading failed: {e}")
        return False

def test_trade_executor():
    """Test TradeExecutor inicializácie"""
    log.info("=== TESTING TRADE EXECUTOR ===")
    
    try:
        from trade_executor import TradeExecutor
        
        # Load config
        with open("strategyConfig_scalp_1s.json", 'r') as f:
            config = json.load(f)
        
        # Initialize in test mode
        te = TradeExecutor(config, test_mode=True)
        log.info("✅ TradeExecutor initialized successfully")
        
        # Test position info
        pos, size, entry = te.get_current_position()
        log.info(f"   Current position: {pos}, size: {size}, entry: {entry}")
        
        # Test daily limits reset
        te.force_reset_daily_limits()
        log.info("   Daily limits reset successful")
        
        return True
        
    except Exception as e:
        log.error(f"❌ TradeExecutor initialization failed: {e}")
        return False

def test_indicators():
    """Test indikátorov"""
    log.info("=== TESTING INDICATORS ===")
    
    try:
        from indicators import calculate_and_merge_indicators
        import pandas as pd
        import numpy as np
        
        # Load config
        with open("strategyConfig_scalp_1s.json", 'r') as f:
            config = json.load(f)
        
        # Create dummy data
        timestamps = pd.date_range(start='2025-01-01', periods=100, freq='5min', tz='UTC')
        dummy_data = pd.DataFrame({
            'open': np.random.uniform(1.0, 2.0, 100),
            'high': np.random.uniform(1.1, 2.1, 100),
            'low': np.random.uniform(0.9, 1.9, 100),
            'close': np.random.uniform(1.0, 2.0, 100),
            'volume': np.random.uniform(1000, 10000, 100),
        }, index=timestamps)
        
        data_dict = {'5m': dummy_data}
        
        # Test indicator calculation
        result_df, calculated_features = calculate_and_merge_indicators(
            data_dict, config,
            skip_hmm=True,  # Skip HMM for testing
            hmm_model_external=None,
            hmm_scaler_external=None
        )
        
        log.info(f"✅ Indicators calculated successfully")
        log.info(f"   Result shape: {result_df.shape}")
        log.info(f"   Calculated features: {len(calculated_features)}")
        
        # Check expected features
        expected_features = config['envSettings']['feature_columns']
        missing_features = set(expected_features) - set(result_df.columns)
        if missing_features:
            log.warning(f"   Missing features: {len(missing_features)} - {list(missing_features)[:5]}...")
        else:
            log.info("   ✅ All expected features present")
        
        return True
        
    except Exception as e:
        log.error(f"❌ Indicators test failed: {e}")
        return False

def main():
    """Main test function"""
    log.info("🧪 STARTING LIVE TRADING INTEGRATION TESTS")
    log.info("=" * 60)
    
    tests = [
        ("Configuration", test_configuration),
        ("Model Loading", test_model_loading),
        ("Trade Executor", test_trade_executor),
        ("Indicators", test_indicators),
    ]
    
    results = {}
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            log.error(f"❌ {test_name} test crashed: {e}")
            results[test_name] = False
        
        log.info("")  # Empty line between tests
    
    # Summary
    log.info("=" * 60)
    log.info("🏁 TEST RESULTS SUMMARY")
    log.info("=" * 60)
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        log.info(f"{test_name:<20}: {status}")
        if result:
            passed += 1
    
    log.info("=" * 60)
    log.info(f"OVERALL: {passed}/{total} tests passed")
    
    if passed == total:
        log.info("🎉 ALL TESTS PASSED! Live trading should work correctly.")
        log.info("💡 Next steps:")
        log.info("   1. Set environment variables (COINAPI_KEY, BINANCE_API_KEY, BINANCE_API_SECRET)")
        log.info("   2. Run: python3 live_trading.py --cfg strategyConfig_scalp_1s.json --test-trade")
        log.info("   3. Monitor logs for proper functionality")
    else:
        log.error("❌ Some tests failed. Fix issues before running live trading!")
    
    return passed == total

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
