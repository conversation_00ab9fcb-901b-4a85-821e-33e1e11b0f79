#!/usr/bin/env python3
"""
Skript pre testovanie špecifických hodnôt trailing stop loss parametrov.
Umožňuje rýchle porovnanie niekoľkých konkrétnych kombinácií.
"""

import argparse
import logging
from pathlib import Path
from datetime import datetime, timezone
import pandas as pd
import numpy as np
from copy import deepcopy

# Import funkcii zo simulate_trading_new.py a test_trailing_stop_loss.py
from simulate_trading_new import (
    load_config, load_backtest_data, run_backtest_original,
    calculate_metrics, custom_objects, log
)
from test_trailing_stop_loss import (
    simulate_single_trade_with_tsl_params, create_neutral_result
)

def test_specific_tsl_combinations(config: dict, data_dict: dict, agent, vecnorm=None, use_1s_decisions: bool = False):
    """
    Testuje špecifické kombinácie TSL parametrov definované v kóde.
    """
    log.info("🎯 Testujem špecifické TSL kombinácie...")
    
    # Definícia špecifických kombinácií na testovanie
    specific_combinations = [
        # Aktuálne nastavenia z configu
        {'name': 'Current', 'activateATRMultiplier': 0.5, 'trailATRMultiplier': 0.35},
        
        # Konzervatívnejšie nastavenia (neskôr aktivácia, menší trail)
        {'name': 'Conservative', 'activateATRMultiplier': 0.8, 'trailATRMultiplier': 0.25},
        
        # Agresívnejšie nastavenia (skôr aktivácia, väčší trail)
        {'name': 'Aggressive', 'activateATRMultiplier': 0.3, 'trailATRMultiplier': 0.5},
        
        # Veľmi skorá aktivácia s miernym trailing
        {'name': 'Early_Trail', 'activateATRMultiplier': 0.2, 'trailATRMultiplier': 0.35},
        
        # Neskoršia aktivácia s agresívnym trailing
        {'name': 'Late_Aggressive', 'activateATRMultiplier': 1.0, 'trailATRMultiplier': 0.65},
        
        # Vyvážené nastavenia
        {'name': 'Balanced', 'activateATRMultiplier': 0.6, 'trailATRMultiplier': 0.4},
        
        # Test extrémnych hodnôt
        {'name': 'Very_Early', 'activateATRMultiplier': 0.15, 'trailATRMultiplier': 0.2},
        {'name': 'Very_Late', 'activateATRMultiplier': 1.5, 'trailATRMultiplier': 0.8},
    ]
    
    # Spustenie pôvodného backtestu na získanie trade setups
    log.info("📊 Spúšťam pôvodný backtest na získanie obchodov...")
    original_trades, original_equity_curve, original_final_equity = run_backtest_original(
        config, data_dict, agent, vecnorm, use_1s_decisions
    )
    
    log.info(f"🎯 Našiel som {len(original_trades)} obchodov na testovanie")
    
    if len(original_trades) == 0:
        log.warning("❌ Žiadne obchody sa nevykonali v pôvodnom backteste!")
        return original_trades, original_equity_curve, original_final_equity, pd.DataFrame()
    
    # Príprava súboru pre výsledky
    results_file = f"specific_tsl_test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    
    # Výsledky pre všetky kombinácie
    all_results = []
    
    # Testovanie každej kombinácie
    for combo in specific_combinations:
        combo_name = combo['name']
        tsl_params = {
            'activateATRMultiplier': combo['activateATRMultiplier'],
            'trailATRMultiplier': combo['trailATRMultiplier']
        }
        
        log.info(f"🧪 Testujem kombináciu '{combo_name}': activate={tsl_params['activateATRMultiplier']:.2f}R, trail={tsl_params['trailATRMultiplier']:.2f}")
        
        combo_results = []
        
        # Pre každý obchod testujeme túto kombináciu
        for trade_idx, trade in enumerate(original_trades):
            # Príprava trade setup s dodatočnými informáciami
            trade_setup = {
                'trade_id': trade_idx,
                'entry_time': trade['entry_time'],
                'direction': trade['direction'],
                'entry_price': trade['entry_price'],
                'size': trade['size'],
                'current_atr': trade.get('atr', 0.001)
            }
            
            # Ak ATR nie je v trade data, pokúsime sa ho získať z 5m dát
            if 'atr' not in trade and data_dict.get('primary') is not None:
                try:
                    primary_data = data_dict['primary']
                    atr_col = next((col for col in primary_data.columns if col.startswith('ATR_')), None)
                    if atr_col:
                        mask = primary_data.index <= trade['entry_time']
                        available_data = primary_data[mask]
                        if not available_data.empty:
                            trade_setup['current_atr'] = available_data.iloc[-1][atr_col]
                except Exception as e:
                    log.warning(f"Nepodarilo sa získať ATR pre obchod {trade_idx}: {e}")
            
            # Simulácia obchodu s týmito TSL parametrami
            result = simulate_single_trade_with_tsl_params(
                trade_setup, tsl_params, config, data_dict['second']
            )
            
            # Pridáme meno kombinácie
            result['combination_name'] = combo_name
            combo_results.append(result)
            all_results.append(result)
        
        # Analýza výsledkov pre túto kombináciu
        combo_df = pd.DataFrame(combo_results)
        analyze_single_combination(combo_df, combo_name, tsl_params)
    
    # Uloženie všetkých výsledkov
    results_df = pd.DataFrame(all_results)
    results_df.to_csv(results_file, index=False, float_format='%.6f')
    log.info(f"💾 Výsledky uložené do: {results_file}")
    
    # Porovnanie všetkých kombinácií
    compare_all_combinations(results_df, specific_combinations)
    
    return original_trades, original_equity_curve, original_final_equity, results_df

def analyze_single_combination(combo_df, combo_name, tsl_params):
    """Analyzuje výsledky pre jednu kombináciu TSL parametrov."""
    total_pnl = combo_df['pnl'].sum()
    avg_pnl = combo_df['pnl'].mean()
    win_rate = (combo_df['exit_reason'] == 'TP').sum() / len(combo_df) * 100 if len(combo_df) > 0 else 0
    tsl_exit_rate = (combo_df['exit_reason'] == 'TSL').sum() / len(combo_df) * 100 if len(combo_df) > 0 else 0
    tsl_activation_rate = combo_df['tsl_activated'].sum() / len(combo_df) * 100 if len(combo_df) > 0 else 0
    avg_max_r = combo_df['max_r_multiple'].mean()
    avg_time_in_trade = combo_df['time_in_trade_seconds'].mean()
    
    log.info(f"  📊 {combo_name} Results:")
    log.info(f"    Total PnL: {total_pnl:.4f}")
    log.info(f"    Avg PnL per trade: {avg_pnl:.6f}")
    log.info(f"    Win Rate: {win_rate:.1f}%")
    log.info(f"    TSL Exit Rate: {tsl_exit_rate:.1f}%")
    log.info(f"    TSL Activation Rate: {tsl_activation_rate:.1f}%")
    log.info(f"    Avg Max R-Multiple: {avg_max_r:.2f}")
    log.info(f"    Avg Time in Trade: {avg_time_in_trade:.0f}s")

def compare_all_combinations(results_df, combinations):
    """Porovnáva všetky kombinácie a zobrazuje najlepšie."""
    log.info("\n🏆 POROVNANIE VŠETKÝCH TSL KOMBINÁCIÍ:")
    
    # Zoskupenie podľa combination_name
    grouped = results_df.groupby('combination_name').agg({
        'pnl': ['sum', 'mean', 'count', 'std'],
        'exit_reason': [
            lambda x: (x == 'TP').sum() / len(x) * 100,
            lambda x: (x == 'TSL').sum() / len(x) * 100
        ],
        'time_in_trade_seconds': 'mean',
        'tsl_activated': lambda x: x.sum() / len(x) * 100,
        'max_r_multiple': 'mean',
        'activateATRMultiplier': 'first',
        'trailATRMultiplier': 'first'
    }).round(4)
    
    grouped.columns = [
        'total_pnl', 'avg_pnl', 'trade_count', 'pnl_std',
        'win_rate_pct', 'tsl_exit_rate_pct', 'avg_time_in_trade',
        'tsl_activation_rate_pct', 'avg_max_r_multiple',
        'activate_atr', 'trail_atr'
    ]
    grouped = grouped.reset_index()
    
    # Zoradenie podľa total PnL
    grouped_sorted = grouped.sort_values('total_pnl', ascending=False)
    
    print("\n" + "="*100)
    print(f"{'Rank':<4} {'Name':<15} {'Total PnL':<10} {'Avg PnL':<10} {'Win%':<6} {'TSL%':<6} {'Act%':<6} {'Activate':<8} {'Trail':<6}")
    print("="*100)
    
    for i, (_, row) in enumerate(grouped_sorted.iterrows(), 1):
        print(f"{i:<4} {row['combination_name']:<15} {row['total_pnl']:<10.4f} {row['avg_pnl']:<10.6f} "
              f"{row['win_rate_pct']:<6.1f} {row['tsl_exit_rate_pct']:<6.1f} {row['tsl_activation_rate_pct']:<6.1f} "
              f"{row['activate_atr']:<8.2f} {row['trail_atr']:<6.2f}")
    
    print("="*100)
    print("TSL% = TSL Exit Rate, Act% = TSL Activation Rate")
    
    # Najlepšia kombinácia
    best = grouped_sorted.iloc[0]
    log.info(f"\n🥇 NAJLEPŠIA KOMBINÁCIA: {best['combination_name']}")
    log.info(f"   Activate: {best['activate_atr']:.2f}R, Trail: {best['trail_atr']:.2f}")
    log.info(f"   Total PnL: {best['total_pnl']:.4f}")
    log.info(f"   Win Rate: {best['win_rate_pct']:.1f}%")
    log.info(f"   TSL Activation Rate: {best['tsl_activation_rate_pct']:.1f}%")
    
    # Uloženie porovnania
    comparison_file = f"tsl_combination_comparison_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    grouped_sorted.to_csv(comparison_file, index=False, float_format='%.6f')
    log.info(f"💾 Porovnanie uložené do: {comparison_file}")
    
    return grouped_sorted

# --- Hlavná časť skriptu ---
if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Testuje špecifické kombinácie trailing stop loss parametrov.")
    parser.add_argument("--cfg", required=True, type=Path, help="Cesta ku konfiguračnému súboru (YAML/JSON).")
    parser.add_argument("--start", required=True, help="Dátum začiatku backtestu (YYYY-MM-DD).")
    parser.add_argument("--end", required=True, help="Dátum konca backtestu (YYYY-MM-DD, vrátane).")
    parser.add_argument("--model", type=Path, default=None, help="Voliteľná cesta k modelu agenta.")
    parser.add_argument("--log-level", default="INFO", choices=["DEBUG", "INFO", "WARNING", "ERROR"], help="Úroveň logovania.")
    parser.add_argument("--use-1s-decisions", action="store_true", help="Agent robí rozhodnutia každú sekundu.")
    args = parser.parse_args()

    # Nastavenie Loggera
    for handler in log.handlers[:]: 
        log.removeHandler(handler)
    log.setLevel(args.log_level.upper())
    handler = logging.StreamHandler()
    formatter = logging.Formatter("%(asctime)s [%(name)s] [%(levelname)s] %(message)s", datefmt="%Y-%m-%d %H:%M:%S")
    handler.setFormatter(formatter)
    log.addHandler(handler)

    try:
        config = load_config(args.cfg)
        
        # Auto-detekcia modelu
        if args.model:
            model_path = Path(args.model)
        else:
            model_path = config.get('runtime', {}).get('model_path')
            if not model_path:
                raise ValueError("Cesta k modelu nie je definovaná.")
            model_path = Path(model_path)
        
        if not model_path.exists():
            chk_dir = Path("./chk")
            if chk_dir.exists():
                model_files = list(chk_dir.glob("sac_*_steps.zip"))
                if model_files:
                    model_files.sort(key=lambda x: int(x.stem.split('_')[1]))
                    model_path = model_files[-1]
                    log.info(f"Auto-detekovaný model: {model_path}")
        
        if not model_path.exists():
            log.error(f"Model neexistuje: {model_path}")
            exit(1)
            
        # VecNormalize loading
        vecnorm_path = model_path.with_suffix('.vecnorm.pkl')
        if not vecnorm_path.exists():
            model_stem = model_path.stem.replace('_steps', '')
            vecnorm_path = model_path.parent / f"{model_stem}.vecnorm.pkl"
            
        vecnorm = None
        if vecnorm_path.exists():
            try:
                from stable_baselines3.common.vec_env import DummyVecEnv, VecNormalize
                import gymnasium as gym
                
                class DummyEnv(gym.Env):
                    def __init__(self):
                        super().__init__()
                        self.observation_space = gym.spaces.Box(-5.0, 5.0, (1451,), dtype=np.float32)
                        self.action_space = gym.spaces.Box(-1.0, 1.0, (4,), dtype=np.float32)
                    
                    def reset(self, **kwargs):
                        return np.zeros(1451, dtype=np.float32), {}
                    
                    def step(self, action):
                        return np.zeros(1451, dtype=np.float32), 0.0, False, False, {}
                
                dummy_env = DummyVecEnv([lambda: DummyEnv()])
                vecnorm = VecNormalize.load(vecnorm_path, dummy_env)
                vecnorm.training = False
                log.info("VecNormalize načítané")
            except Exception as e:
                log.warning(f"Chyba pri načítavaní VecNormalize: {e}")
        
        # Načítanie agenta
        from agent import SimpleCNN1D, SafeReplayBuffer
        from popart_sac import PopArtSAC

        feats = config["envSettings"]["feature_columns"]
        training_settings = config["trainingSettings"]
        
        policy_kwargs = {
            "net_arch": training_settings["netArch"],
            "features_extractor_class": SimpleCNN1D,
            "features_extractor_kwargs": {
                **training_settings["featureExtractorKwargs"],
                "feature_names": feats,
                "meta_len": 11
            }
        }

        custom_objects.update({
            "buffer_size": training_settings["bufferSize"],
            "policy_kwargs": policy_kwargs,
            "replay_buffer_class": SafeReplayBuffer,
            "learning_rate": 0.0001
        })

        agent = PopArtSAC.load(model_path, device="cpu", custom_objects=custom_objects)
        log.info("Agent načítaný")

        start_dt = datetime.strptime(args.start, "%Y-%m-%d").replace(tzinfo=timezone.utc)
        end_dt = datetime.strptime(args.end, "%Y-%m-%d").replace(hour=23, minute=59, second=59, microsecond=999999, tzinfo=timezone.utc)
        
        # Load data
        use_1s_decisions = config.get('use_1s_decisions', False) or args.use_1s_decisions
        backtest_data = load_backtest_data(config, start_dt, end_dt,
                                          load_second_data=True,
                                          use_1s_decisions=use_1s_decisions)

        if len(backtest_data['primary']) < config['envSettings']['state_lookback']:
            log.error(f"Nedostatok dát pre backtest. Končím.")
            exit(1)

        log.info("🎯 ŠPECIFICKÉ TSL TESTOVANIE SPUSTENÉ")
        trades_list, equity_curve_df, final_equity, results_df = test_specific_tsl_combinations(
            config, backtest_data, agent, vecnorm, use_1s_decisions=use_1s_decisions
        )

        log.info("✅ Špecifické TSL testovanie dokončené!")
        log.info("📁 Pozrite si súbory specific_tsl_test_results_*.csv a tsl_combination_comparison_*.csv")

    except Exception as e: 
        log.error(f"Chyba: {e}", exc_info=True)
        exit(1)