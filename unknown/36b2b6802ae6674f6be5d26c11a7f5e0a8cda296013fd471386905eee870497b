#!/usr/bin/env python3
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import os
from pathlib import Path

def analyze_trades_advanced(csv_path="trades.csv"):
    print(f"Performing advanced analysis of trades from {csv_path}...")

    # Fix DtypeWarning by specifying dtypes for problematic columns
    trades_df = pd.read_csv(
        csv_path,
        dtype={'timestamp': 'Int64', 'exit_reason': 'category'},
        low_memory=False
    )

    print("\nSample data (first 5 rows):")
    print(trades_df.head())

    print("\nColumns in the dataset:")
    print(trades_df.columns.tolist())

    # Ensure timestamp parsing
    if 'timestamp' in trades_df.columns:
        trades_df['timestamp'] = pd.to_datetime(trades_df['timestamp'])

    # === ENHANCED MAX-DD CALCULATION ===
    equity_curve = trades_df['equity_total'].dropna() if 'equity_total' in trades_df.columns else pd.Series(dtype=float)
    if not equity_curve.empty:
        # Calculate rolling maximum (peak)
        rolling_max = equity_curve.cummax()
        # Calculate drawdown as percentage
        drawdown = (rolling_max - equity_curve) / rolling_max * 100
        max_dd = drawdown.max()
        # Find max drawdown period
        max_dd_idx = drawdown.idxmax()
        max_dd_start = rolling_max[:max_dd_idx].idxmax() if max_dd_idx > 0 else 0
        
        print(f"Maximum Drawdown: {max_dd:.2f}%")
        print(f"Max DD occurred at index {max_dd_idx}, started from peak at index {max_dd_start}")
        print(f"Equity at peak: {rolling_max.loc[max_dd_start]:.2f}")
        print(f"Equity at trough: {equity_curve.loc[max_dd_idx]:.2f}")
    else:
        max_dd = np.nan
        print("Maximum Drawdown: N/A (no equity data)")

    # === ENHANCED TIME-BASED ANALYSIS ===
    # Add timestamp processing with proper error handling
    trades_df['ts'] = pd.to_datetime(trades_df['timestamp'], utc=True, errors='coerce')
    trades_df['hour'] = trades_df['ts'].dt.hour
    trades_df['dow'] = trades_df['ts'].dt.dayofweek
    trades_df['date'] = trades_df['ts'].dt.date

    # Calculate win rate and other metrics by hour
    if 'net' in trades_df.columns and not trades_df.empty:
        hour_performance = trades_df.groupby('hour').agg({
            'net': ['count', 'mean', 'sum', lambda x: (x > 0).sum() / len(x) * 100 if len(x) > 0 else 0],
        }).round(4)
        hour_performance.columns = ['trade_count', 'avg_pnl', 'total_pnl', 'win_rate_pct']
        
        dow_performance = trades_df.groupby('dow').agg({
            'net': ['count', 'mean', 'sum', lambda x: (x > 0).sum() / len(x) * 100 if len(x) > 0 else 0],
        }).round(4)
        dow_performance.columns = ['trade_count', 'avg_pnl', 'total_pnl', 'win_rate_pct']
    else:
        hour_performance = pd.DataFrame()
        dow_performance = pd.DataFrame()
        
        print("\nPerformance by Hour:")
        print(hour_performance)
        print("\nPerformance by Day of Week (0=Monday, 6=Sunday):")
        print(dow_performance)
        
        # Identify problematic hours (low win rate or negative PnL)
        problem_hours = hour_performance[
            (hour_performance['win_rate_pct'] < 45) | (hour_performance['total_pnl'] < 0)
        ]
        if not problem_hours.empty:
            print(f"\nProblematic hours (win rate < 45% or negative PnL):")
            print(problem_hours)

    # GUARD all plots: only plot if df is not empty
    out_dir = "trade_analysis_plots_fixed"
    Path(out_dir).mkdir(exist_ok=True)
    
    if not hour_performance.empty:
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        hour_performance['trade_count'].plot(kind='bar', ax=axes[0,0], title='Trades per Hour')
        hour_performance['avg_pnl'].plot(kind='bar', ax=axes[0,1], title='Average PnL per Hour')
        hour_performance['total_pnl'].plot(kind='bar', ax=axes[1,0], title='Total PnL per Hour')
        hour_performance['win_rate_pct'].plot(kind='bar', ax=axes[1,1], title='Win Rate % per Hour')
        plt.tight_layout()
        plt.savefig(Path(out_dir)/"hourly_detailed.png", dpi=150, bbox_inches='tight')
        plt.close()
        
    if not dow_performance.empty:
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        dow_performance['trade_count'].plot(kind='bar', ax=axes[0,0], title='Trades per Day of Week')
        dow_performance['avg_pnl'].plot(kind='bar', ax=axes[0,1], title='Average PnL per Day')
        dow_performance['total_pnl'].plot(kind='bar', ax=axes[1,0], title='Total PnL per Day')
        dow_performance['win_rate_pct'].plot(kind='bar', ax=axes[1,1], title='Win Rate % per Day')
        plt.tight_layout()
        plt.savefig(Path(out_dir)/"weekday_detailed.png", dpi=150, bbox_inches='tight')
        plt.close()

    # === DRAWDOWN VISUALIZATION ===
    if not equity_curve.empty:
        plt.figure(figsize=(12, 6))
        plt.plot(equity_curve.index, equity_curve.values, label='Equity Curve', linewidth=1)
        plt.plot(equity_curve.index, rolling_max.values, label='Peak Equity', linestyle='--', alpha=0.7)
        plt.fill_between(equity_curve.index, equity_curve.values, rolling_max.values,
                        alpha=0.3, color='red', label='Drawdown')
        plt.title('Equity Curve with Drawdown Visualization')
        plt.xlabel('Trade Index')
        plt.ylabel('Equity')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.savefig(Path(out_dir)/"equity_drawdown.png", dpi=150, bbox_inches='tight')
        plt.close()

    # === ADDITIONAL RISK METRICS ===
    if not equity_curve.empty and len(equity_curve) > 1:
        returns = equity_curve.pct_change().dropna()
        if len(returns) > 0:
            sharpe_approx = returns.mean() / returns.std() * np.sqrt(252) if returns.std() > 0 else 0
            calmar_ratio = (equity_curve.iloc[-1] / equity_curve.iloc[0] - 1) / (max_dd / 100) if max_dd > 0 else np.inf
            
            print(f"\nAdditional Risk Metrics:")
            print(f"Approximate Sharpe Ratio: {sharpe_approx:.3f}")
            print(f"Calmar Ratio: {calmar_ratio:.3f}")
            print(f"Total Return: {((equity_curve.iloc[-1] / equity_curve.iloc[0] - 1) * 100):.2f}%")

    print(f"\nAnalysis plots saved to: {out_dir}/")

    # === TRADE OUTCOME TABLES AND BEST/WORST TRADES ===
    if 'net' in trades_df.columns and not trades_df.empty:
        print("\n--- Trade Outcome Breakdown ---")
        wins = trades_df[trades_df['net'] > 0]
        losses = trades_df[trades_df['net'] < 0]
        print(f"Total Trades: {len(trades_df)}")
        print(f"Wins: {len(wins)}, Avg Win: {wins['net'].mean():.3f}, Largest Win: {wins['net'].max():.3f}")
        print(f"Losses: {len(losses)}, Avg Loss: {losses['net'].mean():.3f}, Largest Loss: {losses['net'].min():.3f}")
        print(f"Win Rate: {len(wins)/len(trades_df)*100:.2f}%" if len(trades_df) > 0 else "N/A")
        # Best/worst trades
        print("\nTop 5 Winning Trades:")
        print(wins.sort_values(by='net', ascending=False).head(5)[['timestep','env_id','timestamp','net','kind','dir']])
        print("\nTop 5 Losing Trades:")
        print(losses.sort_values(by='net', ascending=True).head(5)[['timestep','env_id','timestamp','net','kind','dir']])
        # If "kind" or "reason" col exists, print grouped PnL and count
        if 'kind' in trades_df.columns:
            print("\nTrade Counts and PnL by Kind:")
            print(trades_df.groupby('kind')['net'].agg(['count','sum','mean']).sort_values('count',ascending=False))
        if 'reason' in trades_df.columns:
            print("\nTrade Counts and PnL by Reason:")
            print(trades_df.groupby('reason')['net'].agg(['count','sum','mean']).sort_values('count',ascending=False))

        # Streaks (rudimentary: consecutive wins/losses)
        print("\n--- Streaks ---")
        s = np.sign(trades_df['net'])
        changes = s != s.shift()
        streak_lengths = (changes.cumsum().groupby(s).value_counts())
        print(f"Winning streaks: {streak_lengths.get(1.0,0)}")
        print(f"Losing streaks: {streak_lengths.get(-1.0,0)}")

if __name__ == "__main__":
    analyze_trades_advanced("trades.csv")
