#!/usr/bin/env python3
"""
Test opravy feature corruption problému
"""
import json
import logging

logging.basicConfig(level=logging.INFO)
log = logging.getLogger("FeatureCorruptionFix")

def test_feature_corruption_fix():
    """Test že oprava feature corruption funguje správne"""
    log.info("=== TESTING FEATURE CORRUPTION FIX ===")
    
    # Load config
    with open('strategyConfig_scalp_1s.json', 'r') as f:
        cfg = json.load(f)
    
    # Simulate the fix logic
    config_feature_cols = cfg["envSettings"]["feature_columns"]
    feature_cols = config_feature_cols.copy()  # Make a copy to prevent modification
    
    log.info(f"Config features: {len(config_feature_cols)}")
    log.info(f"Initial feature_cols: {len(feature_cols)}")
    
    # Simulate corruption (like what happens in live trading)
    log.info("\n--- Simulating feature corruption ---")
    
    # This is what happens when calculate_and_merge_indicators adds extra features
    extra_features = [
        'ATR_14_5m', 'VWAP_pta_5m', 'bollinger_bands_lower_20_2.0_5m',
        'bollinger_bands_middle_20_2.0_5m', 'bollinger_bands_upper_20_2.0_5m',
        'bollinger_bands_width_20_2.0_5m', 'ADX_14_5m', 'DMP_14_5m',
        'DMN_14_5m', 'RSI_14_5m', 'EMA_9_5m', 'EMA_21_5m',
        'ob_price_off_l1', 'ob_price_off_l2', 'ob_price_off_l3',
        'ob_price_off_l4', 'ob_price_off_l5', 'extra_feature_1',
        'extra_feature_2', 'extra_feature_3', 'extra_feature_4'
    ]
    
    # Simulate corruption
    corrupted_feature_cols = feature_cols + extra_features
    log.info(f"Corrupted feature_cols: {len(corrupted_feature_cols)} features")
    log.info(f"Extra features added: {len(extra_features)}")
    
    # Test the fix logic
    log.info("\n--- Testing fix logic ---")
    
    # This is the fix from live_trading.py
    if len(corrupted_feature_cols) != len(config_feature_cols):
        log.error(f"❌ CRITICAL: feature_cols was corrupted! {len(corrupted_feature_cols)} vs {len(config_feature_cols)}")
        log.error(f"❌ Resetting feature_cols to config values")
        fixed_feature_cols = config_feature_cols.copy()
        log.info(f"✅ Reset feature_cols to {len(fixed_feature_cols)} features")
    else:
        fixed_feature_cols = corrupted_feature_cols
        log.info(f"✅ No corruption detected")
    
    # Verify the fix
    log.info("\n--- Verification ---")
    log.info(f"Original config: {len(config_feature_cols)} features")
    log.info(f"Fixed feature_cols: {len(fixed_feature_cols)} features")
    
    if len(fixed_feature_cols) == len(config_feature_cols):
        log.info("✅ FIX SUCCESSFUL: Feature count matches config")
        
        # Check if features are the same
        if fixed_feature_cols == config_feature_cols:
            log.info("✅ Feature names are identical to config")
            return True
        else:
            log.warning("⚠️ Feature names differ from config")
            diff = set(fixed_feature_cols) - set(config_feature_cols)
            if diff:
                log.warning(f"   Different features: {diff}")
            return False
    else:
        log.error(f"❌ FIX FAILED: Expected {len(config_feature_cols)}, got {len(fixed_feature_cols)}")
        return False

def test_dimension_calculation():
    """Test výpočtu dimenzií"""
    log.info("\n=== TESTING DIMENSION CALCULATION ===")
    
    # Load config
    with open('strategyConfig_scalp_1s.json', 'r') as f:
        cfg = json.load(f)
    
    config_feature_cols = cfg["envSettings"]["feature_columns"]
    lookback = cfg["envSettings"].get("state_lookback", 30)
    
    # Calculate expected observation space
    expected_obs_size = len(config_feature_cols) * lookback + 11
    
    # Calculate features needed by model
    feats_needed = (expected_obs_size - 11) // lookback
    
    log.info(f"Config features: {len(config_feature_cols)}")
    log.info(f"Lookback: {lookback}")
    log.info(f"Expected obs size: {expected_obs_size}")
    log.info(f"Features needed by model: {feats_needed}")
    
    # Test dimension match
    if len(config_feature_cols) == feats_needed:
        log.info("✅ DIMENSIONS MATCH: No truncation needed")
        return True
    else:
        log.warning(f"⚠️ DIMENSION MISMATCH: Config has {len(config_feature_cols)}, model needs {feats_needed}")
        return False

def main():
    """Main test function"""
    log.info("🧪 TESTING FEATURE CORRUPTION FIX")
    log.info("=" * 60)
    
    # Test corruption fix
    fix_success = test_feature_corruption_fix()
    
    # Test dimension calculation
    dim_success = test_dimension_calculation()
    
    log.info("=" * 60)
    log.info("🏁 TEST RESULTS")
    log.info("=" * 60)
    
    log.info(f"Feature corruption fix: {'✅ PASS' if fix_success else '❌ FAIL'}")
    log.info(f"Dimension calculation: {'✅ PASS' if dim_success else '❌ FAIL'}")
    
    if fix_success and dim_success:
        log.info("\n🎉 ALL TESTS PASSED!")
        log.info("💡 Live trading should now use correct features without corruption.")
    else:
        log.error("\n❌ Some tests failed. Fix needed before live trading.")
    
    return fix_success and dim_success

if __name__ == '__main__':
    success = main()
    exit(0 if success else 1)
