#!/usr/bin/env python3
import pandas as pd
import numpy as np

def analyze_pnl_calculation():
    """Debug PnL calculation for the specific SL exit"""
    
    print("=== DEBUG PNL CALCULATION ===")
    print("From log:")
    print("EXIT 347.5110 u S @ 2.15730 (R: SL, Trig: 2.15666)")
    print("PnL: 3.02, Fee: 0.3748")
    print()
    
    # Known values from log
    position_size = 347.5110
    position = -1  # SHORT
    sim_exit_price = 2.15730
    sl_trigger = 2.15666
    reported_pnl = 3.02
    exit_fee = 0.3748
    
    # PnL formula from code: pnl = position_size * (sim_exit_price - entry_price) * position
    # Rearranging: entry_price = sim_exit_price - (pnl / (position_size * position))
    
    # Calculate what entry_price would give us the reported PnL
    calculated_entry_price = sim_exit_price - (reported_pnl / (position_size * position))
    
    print(f"=== REVERSE CALCULATION ===")
    print(f"Position: SHORT {position_size} units")
    print(f"Exit price: {sim_exit_price}")
    print(f"Reported PnL: ${reported_pnl}")
    print(f"Calculated entry price: {calculated_entry_price:.5f}")
    print()
    
    # Verify the calculation
    pnl_check = position_size * (sim_exit_price - calculated_entry_price) * position
    print(f"Verification: {position_size} * ({sim_exit_price} - {calculated_entry_price:.5f}) * {position} = {pnl_check:.2f}")
    print()
    
    # Analyze if this makes sense
    print("=== LOGIC CHECK ===")
    print(f"Entry price: {calculated_entry_price:.5f}")
    print(f"Exit price:  {sim_exit_price}")
    print(f"SL trigger:  {sl_trigger}")
    print()
    
    if calculated_entry_price < sim_exit_price:
        print("❌ LOGIC ERROR: For SHORT position, entry price should be HIGHER than exit price for profit")
        print("   Entry < Exit means cena šla HORE = STRATA pre SHORT, nie zisk!")
    else:
        print("✅ Logic OK: Entry > Exit means profit for SHORT position")
    
    print()
    print("=== SL LOGIC CHECK ===")
    if calculated_entry_price > sl_trigger:
        expected_sl = calculated_entry_price + 0.01  # Assume some SL distance
        print(f"Expected SL should be: {expected_sl:.5f} (above entry)")
        print(f"Actual SL trigger:     {sl_trigger}")
        if sl_trigger < calculated_entry_price:
            print("❌ SL ERROR: SL trigger is BELOW entry price for SHORT position!")
            print("   This would cause immediate SL hit upon entry")
    
    print()
    print("=== POSSIBLE ISSUES ===")
    print("1. Entry price calculation error during position opening")
    print("2. SL price calculation error") 
    print("3. PnL calculation using wrong entry_price")
    print("4. Data quality issue at the time of entry")
    
    # Let's also check what the PnL should be if it was a LOSS
    print()
    print("=== EXPECTED LOSS SCENARIO ===")
    # For SHORT: if price went from entry to exit UP, it should be a loss
    # If we assume entry was lower than exit (which is what the data suggests)
    expected_loss = -(sim_exit_price - calculated_entry_price) * position_size
    print(f"If entry was {calculated_entry_price:.5f} and exit was {sim_exit_price}")
    print(f"Expected LOSS should be: ${expected_loss:.2f}")
    print(f"But reported PnL is: ${reported_pnl:.2f} (GAIN)")
    print()
    print("❌ CONCLUSION: PnL calculation má OPAČNÉ ZNAMIENKO!")

if __name__ == "__main__":
    analyze_pnl_calculation()