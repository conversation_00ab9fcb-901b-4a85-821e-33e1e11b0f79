from __future__ import annotations
import csv, math
from pathlib import Path
from collections import deque
from statistics import mean, pstdev

from stable_baselines3.common.callbacks import BaseCallback


class TradeLoggerCallback(BaseCallback):
    """
    Loguje každú udalosť poslanú z prostredia (`trade_event` alebo
    `last_episode_log`) do CSV a zároveň publikuje zhrnuté metriky na TB.
    """

    # základné pole + r<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON>nv vie posielať
    BASE = [
        "timestep", "env_id", "timestamp", "kind", "dir",
        "price", "price_exec", "entry", "sl", "tp",
        "pos", "size", "pnl", "fee", "net",
        "equity", "equity_total", "equity_ep", "idx", "same_bar",
        "r_multiple", "daily_pnl", "daily_risk_used",
        "initial_risk_points", "initial_reward_points",
        "initial_risk_pct", "initial_reward_pct", "risk_reward_ratio",
        "max_equity", "current_equity", "drawdown_pct",
        "equity_diff", "filtered"
    ]

    def __init__(self, csv_path: str | Path = "trades.csv", window: int = 500, append: bool = True, initial_equity: float = 100.0):
        super().__init__()
        self.csv_path = Path(csv_path)
        self.win = window            # veľkosť sliding okna na metriky
        self.append = append         # Whether to append to existing file
        self.initial_equity = initial_equity  # Store initial equity for micro-trade detection

        self.csv_file = None
        self.writer: csv.DictWriter | None = None
        self.last_equity: dict[int, float] = {}  # podľa env‑id

        # sliding okná pre metriky
        self.diff_buf_filt: deque[float] = deque(maxlen=window)
        self.diff_buf_full: deque[float] = deque(maxlen=window)
        self.rmult_buf:    deque[float] = deque(maxlen=window)

        # agregáty
        self.total_filt = self.wins_filt = 0
        self.total_full = self.wins_full = 0

    # ───────────────────────── CSV ───────────────────────────────────────
    def _on_training_start(self):
        self.csv_path.parent.mkdir(parents=True, exist_ok=True)

        # Reset last_equity to avoid incorrect equity_diff calculations
        self.last_equity = {}

        # Determine if we need to write headers (new file or overwrite mode)
        file_exists = self.csv_path.exists() and self.csv_path.stat().st_size > 0
        write_header = not (file_exists and self.append)

        # Open file in append mode if requested and file exists
        mode = "a" if self.append and file_exists else "w"
        self.csv_file = self.csv_path.open(mode, newline="", buffering=1)  # Line buffering

        self.writer = csv.DictWriter(
            self.csv_file,
            fieldnames=self.BASE,
            extrasaction="ignore"
        )

        # Only write header for new files or when overwriting
        if write_header:
            self.writer.writeheader()

    def _write(self, env_id: int, ev: dict) -> None:
        # --- equity diff --------------------------------------------------
        # Reset last_equity when a new OPEN event is detected to avoid incorrect equity_diff
        if ev.get("kind") == "OPEN":
            # Use current equity as the baseline for the new trade
            self.last_equity[env_id] = ev.get("equity_total", 0.0)
            eq_prev = self.last_equity[env_id]
            eq_curr = eq_prev
            eq_diff = 0.0  # No diff on OPEN
        else:
            # Normal equity diff calculation for non-OPEN events
            eq_prev = self.last_equity.get(env_id, ev.get("equity_total", 0.0))
            eq_curr = ev.get("equity_total", eq_prev)
            eq_diff = eq_curr - eq_prev
            self.last_equity[env_id] = eq_curr

        # Calculate micro-trade threshold based on current equity
        # Use a percentage of current equity instead of fixed initial equity
        current_equity = max(eq_curr, self.initial_equity)
        THR = 2e-4 * current_equity
        # Don't mark OPEN trades as micro - only apply to CLOSE trades
        is_micro = ev.get("kind") != "OPEN" and abs(ev.get("net", 0.0)) < THR

        # full záznam
        ev_full = {"timestep": self.num_timesteps,
                   "env_id": env_id,
                   "equity_diff": eq_diff,
                   "filtered": is_micro}
        ev_full.update(ev)

        # --- zápis --------------------------------------------------------
        row = {k: ev_full.get(k, "") for k in self.writer.fieldnames}
        self.writer.writerow(row)
        # No need to flush with line buffering (buffering=1)

        # --- rolling metriky ---------------------------------------------
        if ev["kind"].startswith("CLOSE"):
            # FULL (ne‑filtrované)
            self.diff_buf_full.append(eq_diff)
            self.total_full += 1
            if ev.get("net", 0.0) > 0: self.wins_full += 1

            # R‑multiple
            if "r_multiple" in ev and ev["r_multiple"] is not None:
                self.rmult_buf.append(ev["r_multiple"])

            # FILTROVANÉ (bez mikro‑tradeov)
            if not is_micro:
                self.diff_buf_filt.append(eq_diff)
                self.total_filt += 1
                if ev.get("net", 0.0) > 0: self.wins_filt += 1

    # ─────────────────────── STEP / ROLLOUT ───────────────────────────────
    def _on_step(self) -> bool:
        # spracujeme všetky env infos
        for env_id, info in enumerate(self.locals.get("infos", [])):
            # realtime trade_event (aktuálny bar)
            if "trade_event" in info:
                self._write(env_id, info["trade_event"])

            # log celej epizódy, ak bola v reset info
            if "last_episode_log" in info and info["last_episode_log"]:
                for ev in info["last_episode_log"]:
                    self._write(env_id, ev)
        # manuálne spustíme logging metrik z _on_rollout_end(),
        # pretože SAC nevolá rollout_end.
        self._on_rollout_end()
        return True

    def _on_rollout_end(self):
        # --- helper
        def _sharpe(buf: deque[float]) -> float | None:
            if len(buf) < 5: return None
            m, sd = mean(buf), pstdev(buf)
            return m / sd * math.sqrt(len(buf)) if sd else None

        # metriky (filtrované)
        if self.total_filt:
            self.logger.record("custom/win_rate_filtered",
                               self.wins_filt / self.total_filt)
        if self.diff_buf_filt:
            self.logger.record("custom/equity_diff_mean_filtered",
                               mean(self.diff_buf_filt))
            self.logger.record("custom/equity_diff_sum_filtered",
                               sum(self.diff_buf_filt))
            s = _sharpe(self.diff_buf_filt)
            if s is not None:
                self.logger.record("custom/sharpe_filtered", s)

        # metriky (full)
        if self.total_full:
            self.logger.record("custom/win_rate_full",
                               self.wins_full / self.total_full)
        if self.diff_buf_full:
            self.logger.record("custom/equity_diff_mean_full",
                               mean(self.diff_buf_full))
            self.logger.record("custom/equity_diff_sum_full",
                               sum(self.diff_buf_full))
            s = _sharpe(self.diff_buf_full)
            if s is not None:
                self.logger.record("custom/sharpe_full", s)

        # R‑multiple
        if self.rmult_buf:
            self.logger.record("custom/avg_r_multiple",
                               mean(self.rmult_buf))

        # počty
        self.logger.record("custom/trade_count_filtered", self.total_filt)
        self.logger.record("custom/trade_count_full", self.total_full)
        self.logger.record("custom/micro_trade_ratio",
                           (self.total_full - self.total_filt) /
                           max(1, self.total_full))

    def _on_training_end(self):
        if self.csv_file:
            self.csv_file.close()
