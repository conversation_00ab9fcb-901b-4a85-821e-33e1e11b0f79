#!/usr/bin/env python3
"""
Test finálnej opravy feature corruption problému
"""
import json
import logging

logging.basicConfig(level=logging.INFO)
log = logging.getLogger("FinalFeatureFix")

def test_immutable_feature_cols():
    """Test že EXPECTED_FEATURE_COLS je immutable"""
    log.info("=== TESTING IMMUTABLE FEATURE COLS ===")
    
    # Load config
    with open('strategyConfig_scalp_1s.json', 'r') as f:
        cfg = json.load(f)
    
    # Simulate the new approach
    EXPECTED_FEATURE_COLS = cfg["envSettings"]["feature_columns"]  # Immutable reference
    feature_cols = EXPECTED_FEATURE_COLS.copy()  # Working copy
    
    log.info(f"EXPECTED_FEATURE_COLS: {len(EXPECTED_FEATURE_COLS)} features")
    log.info(f"Initial feature_cols: {len(feature_cols)} features")
    
    # Simulate corruption attempts
    log.info("\n--- Testing corruption resistance ---")
    
    # Try to corrupt feature_cols
    feature_cols.extend(['extra_1', 'extra_2', 'extra_3'])
    log.info(f"After corruption attempt: feature_cols = {len(feature_cols)} features")
    log.info(f"EXPECTED_FEATURE_COLS unchanged: {len(EXPECTED_FEATURE_COLS)} features")
    
    # Reset from immutable reference
    feature_cols = EXPECTED_FEATURE_COLS.copy()
    log.info(f"After reset: feature_cols = {len(feature_cols)} features")
    
    # Verify immutability
    if len(feature_cols) == len(EXPECTED_FEATURE_COLS):
        log.info("✅ IMMUTABILITY TEST PASSED: feature_cols can be reset from EXPECTED_FEATURE_COLS")
        return True
    else:
        log.error("❌ IMMUTABILITY TEST FAILED")
        return False

def test_dimension_consistency():
    """Test konzistencie dimenzií"""
    log.info("\n=== TESTING DIMENSION CONSISTENCY ===")
    
    # Load config
    with open('strategyConfig_scalp_1s.json', 'r') as f:
        cfg = json.load(f)
    
    EXPECTED_FEATURE_COLS = cfg["envSettings"]["feature_columns"]
    lookback = cfg["envSettings"].get("state_lookback", 30)
    
    # Calculate expected observation space
    expected_obs_size = len(EXPECTED_FEATURE_COLS) * lookback + 11
    feats_needed = (expected_obs_size - 11) // lookback
    
    log.info(f"EXPECTED_FEATURE_COLS: {len(EXPECTED_FEATURE_COLS)} features")
    log.info(f"Lookback: {lookback}")
    log.info(f"Expected obs size: {expected_obs_size}")
    log.info(f"Features needed by model: {feats_needed}")
    
    # Test consistency
    if len(EXPECTED_FEATURE_COLS) == feats_needed:
        log.info("✅ DIMENSION CONSISTENCY PASSED: No truncation needed")
        return True
    else:
        log.warning(f"⚠️ DIMENSION MISMATCH: Config has {len(EXPECTED_FEATURE_COLS)}, model needs {feats_needed}")
        
        # Test truncation logic
        if len(EXPECTED_FEATURE_COLS) > feats_needed:
            selected_features = EXPECTED_FEATURE_COLS[:feats_needed]
            log.info(f"Would truncate to first {feats_needed} features")
            log.info(f"Selected: {selected_features[:3]}...{selected_features[-3:]}")
            return True
        else:
            log.error("❌ Too few features in config")
            return False

def test_feature_selection_logic():
    """Test logiky výberu features"""
    log.info("\n=== TESTING FEATURE SELECTION LOGIC ===")
    
    # Load config
    with open('strategyConfig_scalp_1s.json', 'r') as f:
        cfg = json.load(f)
    
    EXPECTED_FEATURE_COLS = cfg["envSettings"]["feature_columns"]
    
    # Simulate base_df with extra features (like from calculate_and_merge_indicators)
    extra_features = [
        'ATR_14_5m', 'VWAP_pta_5m', 'bollinger_bands_lower_20_2.0_5m',
        'bollinger_bands_middle_20_2.0_5m', 'bollinger_bands_upper_20_2.0_5m',
        'bollinger_bands_width_20_2.0_5m', 'ADX_14_5m', 'DMP_14_5m',
        'DMN_14_5m', 'RSI_14_5m', 'EMA_9_5m', 'EMA_21_5m',
        'ob_price_off_l1', 'ob_price_off_l2', 'ob_price_off_l3',
        'ob_price_off_l4', 'ob_price_off_l5'
    ]
    
    all_features = EXPECTED_FEATURE_COLS + extra_features
    log.info(f"Simulated base_df.columns: {len(all_features)} features")
    
    # Test selection logic: base_df = base_df[EXPECTED_FEATURE_COLS]
    selected_features = [col for col in EXPECTED_FEATURE_COLS if col in all_features]
    log.info(f"Selected features: {len(selected_features)} features")
    
    # Verify selection
    if len(selected_features) == len(EXPECTED_FEATURE_COLS):
        log.info("✅ FEATURE SELECTION PASSED: All expected features selected")
        
        # Check if they're the same
        if selected_features == EXPECTED_FEATURE_COLS:
            log.info("✅ Feature order preserved")
            return True
        else:
            log.warning("⚠️ Feature order changed")
            return False
    else:
        log.error(f"❌ FEATURE SELECTION FAILED: Expected {len(EXPECTED_FEATURE_COLS)}, got {len(selected_features)}")
        return False

def main():
    """Main test function"""
    log.info("🧪 TESTING FINAL FEATURE FIX")
    log.info("=" * 60)
    
    # Run tests
    test1 = test_immutable_feature_cols()
    test2 = test_dimension_consistency()
    test3 = test_feature_selection_logic()
    
    log.info("=" * 60)
    log.info("🏁 FINAL TEST RESULTS")
    log.info("=" * 60)
    
    log.info(f"Immutable feature cols: {'✅ PASS' if test1 else '❌ FAIL'}")
    log.info(f"Dimension consistency: {'✅ PASS' if test2 else '❌ FAIL'}")
    log.info(f"Feature selection logic: {'✅ PASS' if test3 else '❌ FAIL'}")
    
    all_passed = test1 and test2 and test3
    
    if all_passed:
        log.info("\n🎉 ALL TESTS PASSED!")
        log.info("💡 Live trading should now use exactly 48 features without corruption.")
        log.info("🚀 No more 'FEATURE DIMENSION MISMATCH' warnings expected!")
    else:
        log.error("\n❌ Some tests failed. Further fixes needed.")
    
    return all_passed

if __name__ == '__main__':
    success = main()
    exit(0 if success else 1)
