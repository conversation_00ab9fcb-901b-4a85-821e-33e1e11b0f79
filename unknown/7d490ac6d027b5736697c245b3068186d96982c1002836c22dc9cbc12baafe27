#!/usr/bin/env python3
from __future__ import annotations
import argparse, os, sys, logging, concurrent.futures as f
from pathlib import Path
from typing import Dict, List, Any

import msgpack
import numpy as np
import pandas as pd
import pyarrow as pa
import pyarrow.parquet as pq

logging.basicConfig(level=logging.INFO,
                    format="%(asctime)s %(levelname)s %(name)s: %(message)s")
log = logging.getLogger("convert")

CANDLE_COLS = {
    "timestamp": "time_period_start",
    "open": "price_open",
    "high": "price_high",
    "low": "price_low",
    "close": "price_close",
    "volume": "volume_traded",
    "buy_volume": "buy_volume",
    "sell_volume": "sell_volume",
    "trade_count": "trades_count",
    "vwap": "vwap",
}

TRADE_COLS = {
    "timestamp": "time_exchange",
    "price": "price",
    "quantity": "size",
    "side": "taker_side",
    "is_buyer_maker": "is_buyer_maker",
}

def _to_ts(x: Any) -> pd.Timestamp | None:
    try:
        ts = pd.to_datetime(x, utc=True)
        if pd.isna(ts):
            return None
        return ts
    except Exception:
        return None


def _process_one(fp: Path, dst: Path) -> None:
    date_str = fp.stem
    log.debug("Spracovávam %s", date_str)

    probe = dst / "ohlcv" / "1m" / f"{date_str}.parquet"
    if probe.exists():
        log.debug("%s → preskakujem (už existuje)", date_str)
        return

    try:
        with fp.open("rb") as f_in:
            raw: Dict = msgpack.unpackb(f_in.read(), raw=False)
    except Exception as e:
        log.error("%s → Chyba pri čítaní alebo rozbaľovaní msgpack: %s", date_str, e)
        return

    # ---- OHLCV -----------------------------------------------------
    ohlcv_data = raw.get("ohlcv")
    if isinstance(ohlcv_data, dict):
        for tf, lst in ohlcv_data.items():
            if not isinstance(lst, list) or not lst:
                continue
            rec = []
            for r in lst:
                 if not isinstance(r, dict) or CANDLE_COLS["timestamp"] not in r:
                     continue
                 rec.append({dst_c: r.get(src_c) for dst_c, src_c in CANDLE_COLS.items()})
            if not rec:
                continue
            try:
                df = pd.DataFrame(rec)
                df["timestamp"] = pd.to_datetime(df["timestamp"], utc=True, errors="coerce")
                df = df.dropna(subset=["timestamp"]).set_index("timestamp").sort_index()
                if df.empty: continue
                out_dir = dst / "ohlcv" / tf
                out_dir.mkdir(parents=True, exist_ok=True)
                table = pa.Table.from_pandas(df, preserve_index=True)
                pq.write_table(table, out_dir / f"{date_str}.parquet", compression="zstd")
            except Exception as e:
                log.error("%s → Chyba pri spracovaní OHLCV pre %s: %s", date_str, tf, e)

    # ---- TRADES ----------------------------------------------------
    trades_data = raw.get("trades")
    if isinstance(trades_data, list) and trades_data:
        rec = []
        for r in trades_data:
            if not isinstance(r, dict) or TRADE_COLS["timestamp"] not in r:
                continue
            # Handle potential missing is_buyer_maker or logic based on taker_side
            item_data = {dst_c: r.get(src_c) for dst_c, src_c in TRADE_COLS.items() if src_c != "is_buyer_maker"}
            if "is_buyer_maker" in r: # Prefer explicit field if exists
                 item_data["is_buyer_maker"] = r.get("is_buyer_maker")
            elif "taker_side" in r: # Fallback to taker_side logic
                 taker_side = r.get("taker_side")
                 if taker_side == 'BUY':
                     item_data["is_buyer_maker"] = False
                 elif taker_side == 'SELL':
                     item_data["is_buyer_maker"] = True
                 else:
                     item_data["is_buyer_maker"] = None # Or some default/error value
            else:
                 item_data["is_buyer_maker"] = None # Cannot determine

            rec.append(item_data)

        if rec:
            try:
                df = pd.DataFrame(rec)
                df["timestamp"] = pd.to_datetime(df["timestamp"], utc=True, errors="coerce")
                df = df.dropna(subset=["timestamp"]).set_index("timestamp").sort_index()
                if df.empty: raise ValueError("DataFrame je prázdny po spracovaní Trades")
                out_dir = dst / "trades"
                out_dir.mkdir(parents=True, exist_ok=True)
                table = pa.Table.from_pandas(df, preserve_index=True)
                pq.write_table(table, out_dir / f"{date_str}.parquet", compression="zstd")
            except Exception as e:
                log.error("%s → Chyba pri spracovaní Trades: %s", date_str, e)

    # ---- ORDERBOOKS (Upravené) -------------------------------------
    orderbooks_data = raw.get("orderbooks")
    if isinstance(orderbooks_data, list) and orderbooks_data:
        rec = []
        MAX_LEVELS = 5
        for snap in orderbooks_data:
            if not isinstance(snap, dict): continue
            ts = _to_ts(snap.get("time_exchange"))
            bids_raw = snap.get("bids")
            asks_raw = snap.get("asks")
            if ts is None or not isinstance(bids_raw, list) or not isinstance(asks_raw, list) or not bids_raw or not asks_raw:
                continue

            try:
                bids = sorted([item for item in bids_raw if isinstance(item, dict) and 'price' in item],
                              key=lambda x: x.get('price', -float('inf')), reverse=True)
                asks = sorted([item for item in asks_raw if isinstance(item, dict) and 'price' in item],
                              key=lambda x: x.get('price', float('inf')))
            except Exception as e:
                log.warning("%s → Chyba pri triedení bids/asks v snapshote %s: %s", date_str, ts, e)
                continue

            if not bids or not asks: # Check again after potential filtering/sorting errors
                 continue

            record = {'timestamp': ts}
            for i in range(1, MAX_LEVELS + 1):
                bid_level_data = bids[i-1] if i <= len(bids) else None
                ask_level_data = asks[i-1] if i <= len(asks) else None

                record[f'bid_px{i}'] = bid_level_data.get('price') if bid_level_data else np.nan
                record[f'bid_qty{i}'] = bid_level_data.get('size') if bid_level_data else np.nan
                record[f'ask_px{i}'] = ask_level_data.get('price') if ask_level_data else np.nan
                record[f'ask_qty{i}'] = ask_level_data.get('size') if ask_level_data else np.nan

            rec.append(record)

        if rec:
            try:
                df = pd.DataFrame(rec)
                for i in range(1, MAX_LEVELS + 1):
                     df[f'bid_px{i}'] = pd.to_numeric(df[f'bid_px{i}'], errors='coerce')
                     df[f'bid_qty{i}'] = pd.to_numeric(df[f'bid_qty{i}'], errors='coerce')
                     df[f'ask_px{i}'] = pd.to_numeric(df[f'ask_px{i}'], errors='coerce')
                     df[f'ask_qty{i}'] = pd.to_numeric(df[f'ask_qty{i}'], errors='coerce')

                df = df.set_index("timestamp").sort_index()
                if df.empty: raise ValueError("DataFrame je prázdny po spracovaní Orderbooks")

                out_dir = dst / "orderbooks"
                out_dir.mkdir(parents=True, exist_ok=True)
                table = pa.Table.from_pandas(df, preserve_index=True)
                pq.write_table(table, out_dir / f"{date_str}.parquet", compression="zstd")
            except Exception as e:
                log.error("%s → Chyba pri spracovaní Orderbooks: %s", date_str, e)

    log.info("%s → OK", date_str)


def parse_date(date_str: str) -> str:
    """Parse date string and return in YYYY-MM-DD format."""
    try:
        # Try to parse as YYYY-MM-DD
        if len(date_str) == 10 and date_str[4] == '-' and date_str[7] == '-':
            return date_str

        # Try to parse as DD.MM.YYYY
        if len(date_str) == 10 and date_str[2] == '.' and date_str[5] == '.':
            day, month, year = date_str.split('.')
            return f"{year}-{month}-{day}"

        # Try to parse as YYYYMMDD
        if len(date_str) == 8 and date_str.isdigit():
            return f"{date_str[:4]}-{date_str[4:6]}-{date_str[6:8]}"

        raise ValueError(f"Unsupported date format: {date_str}")
    except Exception as e:
        log.error(f"Error parsing date {date_str}: {e}")
        raise ValueError(f"Invalid date format: {date_str}")

def main() -> None:
    p = argparse.ArgumentParser(description="Convert msgpack files to parquet format")
    p.add_argument("--src", required=True, help="adresár s *.msgpack")
    p.add_argument("--dst", required=True, help="výstupný adresár pre .parquet")
    p.add_argument("--workers", type=int, default=os.cpu_count() or 4,
                   help="počet paralelných workerov")

    # Add date selection options
    date_group = p.add_argument_group('Date Selection')
    date_group.add_argument("--dates", nargs="*", help="konkrétne dátumy na konverziu (YYYY-MM-DD, DD.MM.YYYY, alebo YYYYMMDD)")
    date_group.add_argument("--start-date", help="počiatočný dátum rozsahu (YYYY-MM-DD, DD.MM.YYYY, alebo YYYYMMDD)")
    date_group.add_argument("--end-date", help="koncový dátum rozsahu (YYYY-MM-DD, DD.MM.YYYY, alebo YYYYMMDD)")
    date_group.add_argument("--all", action="store_true", help="konvertovať všetky súbory (predvolené ak nie sú zadané dátumy)")

    args = p.parse_args()

    src_dir = Path(args.src).expanduser()
    dst_dir = Path(args.dst).expanduser()
    dst_dir.mkdir(parents=True, exist_ok=True)

    # Get all available msgpack files
    all_files = sorted(src_dir.glob("*.msgpack"))
    if not all_files:
        log.error("V %s nie sú žiadne .msgpack súbory", src_dir)
        sys.exit(1)

    # Filter files based on date selection
    selected_files = []

    # If specific dates are provided
    if args.dates:
        date_set = set()
        for date_str in args.dates:
            try:
                parsed_date = parse_date(date_str)
                date_set.add(parsed_date)
            except ValueError as e:
                log.warning(f"{e}")

        selected_files = [f for f in all_files if f.stem in date_set]
        if not selected_files:
            log.warning("Žiadne súbory nenájdené pre zadané dátumy")

    # If date range is provided
    elif args.start_date or args.end_date:
        try:
            start_date = parse_date(args.start_date) if args.start_date else "0000-00-00"
            end_date = parse_date(args.end_date) if args.end_date else "9999-99-99"

            selected_files = [f for f in all_files if start_date <= f.stem <= end_date]
            if not selected_files:
                log.warning(f"Žiadne súbory nenájdené v rozsahu {start_date} až {end_date}")
        except ValueError as e:
            log.error(f"{e}")
            sys.exit(1)

    # If no date selection is provided or --all is specified, use all files
    if not selected_files or args.all:
        selected_files = all_files
        log.info("Konvertujem všetky súbory")

    if not selected_files:
        log.error("Žiadne súbory na konverziu")
        sys.exit(1)

    log.info("Konvertujem %d súborov → %s (workers=%d)",
             len(selected_files), dst_dir, args.workers)

    # Print selected files for confirmation
    if len(selected_files) <= 10:
        log.info("Vybrané súbory: %s", ", ".join(f.stem for f in selected_files))
    else:
        log.info("Vybrané súbory: %s ... %s (celkom %d)",
                 ", ".join(f.stem for f in selected_files[:5]),
                 ", ".join(f.stem for f in selected_files[-5:]),
                 len(selected_files))

    with f.ThreadPoolExecutor(max_workers=args.workers) as ex:
        results = list(ex.map(lambda fp: _process_one(fp, dst_dir), selected_files))

    log.info("HOTOVO.")

if __name__ == "__main__":
    main()