import math
import os
import argparse
import logging
from pathlib import Path
from datetime import datetime, timedelta, timezone
import yaml
import json
import pandas as pd
import numpy as np
import pyarrow.parquet as pq
from stable_baselines3 import SAC
from stable_baselines3.common.vec_env import VecNormalize
from popart_sac import PopArtSAC
import itertools
from copy import deepcopy

# Import funkcii zo simulate_trading_new.py
from simulate_trading_new import (
    load_config, load_backtest_data, get_state, run_backtest_original,
    calculate_metrics, prepare_df_for_simulation, custom_objects, log
)

def generate_tsl_parameter_combinations():
    """
    Generuje kombinácie parametrov špecificky pre trailing stop loss testing.
    Fokusuje sa na activateATRMultiplier a trailATRMultiplier.
    """
    # Definícia hodnôt pre TSL testovanie
    activate_atr_multiplier_values = [0.2, 0.3, 0.5, 0.8, 1.0, 1.2, 1.5]
    trail_atr_multiplier_values = [0.15, 0.25, 0.35, 0.5, 0.65, 0.8, 1.0]
    
    combinations = []
    
    # Generovanie v<PERSON><PERSON><PERSON><PERSON><PERSON> kombinácií TSL parametrov
    for activate_atr in activate_atr_multiplier_values:
        for trail_atr in trail_atr_multiplier_values:
            combinations.append({
                'activateATRMultiplier': activate_atr,
                'trailATRMultiplier': trail_atr
            })
    
    log.info(f"Vygenerovaných {len(combinations)} kombinácií TSL parametrov")
    log.info(f"activateATRMultiplier hodnoty: {activate_atr_multiplier_values}")
    log.info(f"trailATRMultiplier hodnoty: {trail_atr_multiplier_values}")
    
    return combinations

def simulate_single_trade_with_tsl_params(trade_setup, tsl_params, config_base, data_1s):
    """
    Simuluje jeden obchod s konkrétnou kombináciou TSL parametrov.
    
    Args:
        trade_setup: Dictionary s informáciami o obchode (entry_time, direction, etc.)
        tsl_params: Dictionary s TSL parametrami na testovanie
        config_base: Základná konfigurácia
        data_1s: 1s OHLCV dáta pre simuláciu
        
    Returns:
        Dictionary s výsledkami simulácie
    """
    # Vytvorenie kópie konfigurácie s novými TSL parametrami
    config = deepcopy(config_base)
    
    # Aplikovanie nových TSL parametrov
    config['trailingStopLoss']['activateATRMultiplier'] = tsl_params['activateATRMultiplier']
    config['trailingStopLoss']['trailATRMultiplier'] = tsl_params['trailATRMultiplier']
    
    # Načítanie parametrov pre simuláciu
    entry_time = trade_setup['entry_time']
    direction = trade_setup['direction']
    entry_price = trade_setup['entry_price']
    current_atr = trade_setup.get('current_atr', 0.001)  # Default fallback ATR
    position = 1 if direction == 'Long' else -1
    
    # Výpočet SL a TP s pôvodnými parametrami (iba TSL sa mení)
    trade_params = config.get('tradeParams', {})
    fee_perc = trade_params.get('feePercentage', 0.0) / 100.0
    slippage_perc = trade_params.get('slippagePercentage', 0.0) / 100.0
    min_sl_dist_perc = trade_params.get('minSLDistancePercent', 2.0) / 100.0
    min_sl_dist_atr_mult = trade_params.get('minSLDistanceATR', 2.5)
    rr_target = trade_params.get('rrTarget', 2.5)
    
    # Výpočet SL distance
    sl_dist_perc_points = entry_price * min_sl_dist_perc
    sl_dist_atr_points = current_atr * min_sl_dist_atr_mult if current_atr > 0 else 0
    min_sl_dist_points = max(sl_dist_perc_points, sl_dist_atr_points)
    
    if min_sl_dist_points <= 1e-9:
        min_sl_dist_points = entry_price * 0.005  # 0.5% fallback
    
    # Výpočet SL a TP cien
    sl_price = entry_price - min_sl_dist_points if position == 1 else entry_price + min_sl_dist_points
    tp_dist_points = min_sl_dist_points * rr_target
    tp_price = entry_price + tp_dist_points if position == 1 else entry_price - tp_dist_points
    
    # TSL parametry z nových hodnôt
    tsl_config = config.get('trailingStopLoss', {})
    tsl_enabled = tsl_config.get('enabled', False)
    tsl_activate_atr_mult = tsl_params['activateATRMultiplier']
    tsl_trail_atr_mult = tsl_params['trailATRMultiplier']
    
    # Simulácia obchodu s 1s dátami
    entry_fee = abs(trade_setup.get('size', 1.0) * entry_price * fee_perc)
    current_sl_price = sl_price
    current_tp_price = tp_price
    trailing_active = False
    peak_price_in_trade = entry_price
    max_profit = 0.0
    max_drawdown = 0.0
    
    # Trackujeme TSL aktiváciu a updates
    tsl_activation_time = None
    tsl_updates = 0
    max_r_multiple = 0.0
    
    # Nájdenie entry time v 1s dátach a simulácia od tohto bodu
    try:
        if entry_time not in data_1s.index:
            # Nájdi najbližší dostupný timestamp
            available_times = data_1s.index[data_1s.index >= entry_time]
            if len(available_times) == 0:
                # Ak nie sú dáta dostupné, vrátime neutral result
                return create_neutral_result(trade_setup, tsl_params, entry_price, entry_fee, 'NO_DATA')
            entry_time = available_times[0]
        
        # Získanie 1s dát od entry_time ďalej (max 4 hodiny alebo do konca dát)
        end_time = min(entry_time + pd.Timedelta(hours=4), data_1s.index.max())
        trade_data = data_1s.loc[entry_time:end_time]
        
        if len(trade_data) == 0:
            return create_neutral_result(trade_setup, tsl_params, entry_price, entry_fee, 'NO_DATA')
        
        # Simulácia tick-by-tick
        for current_time, row in trade_data.iterrows():
            current_high = row['high']
            current_low = row['low']
            current_close = row['close']
            
            # Aktuálny P&L a tracking max profit/drawdown
            current_pnl = (current_close - entry_price) * position
            if current_pnl > max_profit:
                max_profit = current_pnl
            if current_pnl < max_drawdown:
                max_drawdown = current_pnl
            
            # Kontrola SL/TP hit
            exit_reason = None
            exit_price = None
            
            if position == 1:  # Long
                hit_sl = current_sl_price > 0 and current_low <= current_sl_price
                hit_tp = current_tp_price > 0 and current_high >= current_tp_price
                
                # SL má prioritu nad TP
                if hit_sl and hit_tp:
                    hit_tp = False
                
                if hit_sl:
                    exit_reason = "TSL" if trailing_active else "SL"
                    exit_price = current_sl_price
                elif hit_tp:
                    exit_reason = "TP"
                    exit_price = current_tp_price
                
                # TSL update pre Long
                if not exit_reason and tsl_enabled and current_atr > 0:
                    initial_risk = abs(entry_price - sl_price)
                    profit = current_high - entry_price
                    r_mult = profit / initial_risk if initial_risk > 1e-9 else 0
                    
                    # Track max R-multiple
                    if r_mult > max_r_multiple:
                        max_r_multiple = r_mult
                    
                    if not trailing_active and r_mult >= tsl_activate_atr_mult:
                        trailing_active = True
                        tsl_activation_time = current_time
                        current_sl_price = current_high - tsl_trail_atr_mult * current_atr
                        peak_price_in_trade = current_high
                        log.debug(f"TSL activated at {r_mult:.2f}R for {direction} trade")
                    elif trailing_active:
                        new_sl = current_high - tsl_trail_atr_mult * current_atr
                        if new_sl > current_sl_price:
                            current_sl_price = new_sl
                            tsl_updates += 1
                
            else:  # Short
                hit_sl = current_sl_price > 0 and current_high >= current_sl_price
                hit_tp = current_tp_price > 0 and current_low <= current_tp_price
                
                # SL má prioritu nad TP
                if hit_sl and hit_tp:
                    hit_tp = False
                
                if hit_sl:
                    exit_reason = "TSL" if trailing_active else "SL"
                    exit_price = current_sl_price
                elif hit_tp:
                    exit_reason = "TP"
                    exit_price = current_tp_price
                
                # TSL update pre Short
                if not exit_reason and tsl_enabled and current_atr > 0:
                    initial_risk = abs(entry_price - sl_price)
                    profit = entry_price - current_low
                    r_mult = profit / initial_risk if initial_risk > 1e-9 else 0
                    
                    # Track max R-multiple
                    if r_mult > max_r_multiple:
                        max_r_multiple = r_mult
                    
                    if not trailing_active and r_mult >= tsl_activate_atr_mult:
                        trailing_active = True
                        tsl_activation_time = current_time
                        current_sl_price = current_low + tsl_trail_atr_mult * current_atr
                        peak_price_in_trade = current_low
                        log.debug(f"TSL activated at {r_mult:.2f}R for {direction} trade")
                    elif trailing_active:
                        new_sl = current_low + tsl_trail_atr_mult * current_atr
                        if new_sl < current_sl_price:
                            current_sl_price = new_sl
                            tsl_updates += 1
            
            # Ak bol hit SL/TP, ukončujeme obchod
            if exit_reason:
                sim_exit_price = exit_price * (1 - slippage_perc * position)
                pnl = (sim_exit_price - entry_price) * position
                exit_fee = abs(trade_setup.get('size', 1.0) * sim_exit_price * fee_perc)
                net_pnl = pnl - entry_fee - exit_fee
                time_in_trade = (current_time - entry_time).total_seconds()
                
                return {
                    'trade_id': trade_setup.get('trade_id', 0),
                    'entry_time': entry_time,
                    'direction': direction,
                    'entry_price': entry_price,
                    'exit_time': current_time,
                    'exit_price': sim_exit_price,
                    'pnl': net_pnl,
                    'exit_reason': exit_reason,
                    'activateATRMultiplier': tsl_params['activateATRMultiplier'],
                    'trailATRMultiplier': tsl_params['trailATRMultiplier'],
                    'original_sl_price': sl_price,
                    'original_tp_price': tp_price,
                    'final_sl_price': current_sl_price,
                    'max_drawdown': max_drawdown,
                    'max_profit': max_profit,
                    'time_in_trade_seconds': time_in_trade,
                    'tsl_activated': trailing_active,
                    'tsl_activation_time': tsl_activation_time,
                    'tsl_updates': tsl_updates,
                    'max_r_multiple': max_r_multiple
                }
        
        # Ak sme nedosiahli SL/TP, ukončujeme na poslednej cene (timeout)
        final_row = trade_data.iloc[-1]
        final_price = final_row['close'] * (1 - slippage_perc * position)
        pnl = (final_price - entry_price) * position
        exit_fee = abs(trade_setup.get('size', 1.0) * final_price * fee_perc)
        net_pnl = pnl - entry_fee - exit_fee
        time_in_trade = (trade_data.index[-1] - entry_time).total_seconds()
        
        return {
            'trade_id': trade_setup.get('trade_id', 0),
            'entry_time': entry_time,
            'direction': direction,
            'entry_price': entry_price,
            'exit_time': trade_data.index[-1],
            'exit_price': final_price,
            'pnl': net_pnl,
            'exit_reason': 'TIMEOUT',
            'activateATRMultiplier': tsl_params['activateATRMultiplier'],
            'trailATRMultiplier': tsl_params['trailATRMultiplier'],
            'original_sl_price': sl_price,
            'original_tp_price': tp_price,
            'final_sl_price': current_sl_price,
            'max_drawdown': max_drawdown,
            'max_profit': max_profit,
            'time_in_trade_seconds': time_in_trade,
            'tsl_activated': trailing_active,
            'tsl_activation_time': tsl_activation_time,
            'tsl_updates': tsl_updates,
            'max_r_multiple': max_r_multiple
        }
        
    except Exception as e:
        # V prípade chyby vrátime neutrálny výsledok
        return create_neutral_result(trade_setup, tsl_params, entry_price, entry_fee, f'ERROR: {str(e)}')

def create_neutral_result(trade_setup, tsl_params, entry_price, entry_fee, reason):
    """Helper funkcia pre vytvorenie neutrálneho výsledku."""
    return {
        'trade_id': trade_setup.get('trade_id', 0),
        'entry_time': trade_setup['entry_time'],
        'direction': trade_setup['direction'],
        'entry_price': entry_price,
        'exit_time': trade_setup['entry_time'],
        'exit_price': entry_price,
        'pnl': -entry_fee,
        'exit_reason': reason,
        'activateATRMultiplier': tsl_params['activateATRMultiplier'],
        'trailATRMultiplier': tsl_params['trailATRMultiplier'],
        'original_sl_price': 0.0,
        'original_tp_price': 0.0,
        'final_sl_price': 0.0,
        'max_drawdown': 0.0,
        'max_profit': 0.0,
        'time_in_trade_seconds': 0,
        'tsl_activated': False,
        'tsl_activation_time': None,
        'tsl_updates': 0,
        'max_r_multiple': 0.0
    }

def run_tsl_parameter_sweep(config: dict, data_dict: dict, agent, vecnorm=None, use_1s_decisions: bool = False):
    """
    Spustí backtest s TSL parameter sweep - pre každý obchod testuje rôzne kombinácie TSL parametrov.
    """
    log.info("🧪 Začínam TSL parameter sweep...")
    
    # Generovanie kombinácií TSL parametrov
    tsl_combinations = generate_tsl_parameter_combinations()
    
    # Príprava súboru pre výsledky
    results_file = f"tsl_parameter_sweep_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    
    # Spustenie pôvodného backtestu na získanie trade setups
    log.info("📊 Spúšťam pôvodný backtest na získanie obchodov...")
    original_trades, original_equity_curve, original_final_equity = run_backtest_original(
        config, data_dict, agent, vecnorm, use_1s_decisions
    )
    
    log.info(f"🎯 Našiel som {len(original_trades)} obchodov na testovanie TSL parametrov")
    
    if len(original_trades) == 0:
        log.warning("❌ Žiadne obchody sa nevykonali v pôvodnom backteste!")
        return original_trades, original_equity_curve, original_final_equity, pd.DataFrame()
    
    # Výsledky pre všetky kombinácie
    all_results = []
    
    # Pre každý obchod testujeme všetky kombinácie TSL parametrov
    for trade_idx, trade in enumerate(original_trades):
        log.info(f"📊 Testujem obchod {trade_idx + 1}/{len(original_trades)}: {trade['direction']} @ {trade['entry_time']}")
        
        # Príprava trade setup s dodatočnými informáciami
        trade_setup = {
            'trade_id': trade_idx,
            'entry_time': trade['entry_time'],
            'direction': trade['direction'],
            'entry_price': trade['entry_price'],
            'size': trade['size'],
            'current_atr': trade.get('atr', 0.001)  # Predpokladáme, že ATR môže byť uložené v trade
        }
        
        # Ak ATR nie je v trade data, pokúsime sa ho získať z 5m dát
        if 'atr' not in trade and data_dict.get('primary') is not None:
            try:
                # Nájdeme najbližší 5m bar pre získanie ATR
                primary_data = data_dict['primary']
                atr_col = next((col for col in primary_data.columns if col.startswith('ATR_')), None)
                if atr_col:
                    # Nájdeme posledný dostupný ATR pred entry_time
                    mask = primary_data.index <= trade['entry_time']
                    available_data = primary_data[mask]
                    if not available_data.empty:
                        trade_setup['current_atr'] = available_data.iloc[-1][atr_col]
                        log.debug(f"Získané ATR {trade_setup['current_atr']:.6f} pre obchod {trade_idx}")
            except Exception as e:
                log.warning(f"Nepodarilo sa získať ATR pre obchod {trade_idx}: {e}")
        
        # Testovanie všetkých kombinácií TSL parametrov pre tento obchod
        for combo_idx, tsl_combo in enumerate(tsl_combinations):
            if combo_idx % 10 == 0:  # Progress update každých 10 kombinácií
                progress = (combo_idx / len(tsl_combinations)) * 100
                log.info(f"  TSL kombinácia {combo_idx + 1}/{len(tsl_combinations)} ({progress:.1f}%)")
            
            # Simulácia obchodu s týmito TSL parametrami
            result = simulate_single_trade_with_tsl_params(
                trade_setup, tsl_combo, config, data_dict['second']
            )
            all_results.append(result)
    
    # Uloženie výsledkov
    results_df = pd.DataFrame(all_results)
    
    # Uloženie do CSV
    results_df.to_csv(results_file, index=False, float_format='%.6f')
    log.info(f"💾 Výsledky TSL parameter sweep uložené do: {results_file}")
    
    # Analýza najlepších kombinácií
    analyze_tsl_parameter_sweep_results(results_df)
    
    return original_trades, original_equity_curve, original_final_equity, results_df

def analyze_tsl_parameter_sweep_results(results_df):
    """Analyzuje výsledky TSL parameter sweep a nájde najlepšie kombinácie."""
    log.info("📈 Analyzujem výsledky TSL parameter sweep...")
    
    # Zoskupenie podľa kombinácií TSL parametrov
    tsl_param_columns = ['activateATRMultiplier', 'trailATRMultiplier']
    
    grouped = results_df.groupby(tsl_param_columns).agg({
        'pnl': ['sum', 'mean', 'count', 'std'],
        'exit_reason': [
            lambda x: (x == 'TP').sum() / len(x) * 100,  # Win rate
            lambda x: (x == 'TSL').sum() / len(x) * 100   # TSL exit rate
        ],
        'time_in_trade_seconds': 'mean',
        'tsl_activated': lambda x: x.sum() / len(x) * 100,  # TSL activation rate
        'tsl_updates': 'mean',
        'max_r_multiple': 'mean'
    }).round(4)
    
    grouped.columns = [
        'total_pnl', 'avg_pnl', 'trade_count', 'pnl_std', 
        'win_rate_pct', 'tsl_exit_rate_pct', 'avg_time_in_trade',
        'tsl_activation_rate_pct', 'avg_tsl_updates', 'avg_max_r_multiple'
    ]
    grouped = grouped.reset_index()
    
    # Najlepšie kombinácie podľa celkového PnL
    top_combinations = grouped.nlargest(10, 'total_pnl')
    
    log.info("🏆 TOP 10 TSL kombinácií (podľa celkového PnL):")
    for idx, row in top_combinations.iterrows():
        log.info(f"  {idx+1}. Total PnL: {row['total_pnl']:.2f} | "
                f"Avg PnL: {row['avg_pnl']:.4f} | "
                f"Win Rate: {row['win_rate_pct']:.1f}% | "
                f"TSL Rate: {row['tsl_exit_rate_pct']:.1f}%")
        log.info(f"     TSL Params: activate={row['activateATRMultiplier']:.1f}R, "
                f"trail={row['trailATRMultiplier']:.2f} | "
                f"Activation: {row['tsl_activation_rate_pct']:.1f}% | "
                f"Avg Updates: {row['avg_tsl_updates']:.1f} | "
                f"Avg Max R: {row['avg_max_r_multiple']:.2f}")
    
    # Najlepšie kombinácie podľa iných metrík
    log.info("\n🎯 TOP 5 TSL kombinácií podľa win rate:")
    top_win_rate = grouped.nlargest(5, 'win_rate_pct')
    for idx, row in top_win_rate.iterrows():
        log.info(f"  Win Rate: {row['win_rate_pct']:.1f}% | "
                f"activate={row['activateATRMultiplier']:.1f}R, "
                f"trail={row['trailATRMultiplier']:.2f} | "
                f"Total PnL: {row['total_pnl']:.2f}")
    
    log.info("\n🚀 TOP 5 TSL kombinácií podľa TSL activation rate:")
    top_tsl_activation = grouped.nlargest(5, 'tsl_activation_rate_pct')
    for idx, row in top_tsl_activation.iterrows():
        log.info(f"  TSL Activation: {row['tsl_activation_rate_pct']:.1f}% | "
                f"activate={row['activateATRMultiplier']:.1f}R, "
                f"trail={row['trailATRMultiplier']:.2f} | "
                f"Total PnL: {row['total_pnl']:.2f}")
    
    # Uloženie analýzy
    analysis_file = f"tsl_parameter_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    grouped.to_csv(analysis_file, index=False, float_format='%.4f')
    log.info(f"💾 Analýza TSL parametrov uložená do: {analysis_file}")
    
    # Dodatočné analýzy
    log.info("\n📊 TSL Parameter Analysis Summary:")
    log.info(f"Najlepší activate threshold: {top_combinations.iloc[0]['activateATRMultiplier']:.1f}R")
    log.info(f"Najlepší trail multiplier: {top_combinations.iloc[0]['trailATRMultiplier']:.2f}")
    log.info(f"Priemerná TSL activation rate: {grouped['tsl_activation_rate_pct'].mean():.1f}%")
    log.info(f"Priemerná TSL exit rate: {grouped['tsl_exit_rate_pct'].mean():.1f}%")
    
    return top_combinations

# --- Hlavná časť skriptu ---
if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Testuje rôzne kombinácie trailing stop loss parametrov.")
    parser.add_argument("--cfg", required=True, type=Path, help="Cesta ku konfiguračnému súboru (YAML/JSON).")
    parser.add_argument("--start", required=True, help="Dátum začiatku backtestu (YYYY-MM-DD).")
    parser.add_argument("--end", required=True, help="Dátum konca backtestu (YYYY-MM-DD, vrátane).")
    parser.add_argument("--model", type=Path, default=None, help="Voliteľná cesta k modelu agenta (prepíše cestu v configu).")
    parser.add_argument("--log-level", default="INFO", choices=["DEBUG", "INFO", "WARNING", "ERROR"], help="Úroveň logovania.")
    parser.add_argument("--use-1s-decisions", action="store_true", help="Agent robí rozhodnutia každú sekundu namiesto každých 5 minút.")
    args = parser.parse_args()

    # --- Nastavenie Loggera ---
    for handler in log.handlers[:]: 
        log.removeHandler(handler)
    log.setLevel(args.log_level.upper())
    handler = logging.StreamHandler()
    formatter = logging.Formatter("%(asctime)s [%(name)s] [%(levelname)s] %(message)s", datefmt="%Y-%m-%d %H:%M:%S")
    handler.setFormatter(formatter)
    log.addHandler(handler)
    log.info(f"Logging nastavený na úroveň: {args.log_level.upper()}")

    try:
        config = load_config(args.cfg)
        
        # Automatická detekcia model a vecnorm súborov
        if args.model:
            model_path = Path(args.model)
        else:
            model_path = config.get('runtime', {}).get('model_path')
            if not model_path:
                raise ValueError("Cesta k modelu nie je definovaná ani v args ani v configu.")
            model_path = Path(model_path)
        
        # Ak model_path neexistuje ako .zip, skúsme nájsť checkpoint súbory
        if not model_path.exists():
            chk_dir = Path("./chk")
            if chk_dir.exists():
                model_files = list(chk_dir.glob("sac_*_steps.zip"))
                if model_files:
                    model_files.sort(key=lambda x: int(x.stem.split('_')[1]))
                    model_path = model_files[-1]
                    log.info(f"Auto-detekovaný model checkpoint: {model_path}")
        
        if not model_path.exists():
            log.error(f"Model neexistuje: {model_path}")
            exit(1)
            
        # Nájdenie zodpovedajúceho VecNormalize súboru
        vecnorm_path = model_path.with_suffix('.vecnorm.pkl')
        if not vecnorm_path.exists():
            model_stem = model_path.stem.replace('_steps', '')
            vecnorm_path = model_path.parent / f"{model_stem}.vecnorm.pkl"
            
        if not vecnorm_path.exists():
            log.warning(f"VecNormalize súbor nenájdený: {vecnorm_path}. Pozorování nebudú normalizované!")
            vecnorm = None
        else:
            log.info(f"Načítavam VecNormalize: {vecnorm_path}")
            try:
                from stable_baselines3.common.vec_env import DummyVecEnv
                import gymnasium as gym
                
                class DummyEnv(gym.Env):
                    def __init__(self):
                        super().__init__()
                        self.observation_space = gym.spaces.Box(-5.0, 5.0, (1451,), dtype=np.float32)
                        self.action_space = gym.spaces.Box(-1.0, 1.0, (4,), dtype=np.float32)
                    
                    def reset(self, **kwargs):
                        return np.zeros(1451, dtype=np.float32), {}
                    
                    def step(self, action):
                        return np.zeros(1451, dtype=np.float32), 0.0, False, False, {}
                
                dummy_env = DummyVecEnv([lambda: DummyEnv()])
                vecnorm = VecNormalize.load(vecnorm_path, dummy_env)
                vecnorm.training = False
                log.info("VecNormalize úspešne načítané")
            except Exception as e:
                log.error(f"Chyba pri načítavaní VecNormalize: {e}")
                vecnorm = None
        
        log.info(f"Používam model: {model_path}")
        
        # Načítanie agenta s PopArtSAC
        from agent import SimpleCNN1D, SafeReplayBuffer
        from popart_sac import PopArtSAC

        feats = config["envSettings"]["feature_columns"]
        training_settings = config["trainingSettings"]
        
        policy_kwargs = {
            "net_arch": training_settings["netArch"],
            "features_extractor_class": SimpleCNN1D,
            "features_extractor_kwargs": {
                **training_settings["featureExtractorKwargs"],
                "feature_names": feats,
                "meta_len": 11
            }
        }

        custom_objects.update({
            "buffer_size": training_settings["bufferSize"],
            "policy_kwargs": policy_kwargs,
            "replay_buffer_class": SafeReplayBuffer,
            "learning_rate": 0.0001
        })

        try:
            agent = PopArtSAC.load(model_path, device="cpu", custom_objects=custom_objects)
            log.info(f"Agent typu {type(agent).__name__} načítaný cez PopArtSAC.")
        except Exception as e:
            log.error(f"Chyba pri načítavaní modelu cez PopArtSAC: {e}", exc_info=True)
            exit(1)

        start_dt = datetime.strptime(args.start, "%Y-%m-%d").replace(tzinfo=timezone.utc)
        end_dt = datetime.strptime(args.end, "%Y-%m-%d").replace(hour=23, minute=59, second=59, microsecond=999999, tzinfo=timezone.utc)
        
        # Load data with 1s decision support
        use_1s_decisions = config.get('use_1s_decisions', False) or args.use_1s_decisions
        backtest_data = load_backtest_data(config, start_dt, end_dt,
                                          load_second_data=True,
                                          use_1s_decisions=use_1s_decisions)

        # Skontrolujeme, či máme dosť dát po načítaní
        if len(backtest_data['primary']) < config['envSettings']['state_lookback']:
            log.error(f"Nedostatok dát ({len(backtest_data['primary'])} riadkov) pre spustenie backtestu s lookbackom {config['envSettings']['state_lookback']}. Končím.")
            exit(1)

        log.info("🧪 TSL PARAMETER SWEEP MODE ACTIVATED")
        trades_list, equity_curve_df, final_equity, tsl_results = run_tsl_parameter_sweep(
            config, backtest_data, agent, vecnorm, use_1s_decisions=use_1s_decisions
        )
            
        trades_df = pd.DataFrame(trades_list)

        log.info("=" * 30 + " TSL PARAMETER SWEEP DOKONČENÝ " + "=" * 30)
        initial_equity = config.get('account', {}).get('initialEquity', 100)
        start_time = equity_curve_df.index.min() if not equity_curve_df.empty else start_dt
        end_time = equity_curve_df.index.max() if not equity_curve_df.empty else end_dt
        metrics = calculate_metrics(trades_df, equity_curve_df, initial_equity, final_equity, start_time, end_time)

        print("\n" + "=" * 30 + " PÔVODNÝ BACKTEST VÝSLEDKY " + "=" * 30)
        for key, value in metrics.items():
            if isinstance(value, float) and not np.isinf(value) and pd.notna(value):
                if '%' in key or 'Ratio' in key or 'Factor' in key:
                    print(f"{key:<30}: {value:>15.4f}")
                else:
                    print(f"{key:<30}: {value:>15.2f}")
            elif isinstance(value, int):
                print(f"{key:<30}: {value:>15d}")
            else:
                print(f"{key:<30}: {str(value):>15}")
        print("=" * (60 + len(" PÔVODNÝ BACKTEST VÝSLEDKY ")) + "\n")

        log.info(f"🎯 TSL parameter sweep dokončený s {len(tsl_results)} výsledkami")
        log.info("✅ Pozrite si súbory tsl_parameter_sweep_results_*.csv a tsl_parameter_analysis_*.csv pre detailné výsledky")

    except FileNotFoundError as e: 
        log.error(f"Chyba: Súbor nenájdený: {e}"); exit(1)
    except (ValueError, TypeError, KeyError) as e: 
        log.error(f"Chyba v dátach/konfigurácii: {e}", exc_info=True); exit(1)
    except Exception as e: 
        log.error(f"Neočekávaná chyba počas TSL testovania:", exc_info=True); exit(1)