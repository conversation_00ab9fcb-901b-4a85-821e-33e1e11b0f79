import pandas as pd
import numpy as np
from datetime import datetime, <PERSON><PERSON><PERSON>

def check_data_quality(file_path):
    """Check data quality in the parquet file, looking for zeros and missing data"""
    
    print(f"Checking data quality in: {file_path}")
    
    # Read the parquet file
    df = pd.read_parquet(file_path)
    
    print(f"Total records: {len(df)}")
    print(f"Date range: {df.index.min()} to {df.index.max()}")
    print(f"Columns: {df.columns.tolist()}")
    
    # Check for zeros in OHLCV data
    print("\n=== CHECKING FOR ZEROS ===")
    for col in ['open', 'high', 'low', 'close', 'volume']:
        if col in df.columns:
            zero_count = (df[col] == 0).sum()
            print(f"{col}: {zero_count} zeros out of {len(df)} records ({zero_count/len(df)*100:.2f}%)")
    
    # Check for missing values
    print("\n=== CHECKING FOR MISSING VALUES ===")
    missing = df.isnull().sum()
    for col, count in missing.items():
        if count > 0:
            print(f"{col}: {count} missing values ({count/len(df)*100:.2f}%)")
    
    # Look for specific problematic patterns
    print("\n=== CHECKING FOR PROBLEMATIC PATTERNS ===")
    
    # Check if high < low (impossible)
    if 'high' in df.columns and 'low' in df.columns:
        invalid_hl = (df['high'] < df['low']).sum()
        print(f"Invalid high < low: {invalid_hl} records")
    
    # Check if close outside of high-low range
    if all(col in df.columns for col in ['high', 'low', 'close']):
        invalid_close = ((df['close'] > df['high']) | (df['close'] < df['low'])).sum()
        print(f"Close outside high-low range: {invalid_close} records")
    
    # Check if open outside of high-low range  
    if all(col in df.columns for col in ['high', 'low', 'open']):
        invalid_open = ((df['open'] > df['high']) | (df['open'] < df['low'])).sum()
        print(f"Open outside high-low range: {invalid_open} records")
    
    # Sample some data to see actual values
    print("\n=== SAMPLE DATA ===")
    print("First 10 records:")
    print(df.head(10))
    
    print("\nLast 10 records:")
    print(df.tail(10))
    
    # Look specifically around the timeframe that was problematic
    # The trade was around 2.157 entry and 2.05139 TP
    target_range = df[(df['close'] >= 2.05) & (df['close'] <= 2.16)]
    if not target_range.empty:
        print(f"\n=== DATA AROUND PRICE RANGE 2.05-2.16 ===")
        print(f"Found {len(target_range)} records in this price range")
        print("Sample records:")
        print(target_range.head(20))
        
        # Check for zeros specifically in this range
        print("\nZeros in this price range:")
        for col in ['open', 'high', 'low', 'close', 'volume']:
            if col in target_range.columns:
                zero_count = (target_range[col] == 0).sum()
                print(f"{col}: {zero_count} zeros out of {len(target_range)} records")
    
    # Check for consecutive zeros
    print("\n=== CONSECUTIVE ZEROS ANALYSIS ===")
    for col in ['open', 'high', 'low', 'close', 'volume']:
        if col in df.columns:
            # Find consecutive zeros
            zeros = df[col] == 0
            zero_groups = zeros.ne(zeros.shift()).cumsum()
            consecutive_zeros = zeros.groupby(zero_groups).sum()
            max_consecutive = consecutive_zeros.max() if not consecutive_zeros.empty else 0
            print(f"{col}: Maximum consecutive zeros: {max_consecutive}")
            
            if max_consecutive > 5:  # Show details for significant consecutive zeros
                long_sequences = consecutive_zeros[consecutive_zeros > 5]
                print(f"  - Found {len(long_sequences)} sequences of >5 consecutive zeros")
    
    return df

if __name__ == "__main__":
    file_path = "parquet_processed/XRPUSDC/1s/2025-07-02.parquet"
    df = check_data_quality(file_path)