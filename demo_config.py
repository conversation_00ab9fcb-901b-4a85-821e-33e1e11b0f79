#!/usr/bin/env python3
"""
Demo configuration for testing the trading system without real API keys.
Creates a test environment for validation purposes.
"""

import os
from dotenv import load_dotenv

def setup_demo_environment():
    """Set up demo environment variables for testing."""
    
    # Demo API keys (non-functional, for testing only)
    demo_env = {
        'COINAPI_KEY': 'demo_coinapi_key_12345',
        'BINANCE_API_KEY': 'demo_binance_api_key_12345',
        'BINANCE_API_SECRET': 'demo_binance_secret_12345',
        'TRADING_MODE': 'test',
        'USE_BINANCE_FALLBACK': 'true',
        'USE_CACHED_DATA': 'true'
    }
    
    # Set environment variables
    for key, value in demo_env.items():
        os.environ[key] = value
    
    print("✅ Demo environment variables set")
    print("⚠️ These are non-functional demo keys for testing only")
    
    return demo_env

def test_config_loading():
    """Test configuration loading with demo keys."""
    try:
        from config_loader import load_config_secure
        config = load_config_secure("strategyConfig_scalp_1s.json")
        
        print("✅ Configuration loaded successfully")
        print(f"Test mode: {config.get('testMode', 'unknown')}")
        print(f"Symbol: {config.get('dataProvider', {}).get('symbol', 'unknown')}")
        print(f"Risk per trade: {config.get('riskManagement', {}).get('riskPerTradePercentage', 'unknown')}%")
        
        return True
    except Exception as e:
        print(f"❌ Configuration loading failed: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Setting up demo environment for testing...")
    print()
    
    # Setup demo environment
    setup_demo_environment()
    print()
    
    # Test configuration loading
    print("🔧 Testing configuration loading...")
    success = test_config_loading()
    print()
    
    if success:
        print("✅ Demo environment ready for testing!")
        print()
        print("🎯 You can now test the system with:")
        print("python live_trading.py --cfg strategyConfig_scalp_1s.json --test-trade")
        print()
        print("⚠️ Remember to configure real API keys in .env for live trading")
    else:
        print("❌ Demo environment setup failed")