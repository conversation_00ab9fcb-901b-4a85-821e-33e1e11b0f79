#!/usr/bin/env python3
"""
Check observation space dimensions for all configs.
"""
import json
from pathlib import Path

def check_config_dimensions(config_path):
    """Calculate observation space for a config."""
    try:
        with open(config_path, 'r') as f:
            cfg = json.load(f)
        
        feature_cols = cfg["envSettings"]["feature_columns"]
        lookback = cfg["envSettings"].get("state_lookback", 30)
        
        # Calculate observation space (features * lookback + 11 meta features)
        obs_space = len(feature_cols) * lookback + 11
        
        return {
            'path': config_path.name,
            'features': len(feature_cols), 
            'lookback': lookback,
            'obs_space': obs_space,
            'symbol': cfg.get('symbol', 'N/A')
        }
        
    except Exception as e:
        return {
            'path': config_path.name,
            'error': str(e)
        }

def main():
    # Target observation spaces from available models
    target_obs_spaces = [1451, 1597, 2051]
    
    print("🔍 Checking observation space for all configs:")
    print("=" * 70)
    
    configs = list(Path(".").glob("*config*.json"))
    configs.sort()
    
    results = []
    for config_path in configs:
        result = check_config_dimensions(config_path)
        results.append(result)
        
        if 'error' in result:
            print(f"❌ {result['path']:<30}: ERROR - {result['error']}")
        else:
            obs = result['obs_space']
            match = "✅" if obs in target_obs_spaces else "⚠️"
            print(f"{match} {result['path']:<30}: {result['features']} features × {result['lookback']} lookback + 11 = {obs}")
    
    print("\n" + "=" * 70)
    print("SUMMARY FOR TARGET MODEL SPACES:")
    print("=" * 70)
    
    for target in target_obs_spaces:
        matching_configs = [r for r in results if r.get('obs_space') == target]
        if matching_configs:
            print(f"\n📊 Observation space {target}:")
            for cfg in matching_configs:
                print(f"   ✅ {cfg['path']} (symbol: {cfg['symbol']})")
        else:
            print(f"\n❌ No configs found for observation space {target}")
    
    # Find config for 1451 (current model)
    target_1451_configs = [r for r in results if r.get('obs_space') == 1451]
    if target_1451_configs:
        print(f"\n🎯 For current model (obs_space=1451), use:")
        for cfg in target_1451_configs:
            print(f"   👉 {cfg['path']}")
    else:
        print(f"\n❌ No config found for current model (obs_space=1451)")
        print("   Need to either:")
        print("   1. Create a config with correct dimensions")
        print("   2. Use a different model that matches existing configs")

if __name__ == "__main__":
    main()