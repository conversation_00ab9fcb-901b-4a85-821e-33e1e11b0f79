import torch as th
import torch.nn as nn

class PopArt(nn.Module):
    """
    PopArt normalization layer for value functions.

    This implementation is based on the paper:
    "Learning values across many orders of magnitude" by <PERSON> et al.

    PopArt adaptively normalizes the targets in the value function to make learning more stable
    when rewards have varying scales or magnitudes.
    """
    def __init__(self, in_features: int, out_features: int = 1, beta: float = 0.0001):
        """
        Initialize the PopArt layer.

        Args:
            in_features: Number of input features
            out_features: Number of output features (typically 1 for value function)
            beta: Step size for updating the normalization parameters
        """
        super().__init__()
        self.in_features = in_features
        self.out_features = out_features
        self.beta = beta

        # Linear layer for the value function
        self.linear = nn.Linear(in_features, out_features)

        # Normalization parameters
        self.register_buffer("mean", th.zeros(out_features))
        self.register_buffer("std", th.ones(out_features))

        # Small constant to avoid division by zero
        self.epsilon = 1e-8

    def forward(self, x: th.Tensor) -> th.Tensor:
        """
        Forward pass through the PopArt layer.

        Args:
            x: Input tensor of shape (batch_size, in_features)

        Returns:
            Normalized value predictions
        """
        # Pass through linear layer
        value = self.linear(x)

        # Denormalize the output
        return value * self.std + self.mean

    def normalize(self, y: th.Tensor) -> th.Tensor:
        """
        Normalize targets using current statistics.

        Args:
            y: Target values to normalize

        Returns:
            Normalized targets
        """
        return (y - self.mean) / (self.std + self.epsilon)

    def update_parameters(self, targets: th.Tensor) -> None:
        """
        Update normalization parameters based on new target values.

        Args:
            targets: New target values
        """
        with th.no_grad():
            # Compute new statistics
            batch_mean = targets.mean(dim=0)
            batch_std = th.sqrt(targets.var(dim=0, unbiased=False) + self.epsilon)

            # Update statistics with moving average (in-place to preserve device and optimizer references)
            old_mean = self.mean.clone()
            old_std = self.std.clone()

            # Update mean and std using moving average (in-place operations)
            self.mean.mul_(1 - self.beta).add_(self.beta * batch_mean)
            self.std.mul_(1 - self.beta).add_(self.beta * batch_std)

            # Preserve the output of the network by adjusting the linear layer parameters
            # This ensures that the denormalized output remains the same after the update
            scale = old_std / (self.std + self.epsilon)
            bias = old_mean - self.mean * scale

            # Update the linear layer weights and bias
            self.linear.weight.data.mul_(scale.view(-1, 1))
            self.linear.bias.data.mul_(scale).add_(bias.view(-1))
