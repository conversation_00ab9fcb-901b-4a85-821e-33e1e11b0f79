#!/usr/bin/env python3
"""
Test script na overenie automatického výpočtu lookback dní.
"""

import json
import sys
from recompute_all_features import calculate_required_lookback_days

def test_lookback_calculation():
    """Test automatického výpočtu lookback dní pre rôzne konfigurácie."""
    
    print("🧪 Testovanie automatického výpočtu lookback dní...\n")
    
    # Test konfigurácie
    test_configs = [
        {
            "name": "Základné indikátory",
            "config": {
                "indicatorSettings": {
                    "rsi_5m": {"enabled": True, "basePeriod": 14},
                    "atr_5m": {"enabled": True, "basePeriod": 14},
                    "ema_5m": {"enabled": True, "baseFastPeriod": 9, "baseSlowPeriod": 21}
                }
            },
            "timeframes": ["1s", "5m"]
        },
        {
            "name": "ADX indikátory",
            "config": {
                "indicatorSettings": {
                    "adx": {"enabled": True, "basePeriod": 14},  # 14 * 2 = 28
                    "rsi_5m": {"enabled": True, "basePeriod": 14}
                }
            },
            "timeframes": ["1s", "5m"]
        },
        {
            "name": "HMM s veľkým window",
            "config": {
                "indicatorSettings": {
                    "hmm_5m": {"enabled": True, "window": 200},  # Najväčší!
                    "rsi_5m": {"enabled": True, "basePeriod": 14}
                }
            },
            "timeframes": ["1s", "5m"]
        },
        {
            "name": "MACD s pomalými periódami",
            "config": {
                "indicatorSettings": {
                    "macd_5m": {"enabled": True, "fastPeriod": 12, "slowPeriod": 26, "signalPeriod": 9},
                    "bollinger_5m": {"enabled": True, "basePeriod": 20}
                }
            },
            "timeframes": ["1s", "5m"]
        },
        {
            "name": "Komplexná konfigurácia",
            "config": {
                "indicatorSettings": {
                    "hmm_5m": {"enabled": True, "window": 200},
                    "adx": {"enabled": True, "basePeriod": 14},
                    "macd_5m": {"enabled": True, "slowPeriod": 26},
                    "ema_5m": {"enabled": True, "baseSlowPeriod": 50},  # Pomalá EMA
                    "bollinger_5m": {"enabled": True, "basePeriod": 20}
                }
            },
            "timeframes": ["1s", "5m"]
        }
    ]
    
    for test_case in test_configs:
        print(f"📊 {test_case['name']}:")
        
        for timeframe in test_case['timeframes']:
            lookback_days = calculate_required_lookback_days(test_case['config'], timeframe)
            print(f"   {timeframe}: {lookback_days} dní")
        
        print()
    
    # Test s reálnymi konfiguráciami
    print("🔧 Test s reálnymi konfiguráciami:")
    
    config_files = [
        "strategyConfig_scalp_1s.json",
        "strategyConfig_scalp.json"
    ]
    
    for config_file in config_files:
        try:
            with open(config_file, 'r') as f:
                config = json.load(f)
            
            timeframe = config.get("primaryTimeframe", "5m")
            lookback_days = calculate_required_lookback_days(config, timeframe)
            
            print(f"   {config_file} ({timeframe}): {lookback_days} dní")
            
        except FileNotFoundError:
            print(f"   {config_file}: súbor neexistuje")
        except Exception as e:
            print(f"   {config_file}: chyba - {e}")
    
    print()

def test_periods_calculation():
    """Test výpočtu periód na deň pre rôzne timeframes."""
    
    print("⏰ Test výpočtu periód na deň:")
    
    timeframes = {
        "1s": 86400,    # 24*60*60
        "5m": 288,      # 24*60/5
        "15m": 96,      # 24*60/15
        "1h": 24        # 24
    }
    
    for tf, expected in timeframes.items():
        # Simulácia výpočtu z funkcie
        if tf == '1s':
            periods_per_day = 86400
        elif tf == '5m':
            periods_per_day = 288
        elif tf == '15m':
            periods_per_day = 96
        elif tf == '1h':
            periods_per_day = 24
        else:
            periods_per_day = 288
        
        status = "✅" if periods_per_day == expected else "❌"
        print(f"   {tf}: {periods_per_day} periód/deň {status}")
    
    print()

def test_edge_cases():
    """Test hraničných prípadov."""
    
    print("🔍 Test hraničných prípadov:")
    
    # Prázdna konfigurácia
    empty_config = {"indicatorSettings": {}}
    lookback = calculate_required_lookback_days(empty_config, "5m")
    print(f"   Prázdna konfigurácia: {lookback} dní")
    
    # Vypnuté indikátory
    disabled_config = {
        "indicatorSettings": {
            "rsi_5m": {"enabled": False, "basePeriod": 14},
            "atr_5m": {"enabled": True, "basePeriod": 7}
        }
    }
    lookback = calculate_required_lookback_days(disabled_config, "5m")
    print(f"   Vypnuté indikátory: {lookback} dní")
    
    # Neznámy timeframe
    basic_config = {"indicatorSettings": {"rsi_5m": {"enabled": True, "basePeriod": 14}}}
    lookback = calculate_required_lookback_days(basic_config, "unknown")
    print(f"   Neznámy timeframe: {lookback} dní")
    
    print()

if __name__ == "__main__":
    print("🚀 Spúšťam test automatického výpočtu lookback...\n")
    
    test_lookback_calculation()
    test_periods_calculation()
    test_edge_cases()
    
    print("🎉 Test dokončený!")
    print("\n💡 Výsledky:")
    print("   - 1s timeframe: veľmi krátky lookback (HMM 200s = 3.3 min)")
    print("   - 5m timeframe: stredný lookback (HMM 200 periód = 16.7h)")
    print("   - ADX potrebuje 2x viac periód kvôli vyhladzovaniu")
    print("   - Minimálne 2 dni lookback pre bezpečnosť")
