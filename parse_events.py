#!/usr/bin/env python
"""
tb_export_summary.py
--------------------
Export scalar kriviek z TensorBoard logu do textu alebo Markdownu.

Použitie:
    python tb_export_summary.py PATH [PATH ...]
    # napr. konkrétny súbor
    python tb_export_summary.py events.out.tfevents.1750971230.slavosmn3.1199333.0
    # alebo cel<PERSON> priečinok s viacerými behmi
    python tb_export_summary.py ./logs --markdown --out summary.md
"""

import argparse
from pathlib import Path
from datetime import datetime

import numpy as np
import pandas as pd
import tensorflow as tf


def read_events(path: Path) -> pd.DataFrame:
    """Načíta jeden *.tfevents* súbor do DataFrame."""
    rows = []
    for event in tf.compat.v1.train.summary_iterator(str(path)):
        for v in event.summary.value:
            if hasattr(v, "simple_value"):
                rows.append(
                    {
                        "time": datetime.fromtimestamp(event.wall_time),
                        "step": event.step,
                        "tag": v.tag,
                        "value": v.simple_value,
                    }
                )
    return pd.DataFrame(rows)


def slope(series: pd.Series) -> float:
    """Lineárny sklon (trend) – posledných 20 % vzoriek."""
    if len(series) < 3:
        return 0.0
    n = max(3, int(len(series) * 0.20))
    y = series.tail(n).to_numpy()
    x = np.arange(len(y))
    return float(np.polyfit(x, y, 1)[0])


def summarize(df: pd.DataFrame) -> pd.DataFrame:
    g = df.groupby("tag")["value"]
    summary = g.agg(
        last="last",
        mean="mean",
        min="min",
        max="max",
        std="std",
    )
    summary["trend"] = g.apply(slope)
    summary["trend_sign"] = summary["trend"].apply(
        lambda t: "↑" if t > 0 else ("↓" if t < 0 else "→")
    )
    return summary


def main() -> None:
    ap = argparse.ArgumentParser(description="Export TensorBoard scalars do textu")
    ap.add_argument("paths", nargs="+", help="súbor/y alebo priečinky s *.tfevents*")
    ap.add_argument("--out", default="tb_summary.txt", help="výstupný súbor")
    ap.add_argument("--markdown", action="store_true", help="písať ako Markdown tabuľku")
    args = ap.parse_args()

    # 1. Načítaj všetky súbory
    dfs = []
    for p in args.paths:
        p = Path(p)
        if p.is_dir():
            for f in p.rglob("events.out.tfevents.*"):
                dfs.append(read_events(f))
        elif p.is_file():
            dfs.append(read_events(p))
        else:
            ap.error(f"Path not found: {p}")
    if not dfs:
        ap.error("Nenájdený žiadny *.tfevents* súbor")

    df = pd.concat(dfs, ignore_index=True).sort_values(["tag", "time"])

    # 2. Súhrn
    summary = summarize(df)

    # 3. Zapíš text
    cols = ["last", "mean", "min", "max", "trend_sign"]
    if args.markdown:
        text = summary[cols].to_markdown(floatfmt=".6g")
    else:
        text = summary[cols].to_string(float_format="{:.6g}".format)

    Path(args.out).write_text(text, encoding="utf-8")
    print(f"✅ Summary written to: {Path(args.out).resolve()}  ({len(summary)} tags)")


if __name__ == "__main__":
    main()
