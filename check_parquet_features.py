#!/usr/bin/env python3
"""
Skript na kontrolu features v parquet súboroch
"""
import pandas as pd
from pathlib import Path

def check_parquet_features():
    # Nájdeme parquet súbor
    parquet_dir = Path('parquet_processed/XRPUSDC/5m')
    if not parquet_dir.exists():
        print('Parquet directory does not exist')
        return
    
    files = list(parquet_dir.glob('*.parquet'))
    if not files:
        print('No parquet files found')
        return
    
    # Použijeme najnovší súbor
    latest_file = sorted(files)[-1]
    print(f'Reading: {latest_file}')
    
    df = pd.read_parquet(latest_file)
    print(f'\nColumns in parquet file ({len(df.columns)} total):')
    for i, col in enumerate(df.columns):
        print(f'{i+1:3d}. {col}')
    
    print(f'\nSample data shape: {df.shape}')
    print(f'Date range: {df.index.min()} to {df.index.max()}')
    
    # Porovnajme s konfiguráciou
    import json
    with open('strategyConfig_scalp_1s.json', 'r') as f:
        config = json.load(f)
    
    expected_features = config['envSettings']['feature_columns']
    print(f'\nExpected features from config ({len(expected_features)} total):')
    for i, feat in enumerate(expected_features):
        print(f'{i+1:3d}. {feat}')
    
    # Porovnanie
    parquet_features = set(df.columns)
    config_features = set(expected_features)
    
    missing_in_parquet = config_features - parquet_features
    extra_in_parquet = parquet_features - config_features
    
    print(f'\n=== COMPARISON ===')
    print(f'Features in config: {len(config_features)}')
    print(f'Features in parquet: {len(parquet_features)}')
    print(f'Common features: {len(config_features & parquet_features)}')
    
    if missing_in_parquet:
        print(f'\nMissing in parquet ({len(missing_in_parquet)}):')
        for feat in sorted(missing_in_parquet):
            print(f'  - {feat}')
    
    if extra_in_parquet:
        print(f'\nExtra in parquet ({len(extra_in_parquet)}):')
        for feat in sorted(extra_in_parquet):
            print(f'  + {feat}')

if __name__ == '__main__':
    check_parquet_features()
