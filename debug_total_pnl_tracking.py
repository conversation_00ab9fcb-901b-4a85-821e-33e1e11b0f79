import pandas as pd
import numpy as np
from datetime import datetime, timezone
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
log = logging.getLogger("pnl_tracker")

def debug_total_pnl_calculation():
    """
    Debug script to trace Total PnL calculation issues.
    Analyzes the specific case where Total PnL jumps from 2.84 to -1.33
    """
    
    print("🔍 DEBUG: Total PnL Calculation Tracking")
    print("=" * 60)
    
    # Simulate the scenario based on the user's description
    print("📊 Scenario Analysis:")
    print("Previous Total PnL: $2.84")
    print("Latest EXIT PnL: -$1.33")
    print("Expected Total PnL: $2.84 + (-$1.33) = $1.51")
    print("Actual shown Total PnL: -$1.33")
    print()
    
    # Check the Total PnL formula from line 1169
    print("🧮 Total PnL Formula Analysis (line 1169):")
    print("total_pnl = equity_total - initial_equity + current_unrealized_pnl")
    print()
    
    # Simulate possible scenarios
    initial_equity = 10000.0
    
    print("📈 Scenario 1: Correct Accumulation")
    equity_after_first_trade = initial_equity + 2.84  # Should be 10002.84
    current_unrealized_pnl = 0.0  # No open position
    total_pnl_correct = equity_after_first_trade - initial_equity + current_unrealized_pnl
    print(f"  equity_total: ${equity_after_first_trade:.2f}")
    print(f"  initial_equity: ${initial_equity:.2f}")
    print(f"  current_unrealized_pnl: ${current_unrealized_pnl:.2f}")
    print(f"  total_pnl: ${total_pnl_correct:.2f} ✅")
    print()
    
    print("❌ Scenario 2: equity_total Reset Issue")
    equity_reset = initial_equity - 1.33  # equity_total somehow becomes 9998.67
    total_pnl_wrong = equity_reset - initial_equity + current_unrealized_pnl
    print(f"  equity_total: ${equity_reset:.2f} (RESET ERROR)")
    print(f"  initial_equity: ${initial_equity:.2f}")
    print(f"  current_unrealized_pnl: ${current_unrealized_pnl:.2f}")
    print(f"  total_pnl: ${total_pnl_wrong:.2f} ❌")
    print()
    
    print("❌ Scenario 3: Negative PnL Overwrite")
    equity_overwrite = initial_equity  # equity_total back to initial
    current_unrealized_pnl = -1.33  # Unrealized PnL showing as -1.33
    total_pnl_overwrite = equity_overwrite - initial_equity + current_unrealized_pnl
    print(f"  equity_total: ${equity_overwrite:.2f} (OVERWRITE ERROR)")
    print(f"  initial_equity: ${initial_equity:.2f}")
    print(f"  current_unrealized_pnl: ${current_unrealized_pnl:.2f}")
    print(f"  total_pnl: ${total_pnl_overwrite:.2f} ❌")
    print()
    
    print("🔧 Debugging Actions Needed:")
    print("1. Check if equity_total is being reset somewhere")
    print("2. Verify PnL accumulation logic in exit scenarios")
    print("3. Check if fees are being double-counted")
    print("4. Verify unrealized PnL calculation for closed positions")
    print()
    
    # Check where equity_total gets modified in the code
    print("📍 equity_total Modification Points in Code:")
    modifications = [
        ("Line 1063", "equity_total = initial_equity", "Initialization"),
        ("Line 1449", "equity_total += pnl - exit_fee", "SL/TP Exit"),
        ("Line 1565", "equity_total += pnl - exit_fee", "Agent Signal Exit"),
        ("Line 1594", "equity_total += pnl - exit_fee", "Soft TP Exit"),
        ("Line 1726", "equity_total -= current_entry_fee", "Entry Fee Deduction"),
        ("Line 1754", "equity_total += pnl - exit_fee", "End of Data Exit")
    ]
    
    for line, code, description in modifications:
        print(f"  {line}: {code} ({description})")
    print()
    
    return {
        'expected_total_pnl': total_pnl_correct,
        'actual_shown': total_pnl_wrong,
        'potential_issue': 'equity_total reset or incorrect PnL accumulation'
    }

if __name__ == "__main__":
    result = debug_total_pnl_calculation()
    print("🎯 Summary:")
    print(f"Expected Total PnL: ${result['expected_total_pnl']:.2f}")
    print(f"Actual Shown: ${result['actual_shown']:.2f}")
    print(f"Issue: {result['potential_issue']}")