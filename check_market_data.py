#!/usr/bin/env python3
"""
Skript na analýzu market dát pre overenie realistickosti PnL skokov.
Kontroluje XRPUSDC 1s dáta okolo času 06:38:20-06:38:35 dňa 2025-07-02.
"""

import pandas as pd
import numpy as np
from datetime import datetime, timezone

def check_price_movement():
    """Kontroluje cenové pohyby v kritickom čase."""
    
    # Načítanie 1s dát
    file_path = "parquet_processed/XRPUSDC/1s/2025-07-02.parquet"
    
    try:
        df = pd.read_parquet(file_path)
        print(f"✅ Načítaných {len(df)} 1s barov pre 2025-07-02")
        print(f"Stĺpce: {list(df.columns)}")
        
        # Kontrola timestamp formátu
        if 'timestamp' in df.columns:
            df['timestamp'] = pd.to_datetime(df['timestamp'], utc=True)
            df = df.set_index('timestamp').sort_index()
        elif isinstance(df.index, pd.DatetimeIndex):
            df.index = pd.to_datetime(df.index, utc=True)
        else:
            print("❌ Nenašiel som timestamp stĺpec!")
            return
        
        print(f"📅 Časový rozsah dát: {df.index.min()} -> {df.index.max()}")
        
        # Filtruj dáta okolo kritického času 06:38:00 - 06:39:00
        start_time = pd.Timestamp('2025-07-02 06:37:00', tz='UTC')
        end_time = pd.Timestamp('2025-07-02 06:40:00', tz='UTC')
        
        critical_data = df.loc[start_time:end_time]
        
        if critical_data.empty:
            print(f"❌ Žiadne dáta v kritickom čase {start_time} -> {end_time}")
            return
        
        print(f"\n🔍 ANALÝZA KRITICKÉHO OBDOBIA ({len(critical_data)} barov):")
        print("=" * 80)
        
        # Zobraz detailné dáta okolo kritického času
        for idx, row in critical_data.iterrows():
            timestamp_str = idx.strftime('%H:%M:%S')
            print(f"{timestamp_str} | O:{row['open']:.5f} H:{row['high']:.5f} L:{row['low']:.5f} C:{row['close']:.5f}")
        
        # Analýza pre short pozíciu
        print(f"\n📊 ANALÝZA SHORT POZÍCIE:")
        print("=" * 50)
        
        # Z logu: exit na 2.05201 o 06:38:35 s profitom 122.42
        exit_time = pd.Timestamp('2025-07-02 06:38:35', tz='UTC')
        exit_price = 2.05201
        profit = 122.42
        position_size = 1164.3132
        
        # Vypočítanie entry price
        # profit = position_size * (entry_price - exit_price) pre short
        calculated_entry_price = exit_price + (profit / position_size)
        
        print(f"💰 Očakávaná entry cena: {calculated_entry_price:.5f}")
        print(f"🎯 Exit cena (z logu): {exit_price:.5f}")
        print(f"📈 Očakávaný cenový pokles: {calculated_entry_price - exit_price:.5f}")
        
        # Nájdi najbližšie dáta k exit time
        if exit_time in critical_data.index:
            exit_bar = critical_data.loc[exit_time]
            print(f"✅ Exit bar {exit_time.strftime('%H:%M:%S')}: C:{exit_bar['close']:.5f}")
        else:
            # Nájdi najbližší bar
            time_diffs = abs(critical_data.index - exit_time)
            closest_idx = time_diffs.argmin()
            closest_time = critical_data.index[closest_idx]
            closest_bar = critical_data.iloc[closest_idx]
            print(f"📍 Najbližší bar k {exit_time.strftime('%H:%M:%S')}: {closest_time.strftime('%H:%M:%S')}")
            print(f"   OHLC: {closest_bar['open']:.5f}/{closest_bar['high']:.5f}/{closest_bar['low']:.5f}/{closest_bar['close']:.5f}")
        
        # Hľadanie entry obdobia (pred 06:38:20)
        entry_period = critical_data[critical_data.index <= pd.Timestamp('2025-07-02 06:38:20', tz='UTC')]
        
        if not entry_period.empty:
            print(f"\n🔎 DÁTA PRED EXITOM (entry obdobie):")
            for idx, row in entry_period.tail(5).iterrows():  # Posledných 5 barov pred exitom
                timestamp_str = idx.strftime('%H:%M:%S')
                close_price = row['close']
                
                # Simulácia unrealized PnL pre short pozíciu
                if close_price > 0:
                    unrealized_pnl = position_size * (calculated_entry_price - close_price)
                    print(f"{timestamp_str} | Close: {close_price:.5f} | Unrealized PnL: {unrealized_pnl:.2f}")
        
        # Kontrola, či je pokles realistický
        max_price_before = critical_data[critical_data.index <= pd.Timestamp('2025-07-02 06:38:20', tz='UTC')]['close'].max()
        min_price_after = critical_data[critical_data.index >= pd.Timestamp('2025-07-02 06:38:30', tz='UTC')]['close'].min()
        
        print(f"\n📉 CENOVÝ POHYB ANALÝZA:")
        print(f"🔴 Max cena pred 06:38:20: {max_price_before:.5f}")
        print(f"🟢 Min cena po 06:38:30: {min_price_after:.5f}")
        print(f"📊 Celkový pokles: {max_price_before - min_price_after:.5f}")
        
        # Overenie realistickosti
        expected_drop = calculated_entry_price - exit_price
        actual_drop = max_price_before - min_price_after
        
        print(f"\n✅ OVERENIE REALISTICKOSTI:")
        print(f"Očakávaný pokles: {expected_drop:.5f}")
        print(f"Skutočný pokles: {actual_drop:.5f}")
        
        if abs(actual_drop - expected_drop) < 0.01:  # Tolerancia 0.01
            print("✅ POTVRDENÉ: Cenový pohyb je konzistentný s PnL skokom!")
        else:
            print("❌ NEZHODA: Cenový pohyb nezodpovedá očakávanému PnL skoku!")
        
    except Exception as e:
        print(f"❌ Chyba pri analýze: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_price_movement()