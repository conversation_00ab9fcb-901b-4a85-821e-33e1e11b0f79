#!/usr/bin/env python3
"""
Enhanced startup script for the 1s decision frequency trading system.
Handles API key validation, environment setup, and provides user guidance.
"""

import os
import sys
import logging
from pathlib import Path

def check_requirements():
    """Check if all required packages are installed."""
    required_imports = {
        'numpy': 'numpy',
        'pandas': 'pandas',
        'torch': 'torch',
        'websockets': 'websockets',
        'requests': 'requests',
        'python-binance': 'binance',
        'stable-baselines3': 'stable_baselines3',
        'python-dotenv': 'dotenv'
    }
    
    missing_packages = []
    for package_name, import_name in required_imports.items():
        try:
            __import__(import_name)
        except ImportError:
            missing_packages.append(package_name)
    
    if missing_packages:
        print("❌ Missing required packages:")
        for pkg in missing_packages:
            print(f"   - {pkg}")
        print("\nInstall missing packages with:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def check_api_keys():
    """Check if API keys are configured."""
    from dotenv import load_dotenv
    
    # Load .env file
    if os.path.exists('.env'):
        load_dotenv('.env')
        print("✅ Environment file (.env) found and loaded")
    else:
        print("⚠️ No .env file found. Using system environment variables only.")
        print("💡 Create .env file from .env.example for easier configuration")
    
    # Check required API keys
    coinapi_key = os.getenv('COINAPI_KEY')
    binance_key = os.getenv('BINANCE_API_KEY')
    binance_secret = os.getenv('BINANCE_API_SECRET')
    
    issues = []
    
    if not coinapi_key or coinapi_key == 'your_coinapi_key_here':
        issues.append("COINAPI_KEY not configured")
    
    if not binance_key or binance_key == 'your_binance_api_key_here':
        issues.append("BINANCE_API_KEY not configured")
        
    if not binance_secret or binance_secret == 'your_binance_api_secret_here':
        issues.append("BINANCE_API_SECRET not configured")
    
    if issues:
        print("❌ API Key Issues:")
        for issue in issues:
            print(f"   - {issue}")
        print("\n📝 To fix this:")
        print("1. Copy .env.example to .env")
        print("2. Edit .env and add your real API keys")
        print("3. Get CoinAPI key from: https://coinapi.io")
        print("4. Get Binance keys from: Binance Account Settings > API Management")
        return False
    else:
        print("✅ All API keys configured")
        return True

def check_model_files():
    """Check if trained model files exist."""
    model_files = list(Path('.').glob('sac_*_steps.zip'))
    vecnorm_files = list(Path('.').glob('*.vecnorm.pkl'))
    
    if not model_files:
        print("❌ No trained SAC model found (sac_*_steps.zip)")
        print("💡 Train a model first or download a pre-trained model")
        return False
    
    if not vecnorm_files:
        print("⚠️ No VecNormalize file found (*.vecnorm.pkl)")
        print("💡 Model will run without normalization")
    
    latest_model = max(model_files, key=lambda x: int(x.stem.split('_')[1]))
    print(f"✅ Found trained model: {latest_model}")
    
    return True

def check_data_files():
    """Check if historical data files exist."""
    parquet_dir = Path('parquet_processed')
    
    if not parquet_dir.exists():
        print("❌ No historical data directory found (parquet_processed/)")
        print("💡 Run data preprocessing scripts first")
        return False
    
    # Check for parquet files in subdirectories (e.g., parquet_processed/XRPUSDC/5m/)
    parquet_files = list(parquet_dir.rglob('*.parquet'))
    if not parquet_files:
        print("❌ No parquet data files found in parquet_processed/")
        print("💡 Run data preprocessing scripts first")
        return False
    
    # Count files by timeframe
    timeframes = {}
    for file in parquet_files:
        tf = file.parent.name
        timeframes[tf] = timeframes.get(tf, 0) + 1
    
    print(f"✅ Found {len(parquet_files)} historical data files")
    for tf, count in timeframes.items():
        print(f"   - {tf}: {count} files")
    return True

def main():
    """Main startup function."""
    print("=" * 60)
    print("🚀 SCALPEL 1s DECISION FREQUENCY TRADING SYSTEM")
    print("=" * 60)
    print()
    
    # Step 1: Check requirements
    print("🔍 Checking system requirements...")
    if not check_requirements():
        sys.exit(1)
    print()
    
    # Step 2: Check API keys
    print("🔑 Checking API key configuration...")
    api_keys_ok = check_api_keys()
    print()
    
    # Step 3: Check model files
    print("🤖 Checking trained model files...")
    model_ok = check_model_files()
    print()
    
    # Step 4: Check data files
    print("📊 Checking historical data files...")
    data_ok = check_data_files()
    print()
    
    # Summary and next steps
    if api_keys_ok and model_ok and data_ok:
        print("✅ All system checks passed!")
        print()
        print("🎯 Ready to start trading! Choose your mode:")
        print()
        print("🧪 TEST MODE (recommended first):")
        print("   python live_trading.py --cfg strategyConfig_scalp_1s.json --use-1s-decisions")
        print()
        print("💰 LIVE MODE (real money):")
        print("   1. Edit strategyConfig_scalp_1s.json and set 'testMode': false")
        print("   2. python live_trading.py --cfg strategyConfig_scalp_1s.json --use-1s-decisions")
        print()
        print("🔧 TEST SINGLE TRADE:")
        print("   python live_trading.py --cfg strategyConfig_scalp_1s.json --test-trade")
        print()
        print("📝 Notes:")
        print("   - System will automatically fallback to Binance data if CoinAPI quota is exceeded")
        print("   - Press Ctrl+C to stop trading safely")
        print("   - Check live_trading.log for detailed logs")
        print("   - Current risk: 2% per trade (conservative setting)")
        print()
        
    else:
        print("❌ System not ready for trading.")
        print("Please fix the issues above before starting.")
        sys.exit(1)

if __name__ == "__main__":
    main()