#!/usr/bin/env python3
"""
Data quality fix script for trading simulation.
Identifies and fixes zero/invalid values in OHLC data.
"""

import pandas as pd
import numpy as np
from pathlib import Path
import argparse
import logging

logging.basicConfig(level=logging.INFO)
log = logging.getLogger(__name__)

def fix_ohlc_data(df):
    """
    Fix invalid OHLC data by replacing zero/invalid values.
    
    Args:
        df: DataFrame with OHLC columns
        
    Returns:
        Fixed DataFrame
    """
    df_fixed = df.copy()
    
    # Required OHLC columns
    ohlc_cols = ['open', 'high', 'low', 'close']
    missing_cols = [col for col in ohlc_cols if col not in df_fixed.columns]
    
    if missing_cols:
        log.warning(f"Missing OHLC columns: {missing_cols}")
        return df_fixed
    
    # Count invalid values before fixing
    invalid_counts = {}
    for col in ohlc_cols:
        invalid_mask = (df_fixed[col] <= 0) | pd.isna(df_fixed[col])
        invalid_counts[col] = invalid_mask.sum()
    
    log.info(f"Invalid values found: {invalid_counts}")
    
    # Fix strategy: forward fill from previous valid values
    for col in ohlc_cols:
        # Replace zeros and NaN with NaN first
        df_fixed[col] = df_fixed[col].replace(0.0, np.nan)
        
        # Forward fill
        df_fixed[col] = df_fixed[col].ffill()
        
        # If still NaN at beginning, backward fill
        df_fixed[col] = df_fixed[col].bfill()
    
    # Validate OHLC relationships
    # High should be >= max(open, close)
    # Low should be <= min(open, close)
    
    for i in df_fixed.index:
        row = df_fixed.loc[i]
        o, h, l, c = row['open'], row['high'], row['low'], row['close']
        
        # Fix high if too low
        expected_high = max(o, c)
        if h < expected_high:
            df_fixed.loc[i, 'high'] = expected_high
            
        # Fix low if too high  
        expected_low = min(o, c)
        if l > expected_low:
            df_fixed.loc[i, 'low'] = expected_low
    
    # Count remaining invalid values
    fixed_counts = {}
    for col in ohlc_cols:
        invalid_mask = (df_fixed[col] <= 0) | pd.isna(df_fixed[col])
        fixed_counts[col] = invalid_mask.sum()
    
    log.info(f"Invalid values after fix: {fixed_counts}")
    
    return df_fixed

def main():
    parser = argparse.ArgumentParser(description="Fix OHLC data quality issues")
    parser.add_argument("--file", required=True, help="Path to parquet file to fix")
    parser.add_argument("--output", help="Output file path (default: overwrites input)")
    parser.add_argument("--check-only", action="store_true", help="Only check for issues, don't fix")
    args = parser.parse_args()
    
    input_path = Path(args.file)
    if not input_path.exists():
        log.error(f"File not found: {input_path}")
        return 1
    
    log.info(f"Loading data from: {input_path}")
    df = pd.read_parquet(input_path)
    
    if 'timestamp' in df.columns and not isinstance(df.index, pd.DatetimeIndex):
        df['timestamp'] = pd.to_datetime(df['timestamp'], utc=True)
        df = df.set_index('timestamp')
    
    log.info(f"Loaded {len(df)} rows")
    
    if args.check_only:
        ohlc_cols = ['open', 'high', 'low', 'close']
        for col in ohlc_cols:
            if col in df.columns:
                invalid_count = ((df[col] <= 0) | pd.isna(df[col])).sum()
                log.info(f"{col}: {invalid_count} invalid values")
        return 0
    
    # Fix the data
    df_fixed = fix_ohlc_data(df)
    
    # Save results
    output_path = Path(args.output) if args.output else input_path
    log.info(f"Saving fixed data to: {output_path}")
    df_fixed.to_parquet(output_path)
    
    log.info("Data quality fix completed!")
    return 0

if __name__ == "__main__":
    exit(main())