#!/usr/bin/env python3
"""
Test script pre overenie načítania konfigurácie daily limits
"""

import json
import sys
import os

def test_config_loading():
    """Test načítania konfigurácie"""
    
    config_file = "strategyConfig_scalp_1s.json"
    
    print(f"🧪 Testovanie načítania konfigurácie z {config_file}...")
    
    try:
        with open(config_file, 'r') as f:
            config = json.load(f)
        
        print("✅ Konfigurácia úspešne načítaná!")
        
        # Test riskManagement sekcie
        risk_mgmt = config.get('riskManagement', {})
        print(f"\n📊 riskManagement sekcia:")
        print(f"   Celá sekcia: {risk_mgmt}")
        print(f"   maxDailyRiskPercent: {risk_mgmt.get('maxDailyRiskPercent', 'NOT_FOUND')}")
        
        # Test top-level daily limits
        print(f"\n📊 Top-level daily limits:")
        print(f"   maxDailyTrades: {config.get('maxDailyTrades', 'NOT_FOUND')}")
        print(f"   maxDailyAtrLossMultiplier: {config.get('maxDailyAtrLossMultiplier', 'NOT_FOUND')}")
        
        # Simulácia načítania ako v kóde
        max_daily_risk_r_mult = risk_mgmt.get('maxDailyRiskPercent', 999999.0)
        max_daily_trades = config.get('maxDailyTrades', 999999)
        max_daily_atr_loss_multiplier = config.get('maxDailyAtrLossMultiplier', 999999.0)
        
        print(f"\n🎯 Finálne hodnoty (ako v kóde):")
        print(f"   max_daily_risk_r_mult: {max_daily_risk_r_mult}")
        print(f"   max_daily_trades: {max_daily_trades}")
        print(f"   max_daily_atr_loss_multiplier: {max_daily_atr_loss_multiplier}")
        
        # Test daily limits logiky
        print(f"\n🧮 Test daily limits logiky:")
        
        # Simulácia hodnôt po jednom obchode
        daily_risk_used = -1.05  # Jeden stratový obchod -1.05R
        daily_pnl = -2.10        # Strata $2.10
        daily_trade_count = 1    # Jeden obchod
        current_atr = 0.012      # ATR 0.012
        
        print(f"   Simulované hodnoty po jednom obchode:")
        print(f"     daily_risk_used: {daily_risk_used}")
        print(f"     daily_pnl: {daily_pnl}")
        print(f"     daily_trade_count: {daily_trade_count}")
        print(f"     current_atr: {current_atr}")
        
        # Test podmienok
        risk_limit_hit = abs(daily_risk_used) >= max_daily_risk_r_mult
        atr_loss_limit = max_daily_atr_loss_multiplier * current_atr
        atr_limit_hit = (current_atr > 0 and abs(daily_pnl) >= atr_loss_limit)
        trade_count_limit_hit = daily_trade_count >= max_daily_trades
        
        print(f"\n🔍 Kontrola podmienok:")
        print(f"   Risk limit: |{daily_risk_used}| >= {max_daily_risk_r_mult}? {risk_limit_hit}")
        print(f"   ATR limit: |{daily_pnl}| >= {atr_loss_limit:.6f}? {atr_limit_hit}")
        print(f"   Trade count: {daily_trade_count} >= {max_daily_trades}? {trade_count_limit_hit}")
        
        any_limit_hit = risk_limit_hit or atr_limit_hit or trade_count_limit_hit
        print(f"   Akýkoľvek limit prekročený? {any_limit_hit}")
        
        if any_limit_hit:
            print("❌ PROBLÉM: Daily limits by sa aktivovali po jednom obchode!")
            if risk_limit_hit:
                print(f"   → Risk limit problém: {abs(daily_risk_used)} >= {max_daily_risk_r_mult}")
            if atr_limit_hit:
                print(f"   → ATR limit problém: {abs(daily_pnl)} >= {atr_loss_limit}")
            if trade_count_limit_hit:
                print(f"   → Trade count problém: {daily_trade_count} >= {max_daily_trades}")
            return False
        else:
            print("✅ ÚSPECH: Daily limits by sa neaktivovali po jednom obchode!")
            return True
            
    except Exception as e:
        print(f"❌ CHYBA pri načítaní konfigurácie: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Spúšťam test načítania konfigurácie...\n")
    
    success = test_config_loading()
    
    if success:
        print("\n🎉 Test prešiel úspešne!")
        sys.exit(0)
    else:
        print("\n💥 Test zlyhal!")
        sys.exit(1)
