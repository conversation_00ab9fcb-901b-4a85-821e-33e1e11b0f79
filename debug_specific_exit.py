#!/usr/bin/env python3
"""
Debug script pre analýzu konkrétneho problematického exitu:
EXIT 347.5110 u S @ 2.15730 (R: SL, Trig: 2.15666) s PnL: 3.02

Tento script nájde vstup tohto obchodu a overí SL logiku.
"""

import pandas as pd
import numpy as np
from datetime import datetime, timezone
from pathlib import Path

def debug_specific_exit():
    """Analyzuje konkrétny problematický exit z logu."""
    
    print("🔍 ANALÝZA KONKRÉTNEHO PROBLEMATICKÉHO EXITU")
    print("=" * 60)
    
    # Údaje z logu
    exit_time = "2025-07-02 01:17:43+00:00"
    exit_price = 2.15730
    sl_trigger = 2.15666
    pnl = 3.02
    position_size = 347.5110
    position_type = "SHORT"
    
    print(f"Exit Time: {exit_time}")
    print(f"Exit Price: {exit_price}")
    print(f"SL Trigger: {sl_trigger}")
    print(f"PnL: {pnl}")
    print(f"Position Size: {position_size}")
    print(f"Position Type: {position_type}")
    print()
    
    # Výpočet entry price z PnL formuly
    # PnL = position_size * (exit_price - entry_price) * position
    # Pre SHORT: position = -1
    # 3.02 = 347.5110 * (2.15730 - entry_price) * (-1)
    # 3.02 = 347.5110 * (entry_price - 2.15730)
    # entry_price - 2.15730 = 3.02 / 347.5110
    # entry_price = 2.15730 + (3.02 / 347.5110)
    
    calculated_entry = exit_price + (pnl / position_size)
    print(f"🧮 VYPOČÍTANÁ ENTRY CENA Z PnL:")
    print(f"Entry Price (calculated): {calculated_entry:.5f}")
    print()
    
    # Overenie PnL kalkulácie
    pnl_check = position_size * (exit_price - calculated_entry) * (-1)  # position = -1 pre SHORT
    print(f"✅ OVERENIE PnL KALKULÁCIE:")
    print(f"PnL check: {position_size:.4f} * ({exit_price:.5f} - {calculated_entry:.5f}) * (-1) = {pnl_check:.2f}")
    print(f"PnL from log: {pnl:.2f}")
    print(f"Match: {'✅' if abs(pnl_check - pnl) < 0.01 else '❌'}")
    print()
    
    # Analýza SL logiky
    print(f"🚨 ANALÝZA SL LOGIKY:")
    print(f"Entry Price: {calculated_entry:.5f}")
    print(f"SL Trigger: {sl_trigger:.5f}")
    print(f"Exit Price: {exit_price:.5f}")
    print()
    
    sl_distance_from_entry = sl_trigger - calculated_entry
    print(f"SL distance from entry: {sl_distance_from_entry:.5f}")
    
    if sl_distance_from_entry > 0:
        print("✅ SL je VYŠŠIE ako entry (správne pre SHORT)")
    else:
        print("❌ SL je NIŽŠIE ako entry (NESPRÁVNE pre SHORT)")
    print()
    
    # Analýza prečo mal SHORT pozitívny PnL
    print(f"🤔 PREČO MÁ SHORT POZITÍVNY PnL?")
    print(f"Exit price ({exit_price:.5f}) vs Entry price ({calculated_entry:.5f})")
    
    price_change = exit_price - calculated_entry
    print(f"Price change: {price_change:.5f}")
    
    if price_change < 0:
        print("✅ Cena klesla → SHORT má profit (normálne)")
        print("💡 Ale prečo sa spustil SL, keď SHORT má profit?")
    else:
        print("❌ Cena stúpla → SHORT by mal mať stratu")
        print("🚨 SL logika je určite nesprávna!")
    print()
    
    # Načítanie 1s dát pre overenie
    print("📊 OVERENIE Z 1S DÁT:")
    try:
        data_path = Path("parquet_processed/XRPUSDC/1s/2025-07-02.parquet")
        if data_path.exists():
            df = pd.read_parquet(data_path)
            df['timestamp'] = pd.to_datetime(df['timestamp'], utc=True)
            df = df.set_index('timestamp')
            
            # Nájdi údaje okolo exit času
            exit_dt = pd.to_datetime(exit_time)
            
            # Vezmi 5 minút pred a po exite
            start_time = exit_dt - pd.Timedelta(minutes=5)
            end_time = exit_dt + pd.Timedelta(minutes=2)
            
            relevant_data = df.loc[start_time:end_time]
            
            if not relevant_data.empty:
                print(f"Našiel som {len(relevant_data)} 1s barov okolo exit času")
                
                # Nájdi exact exit bar
                exit_bar = df.loc[df.index <= exit_dt].iloc[-1] if len(df.loc[df.index <= exit_dt]) > 0 else None
                
                if exit_bar is not None:
                    print(f"Exit bar data:")
                    print(f"  Time: {exit_bar.name}")
                    print(f"  OHLC: {exit_bar['open']:.5f} / {exit_bar['high']:.5f} / {exit_bar['low']:.5f} / {exit_bar['close']:.5f}")
                    print(f"  Exit price from log: {exit_price:.5f}")
                    
                    # Overenie či exit price zodpovedá market dátam
                    if exit_bar['low'] <= exit_price <= exit_bar['high']:
                        print("✅ Exit price je v rámci H/L tohto baru")
                    else:
                        print("❌ Exit price je MIMO H/L tohto baru!")
                        
        else:
            print(f"❌ Súbor {data_path} neexistuje")
            
    except Exception as e:
        print(f"❌ Chyba pri načítavaní dát: {e}")
    
    print()
    print("🎯 ZÁVER:")
    if pnl > 0 and position_type == "SHORT":
        print("❌ SHORT pozícia má pozitívny PnL pri SL exit - TO JE PROBLÉM!")
        print("💡 SL logika je nesprávna alebo entry/exit prices sú chybné")
    
    return {
        'calculated_entry': calculated_entry,
        'sl_trigger': sl_trigger,
        'exit_price': exit_price,
        'pnl': pnl,
        'sl_distance_from_entry': sl_distance_from_entry
    }

if __name__ == "__main__":
    result = debug_specific_exit()