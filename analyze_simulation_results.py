#!/usr/bin/env python3
"""
Komplexná analýza výsledkov zo simulate_trading.py
Analyzuje trades a equity curve, vytvára detailné grafy a štatistiky.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import seaborn as sns
from datetime import datetime, timedelta
from pathlib import Path
import argparse

# Nastavenie slovenčiny a lepšieho štýlu
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

def analyze_simulation_results(trades_file="backtest_trades.csv", equity_file="backtest_equity.csv", output_dir="simulation_analysis"):
    """
    Kompletná analýza výsledkov simulácie s vytvorením grafov a reportu.
    """
    print(f"🔍 Analyzujem výsledky simulácie...")
    print(f"📊 Trades súbor: {trades_file}")
    print(f"📈 Equity súbor: {equity_file}")
    
    # Vytvorenie výstupného adresára
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    # === NAČÍTANIE DÁT ===
    try:
        trades_df = pd.read_csv(trades_file)
        print(f"✅ Načítaných {len(trades_df)} obchodov")
    except FileNotFoundError:
        print(f"❌ Súbor {trades_file} neexistuje!")
        return
    except Exception as e:
        print(f"❌ Chyba pri načítavaní trades: {e}")
        return
    
    try:
        equity_df = pd.read_csv(equity_file)
        equity_df['timestamp'] = pd.to_datetime(equity_df['timestamp'])
        equity_df.set_index('timestamp', inplace=True)
        print(f"✅ Načítaných {len(equity_df)} equity bodov")
    except FileNotFoundError:
        print(f"⚠️ Súbor {equity_file} neexistuje, použijem len trades analýzu")
        equity_df = pd.DataFrame()
    except Exception as e:
        print(f"⚠️ Chyba pri načítavaní equity: {e}")
        equity_df = pd.DataFrame()
    
    # === PRÍPRAVA TRADES DÁT ===
    if not trades_df.empty:
        trades_df['entry_time'] = pd.to_datetime(trades_df['entry_time'])
        trades_df['exit_time'] = pd.to_datetime(trades_df['exit_time'])
        trades_df['duration'] = (trades_df['exit_time'] - trades_df['entry_time']).dt.total_seconds() / 60  # minúty
        trades_df['hour'] = trades_df['entry_time'].dt.hour
        trades_df['dow'] = trades_df['entry_time'].dt.dayofweek  # 0=Pondelok, 6=Nedeľa
        trades_df['date'] = trades_df['entry_time'].dt.date
        trades_df['is_win'] = trades_df['pnl'] > 0
        trades_df['r_multiple'] = trades_df['pnl'] / (trades_df['entry_price'] * trades_df['size'] * 0.01)  # Približný R-multiple
        
    # === ZÁKLADNÉ ŠTATISTIKY ===
    print("\n" + "="*60)
    print("📊 ZÁKLADNÉ ŠTATISTIKY")
    print("="*60)
    
    if not trades_df.empty:
        total_trades = len(trades_df)
        wins = trades_df[trades_df['pnl'] > 0]
        losses = trades_df[trades_df['pnl'] <= 0]
        
        win_rate = (len(wins) / total_trades * 100) if total_trades > 0 else 0
        total_pnl = trades_df['pnl'].sum()
        avg_win = wins['pnl'].mean() if len(wins) > 0 else 0
        avg_loss = losses['pnl'].mean() if len(losses) > 0 else 0
        profit_factor = abs(wins['pnl'].sum() / losses['pnl'].sum()) if len(losses) > 0 and losses['pnl'].sum() != 0 else float('inf')
        avg_duration = trades_df['duration'].mean()
        
        print(f"Celkový počet obchodov: {total_trades}")
        print(f"Výherné obchody: {len(wins)} ({win_rate:.2f}%)")
        print(f"Prehrané obchody: {len(losses)} ({100-win_rate:.2f}%)")
        print(f"Celkový PnL: ${total_pnl:.2f}")
        print(f"Priemerný zisk: ${avg_win:.2f}")
        print(f"Priemerná strata: ${avg_loss:.2f}")
        print(f"Profit Factor: {profit_factor:.2f}")
        print(f"Priemerná dĺžka obchodu: {avg_duration:.1f} minút")
        print(f"Najväčší zisk: ${trades_df['pnl'].max():.2f}")
        print(f"Najväčšia strata: ${trades_df['pnl'].min():.2f}")
        print(f"Celkové poplatky: ${(trades_df['entry_fee'].sum() + trades_df['exit_fee'].sum()):.2f}")
        
        # R-multiple analýza
        if 'r_multiple' in trades_df.columns:
            avg_r = trades_df['r_multiple'].mean()
            print(f"Priemerný R-multiple: {avg_r:.2f}R")
    
    # === ANALÝZA PODĽA ČASU ===
    print("\n" + "="*60)
    print("⏰ ANALÝZA PODĽA ČASU")
    print("="*60)
    
    if not trades_df.empty:
        # Analýza podľa hodín
        hourly_stats = trades_df.groupby('hour').agg({
            'pnl': ['count', 'sum', 'mean'],
            'is_win': 'mean'
        }).round(3)
        hourly_stats.columns = ['count', 'total_pnl', 'avg_pnl', 'win_rate']
        hourly_stats['win_rate'] *= 100
        
        print("\nVýkonnosť podľa hodín (UTC):")
        print(hourly_stats)
        
        # Analýza podľa dní v týždni
        dow_names = ['Pondelok', 'Utorok', 'Streda', 'Štvrtok', 'Piatok', 'Sobota', 'Nedeľa']
        weekly_stats = trades_df.groupby('dow').agg({
            'pnl': ['count', 'sum', 'mean'],
            'is_win': 'mean'
        }).round(3)
        weekly_stats.columns = ['count', 'total_pnl', 'avg_pnl', 'win_rate']
        weekly_stats['win_rate'] *= 100
        weekly_stats.index = [dow_names[i] for i in weekly_stats.index]
        
        print("\nVýkonnosť podľa dní v týždni:")
        print(weekly_stats)
    
    # === ANALÝZA EXIT DÔVODOV ===
    print("\n" + "="*60)
    print("🚪 ANALÝZA EXIT DÔVODOV")
    print("="*60)
    
    if not trades_df.empty and 'exit_reason' in trades_df.columns:
        exit_analysis = trades_df.groupby('exit_reason').agg({
            'pnl': ['count', 'sum', 'mean'],
            'is_win': 'mean'
        }).round(3)
        exit_analysis.columns = ['count', 'total_pnl', 'avg_pnl', 'win_rate']
        exit_analysis['win_rate'] *= 100
        
        print("Analýza podľa exit dôvodov:")
        print(exit_analysis)
    
    # === VYTVORENIE GRAFOV ===
    print(f"\n📈 Vytváram grafy...")
    
    # 1. EQUITY CURVE
    if not equity_df.empty:
        plt.figure(figsize=(15, 8))
        plt.subplot(2, 1, 1)
        plt.plot(equity_df.index, equity_df['equity'], linewidth=2, color='blue')
        plt.title('Equity Curve', fontsize=16, fontweight='bold')
        plt.ylabel('Equity ($)')
        plt.grid(True, alpha=0.3)
        
        # Drawdown
        running_max = equity_df['equity'].expanding().max()
        drawdown = ((equity_df['equity'] - running_max) / running_max * 100)
        
        plt.subplot(2, 1, 2)
        plt.fill_between(equity_df.index, drawdown, 0, color='red', alpha=0.3)
        plt.plot(equity_df.index, drawdown, color='red', linewidth=1)
        plt.title('Drawdown (%)', fontsize=16, fontweight='bold')
        plt.ylabel('Drawdown (%)')
        plt.xlabel('Čas')
        plt.grid(True, alpha=0.3)
        
        max_dd = drawdown.min()
        print(f"Maximálny Drawdown: {max_dd:.2f}%")
        
        plt.tight_layout()
        plt.savefig(output_path / "equity_curve.png", dpi=300, bbox_inches='tight')
        plt.close()
    
    # 2. PNL DISTRIBÚCIA
    if not trades_df.empty:
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # Histogram PnL
        axes[0, 0].hist(trades_df['pnl'], bins=30, alpha=0.7, color='skyblue', edgecolor='black')
        axes[0, 0].axvline(0, color='red', linestyle='--', linewidth=2)
        axes[0, 0].set_title('Distribúcia PnL', fontweight='bold')
        axes[0, 0].set_xlabel('PnL ($)')
        axes[0, 0].set_ylabel('Počet obchodov')
        axes[0, 0].grid(True, alpha=0.3)
        
        # Kumulatívny PnL
        cumulative_pnl = trades_df['pnl'].cumsum()
        axes[0, 1].plot(range(len(cumulative_pnl)), cumulative_pnl, linewidth=2, color='green')
        axes[0, 1].set_title('Kumulatívny PnL', fontweight='bold')
        axes[0, 1].set_xlabel('Číslo obchodu')
        axes[0, 1].set_ylabel('Kumulatívny PnL ($)')
        axes[0, 1].grid(True, alpha=0.3)
        
        # Dĺžka obchodov
        axes[1, 0].hist(trades_df['duration'], bins=30, alpha=0.7, color='orange', edgecolor='black')
        axes[1, 0].set_title('Distribúcia dĺžky obchodov', fontweight='bold')
        axes[1, 0].set_xlabel('Dĺžka (minúty)')
        axes[1, 0].set_ylabel('Počet obchodov')
        axes[1, 0].grid(True, alpha=0.3)
        
        # Win/Loss ratio
        win_loss_data = ['Výhra' if x > 0 else 'Strata' for x in trades_df['pnl']]
        win_loss_counts = pd.Series(win_loss_data).value_counts()
        axes[1, 1].pie(win_loss_counts.values, labels=win_loss_counts.index, autopct='%1.1f%%', startangle=90)
        axes[1, 1].set_title('Pomer výher a strát', fontweight='bold')
        
        plt.tight_layout()
        plt.savefig(output_path / "pnl_analysis.png", dpi=300, bbox_inches='tight')
        plt.close()
    
    # 3. ČASOVÁ ANALÝZA
    if not trades_df.empty:
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # PnL podľa hodín
        hourly_pnl = trades_df.groupby('hour')['pnl'].sum()
        axes[0, 0].bar(hourly_pnl.index, hourly_pnl.values, color='lightblue', edgecolor='black')
        axes[0, 0].set_title('Celkový PnL podľa hodín (UTC)', fontweight='bold')
        axes[0, 0].set_xlabel('Hodina')
        axes[0, 0].set_ylabel('PnL ($)')
        axes[0, 0].grid(True, alpha=0.3)
        
        # Win rate podľa hodín
        hourly_winrate = trades_df.groupby('hour')['is_win'].mean() * 100
        axes[0, 1].bar(hourly_winrate.index, hourly_winrate.values, color='lightgreen', edgecolor='black')
        axes[0, 1].axhline(50, color='red', linestyle='--', linewidth=2)
        axes[0, 1].set_title('Win Rate podľa hodín (%)', fontweight='bold')
        axes[0, 1].set_xlabel('Hodina')
        axes[0, 1].set_ylabel('Win Rate (%)')
        axes[0, 1].grid(True, alpha=0.3)
        
        # Počet obchodov podľa dní v týždni
        dow_counts = trades_df.groupby('dow').size()
        dow_labels = ['Po', 'Ut', 'St', 'Št', 'Pi', 'So', 'Ne']
        axes[1, 0].bar(range(len(dow_counts)), dow_counts.values, color='coral', edgecolor='black')
        axes[1, 0].set_title('Počet obchodov podľa dní v týždni', fontweight='bold')
        axes[1, 0].set_xlabel('Deň v týždni')
        axes[1, 0].set_ylabel('Počet obchodov')
        axes[1, 0].set_xticks(range(len(dow_labels)))
        axes[1, 0].set_xticklabels(dow_labels)
        axes[1, 0].grid(True, alpha=0.3)
        
        # PnL podľa dní v týždni
        dow_pnl = trades_df.groupby('dow')['pnl'].sum()
        axes[1, 1].bar(range(len(dow_pnl)), dow_pnl.values, color='gold', edgecolor='black')
        axes[1, 1].set_title('Celkový PnL podľa dní v týždni', fontweight='bold')
        axes[1, 1].set_xlabel('Deň v týždni')
        axes[1, 1].set_ylabel('PnL ($)')
        axes[1, 1].set_xticks(range(len(dow_labels)))
        axes[1, 1].set_xticklabels(dow_labels)
        axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(output_path / "time_analysis.png", dpi=300, bbox_inches='tight')
        plt.close()
    
    # 4. EXIT REASONS ANALÝZA
    if not trades_df.empty and 'exit_reason' in trades_df.columns:
        fig, axes = plt.subplots(1, 2, figsize=(15, 6))
        
        # Počet obchodov podľa exit reason
        exit_counts = trades_df['exit_reason'].value_counts()
        axes[0].pie(exit_counts.values, labels=exit_counts.index, autopct='%1.1f%%', startangle=90)
        axes[0].set_title('Distribúcia Exit Dôvodov', fontweight='bold')
        
        # PnL podľa exit reason
        exit_pnl = trades_df.groupby('exit_reason')['pnl'].sum().sort_values()
        colors = ['red' if x < 0 else 'green' for x in exit_pnl.values]
        axes[1].barh(range(len(exit_pnl)), exit_pnl.values, color=colors, alpha=0.7)
        axes[1].set_title('PnL podľa Exit Dôvodov', fontweight='bold')
        axes[1].set_xlabel('PnL ($)')
        axes[1].set_yticks(range(len(exit_pnl)))
        axes[1].set_yticklabels(exit_pnl.index)
        axes[1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(output_path / "exit_analysis.png", dpi=300, bbox_inches='tight')
        plt.close()
    
    # 5. POKROČILÉ ANALÝZY
    if not trades_df.empty:
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # Running win rate
        running_winrate = trades_df['is_win'].expanding().mean() * 100
        axes[0, 0].plot(range(len(running_winrate)), running_winrate, linewidth=2, color='purple')
        axes[0, 0].axhline(50, color='red', linestyle='--', linewidth=2)
        axes[0, 0].set_title('Vývoj Win Rate', fontweight='bold')
        axes[0, 0].set_xlabel('Číslo obchodu')
        axes[0, 0].set_ylabel('Win Rate (%)')
        axes[0, 0].grid(True, alpha=0.3)
        
        # Long vs Short performance
        if 'direction' in trades_df.columns:
            direction_stats = trades_df.groupby('direction').agg({
                'pnl': ['count', 'sum', 'mean'],
                'is_win': 'mean'
            })
            direction_stats.columns = ['count', 'total_pnl', 'avg_pnl', 'win_rate']
            direction_pnl = direction_stats['total_pnl']
            axes[0, 1].bar(direction_pnl.index, direction_pnl.values, color=['blue', 'orange'], alpha=0.7)
            axes[0, 1].set_title('PnL: Long vs Short', fontweight='bold')
            axes[0, 1].set_ylabel('PnL ($)')
            axes[0, 1].grid(True, alpha=0.3)
        
        # R-multiple distribúcia
        if 'r_multiple' in trades_df.columns:
            axes[1, 0].hist(trades_df['r_multiple'], bins=30, alpha=0.7, color='cyan', edgecolor='black')
            axes[1, 0].axvline(0, color='red', linestyle='--', linewidth=2)
            axes[1, 0].axvline(trades_df['r_multiple'].mean(), color='green', linestyle='--', linewidth=2, label=f'Priemer: {trades_df["r_multiple"].mean():.2f}R')
            axes[1, 0].set_title('Distribúcia R-Multiple', fontweight='bold')
            axes[1, 0].set_xlabel('R-Multiple')
            axes[1, 0].set_ylabel('Počet obchodov')
            axes[1, 0].legend()
            axes[1, 0].grid(True, alpha=0.3)
        
        # Streaks analýza
        trades_df['streak_id'] = (trades_df['is_win'] != trades_df['is_win'].shift()).cumsum()
        streaks = trades_df.groupby('streak_id').agg({
            'is_win': ['first', 'count']
        })
        streaks.columns = ['is_win', 'length']
        win_streaks = streaks[streaks['is_win'] == True]['length']
        loss_streaks = streaks[streaks['is_win'] == False]['length']
        
        streak_data = []
        if len(win_streaks) > 0:
            streak_data.extend(['Win'] * len(win_streaks))
        if len(loss_streaks) > 0:
            streak_data.extend(['Loss'] * len(loss_streaks))
        
        if streak_data:
            axes[1, 1].hist([win_streaks.values, loss_streaks.values], bins=20, alpha=0.7, 
                           label=['Win Streaks', 'Loss Streaks'], color=['green', 'red'])
            axes[1, 1].set_title('Distribúcia Streaks', fontweight='bold')
            axes[1, 1].set_xlabel('Dĺžka streak')
            axes[1, 1].set_ylabel('Počet')
            axes[1, 1].legend()
            axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(output_path / "advanced_analysis.png", dpi=300, bbox_inches='tight')
        plt.close()
    
    # === VYTVORENIE TEXTOVÉHO REPORTU ===
    report_path = output_path / "trading_report.txt"
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write("=" * 80 + "\n")
        f.write("KOMPLEXNÝ REPORT VÝSLEDKOV SIMULÁCIE\n")
        f.write("=" * 80 + "\n\n")
        
        f.write(f"📊 Analyzované súbory:\n")
        f.write(f"   - Trades: {trades_file}\n")
        f.write(f"   - Equity: {equity_file}\n\n")
        
        if not trades_df.empty:
            f.write("📈 ZÁKLADNÉ ŠTATISTIKY:\n")
            f.write("-" * 40 + "\n")
            f.write(f"Celkový počet obchodov: {total_trades}\n")
            f.write(f"Výherné obchody: {len(wins)} ({win_rate:.2f}%)\n")
            f.write(f"Prehrané obchody: {len(losses)} ({100-win_rate:.2f}%)\n")
            f.write(f"Celkový PnL: ${total_pnl:.2f}\n")
            f.write(f"Priemerný zisk: ${avg_win:.2f}\n")
            f.write(f"Priemerná strata: ${avg_loss:.2f}\n")
            f.write(f"Profit Factor: {profit_factor:.2f}\n")
            f.write(f"Priemerná dĺžka obchodu: {avg_duration:.1f} minút\n\n")
            
            f.write("⏰ NAJLEPŠIE A NAJHORŠIE HODINY:\n")
            f.write("-" * 40 + "\n")
            if not hourly_stats.empty:
                best_hour = hourly_stats['total_pnl'].idxmax()
                worst_hour = hourly_stats['total_pnl'].idxmin()
                f.write(f"Najlepšia hodina: {best_hour}:00 UTC (PnL: ${hourly_stats.loc[best_hour, 'total_pnl']:.2f})\n")
                f.write(f"Najhoršia hodina: {worst_hour}:00 UTC (PnL: ${hourly_stats.loc[worst_hour, 'total_pnl']:.2f})\n\n")
            
            f.write("🎯 ODPORÚČANIA:\n")
            f.write("-" * 40 + "\n")
            if win_rate < 50:
                f.write("⚠️  Win rate je pod 50%, zvážte úpravu entry stratégie\n")
            if profit_factor < 1.5:
                f.write("⚠️  Profit factor je nízky, zlepšite risk management\n")
            if not equity_df.empty and max_dd < -10:
                f.write(f"⚠️  Vysoký drawdown ({max_dd:.2f}%), znížte risk per trade\n")
            if avg_duration > 60:
                f.write("⚠️  Príliš dlhé obchody, zvážte skoršie exit signály\n")
    
    print(f"\n✅ Analýza dokončená!")
    print(f"📁 Výstupný adresár: {output_path}")
    print(f"📋 Vygenerované súbory:")
    print(f"   - equity_curve.png (equity curve a drawdown)")
    print(f"   - pnl_analysis.png (PnL distribúcia)")  
    print(f"   - time_analysis.png (časová analýza)")
    print(f"   - exit_analysis.png (analýza exit dôvodov)")
    print(f"   - advanced_analysis.png (pokročilé analýzy)")
    print(f"   - trading_report.txt (textový report)")

def main():
    parser = argparse.ArgumentParser(description="Analýza výsledkov simulácie tradingu")
    parser.add_argument("--trades", default="backtest_trades.csv", help="CSV súbor s obchodmi")
    parser.add_argument("--equity", default="backtest_equity.csv", help="CSV súbor s equity curve")
    parser.add_argument("--output", default="simulation_analysis", help="Výstupný adresár")
    
    args = parser.parse_args()
    
    analyze_simulation_results(args.trades, args.equity, args.output)

if __name__ == "__main__":
    main()