from __future__ import annotations

import argparse, logging, multiprocessing as mp, copy, re
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List

import pandas as pd
import pyarrow as pa, pyarrow.parquet as pq
import yaml

from indicators import calculate_and_merge_indicators

# ─────────── logging ─────────────────────────────────────────────
LOG = logging.getLogger("precompute")
LOG.setLevel(logging.INFO)
logging.basicConfig(
    format="%(asctime)s %(levelname)5s │ %(name)s │ %(message)s",
    datefmt="%H:%M:%S",
    level=logging.INFO,
)

# ─────────── helpers ─────────────────────────────────────────────
def _daterange(start: datetime, end: datetime):
    cur = start
    while cur <= end:
        yield cur
        cur += timedelta(days=1)


def _read_parquet_df(fp: Path) -> pd.DataFrame:
    df = pd.read_parquet(fp, engine="pyarrow")
    if "timestamp" in df.columns:
        df.set_index("timestamp", inplace=True)
    df.index = pd.to_datetime(df.index, utc=True)
    return df


def _load_frames_for_day(
        root: Path,
        symbol: str,
        date_str: str,
        cfg: dict
) -> Dict[str, pd.DataFrame]:
    """Načíta všetky TF, trades a orderbooks potrebné na výpočet."""
    frames: Dict[str, pd.DataFrame] = {}
    wanted = (
        [cfg["primaryTimeframe"]] if isinstance(cfg["primaryTimeframe"], str)
        else cfg["primaryTimeframe"]
    )
    # ďalšie TF vyžadované indikátormi (len ak je hodnota slovník)
    wanted += [s.get("tf", "") for s in cfg["indicatorSettings"].values() if isinstance(s, dict)]
    wanted += ["trades", "orderbooks"]

    for tf in {w for w in wanted if w}:
        # Hľadáme OHLCV / trades / orderbook súbor pre dané timeframe
        # Skúšame všetky možné cesty v poradí:
        possible_paths = [
            # 1. Primárna cesta: <root>/<symbol>/<tf>/<YYYY-MM-DD>.parquet
            root / symbol / tf / f"{date_str}.parquet",
            # 2. S medzikatalógom ohlcv: <root>/<symbol>/ohlcv/<tf>/<YYYY-MM-DD>.parquet
            root / symbol / "ohlcv" / tf / f"{date_str}.parquet",
            # 3. Priamo v ohlcv: <root>/ohlcv/<tf>/<YYYY-MM-DD>.parquet
            root / "ohlcv" / tf / f"{date_str}.parquet",
            # 4. Priamo v trades: <root>/trades/<YYYY-MM-DD>.parquet (pre tf="trades")
            root / "trades" / f"{date_str}.parquet" if tf == "trades" else None,
            # 5. Priamo v orderbooks: <root>/orderbooks/<YYYY-MM-DD>.parquet (pre tf="orderbooks")
            root / "orderbooks" / f"{date_str}.parquet" if tf == "orderbooks" else None
        ]
        
        # Odstráň None hodnoty
        possible_paths = [p for p in possible_paths if p is not None]
        
        # Skús nájsť prvý existujúci súbor
        fp = None
        for path in possible_paths:
            if path.exists():
                fp = path
                LOG.info(f"Found data for {tf} at {fp}")
                break
                
        if fp and fp.exists():
            frames[tf] = _read_parquet_df(fp)
        else:
            LOG.debug(f"No data found for {tf} on {date_str}. Tried paths: {possible_paths}")
            
    return frames


def _write_parquet(df: pd.DataFrame, fp: Path, overwrite: bool):
    fp.parent.mkdir(parents=True, exist_ok=True)
    if fp.exists() and not overwrite:
        raise FileExistsError(fp)
    pq.write_table(
        pa.Table.from_pandas(df.reset_index(), preserve_index=False),
        fp, compression="zstd", version="2.6"
    )


def _align_higher_tf_features(feat_df: pd.DataFrame, primary_index: pd.DatetimeIndex):
    # Cielime len na stĺpce ktoré končia na "_15m" (prípadne "_30m", "_1h" …)
    htf_pattern = re.compile(r"_((15m)|(30m)|(1h))$")
    htf_cols = [c for c in feat_df.columns if htf_pattern.search(c)]
    if not htf_cols:
        return feat_df

    # Na vyššej TF budú NaN aj vlastné „warm-up" riadky – tie ostanú
    for col in htf_cols:
        # Reindex to primary timeframe and forward fill
        feat_df[col] = feat_df[col].reindex(primary_index).ffill()

    return feat_df

# ─────────── worker – jedna TF + jeden deň ───────────────────────
def _process(args):
    (
        src_root, dst_root, symbol, tf, date_str,
        cfg_orig, overwrite,
        hmm_model, hmm_scaler
    ) = args
    try:
        # 1) načítaj všetky zdroje pre daný deň
        frames = _load_frames_for_day(src_root, symbol, date_str, cfg_orig)
        if not frames:
            return f"{tf} {date_str}  – skip (žiadne dáta)"

        # 2) lokálna kópia cfg s aktuálnym primaryTimeframe
        cfg = copy.deepcopy(cfg_orig)
        cfg["primaryTimeframe"] = tf

        # 3) výpočet indikátorov
        feat_df, cols = calculate_and_merge_indicators(
            frames, cfg,
            hmm_model_external=hmm_model,
            hmm_scaler_external=hmm_scaler,
        )
        if feat_df.empty:
            return f"{tf} {date_str}  – skip (prázdny DF)"

        feat_df = _align_higher_tf_features(feat_df, frames[cfg["primaryTimeframe"]].index)

        # Drop warmup NaNs
        feature_columns = [c for c in feat_df.columns if c not in ['open', 'high', 'low', 'close', 'volume']]
        feat_df = feat_df.dropna(subset=[col for col in feature_columns if not col.endswith(("_15m", "_30m", "_1h"))], how="any")

        # 4) zápis
        dst_root = dst_root or src_root
        fp_out = dst_root / symbol / tf / f"{date_str}.parquet"
        _write_parquet(feat_df, fp_out, overwrite)
        return f"{tf} {date_str}  – OK rows={len(feat_df):6d} cols={len(cols)}"
    except Exception as e:
        return f"{tf} {date_str}  – ERROR: {e}"

# ─────────── CLI ─────────────────────────────────────────────────
def _cli():
    p = argparse.ArgumentParser()
    p.add_argument("--root-dir", required=True, type=Path,
                   help="kde sú raw Parquety {symbol}/{tf}/YYYY-MM-DD.parquet")
    p.add_argument("--dst-dir",  type=Path,
                   help="výstupný adresár (default = in‑place)")
    p.add_argument("--symbol",   required=True)
    p.add_argument("--cfg",      required=True, type=Path)
    p.add_argument("--start",    required=True)   # YYYY‑MM‑DD
    p.add_argument("--end",      required=True)   # YYYY‑MM‑DD
    p.add_argument("--n-workers", default=max(1, mp.cpu_count()//2), type=int)
    p.add_argument("--overwrite", action="store_true")
    return p.parse_args()

# ─────────── main ────────────────────────────────────────────────
def main():
    ns = _cli()
    cfg = yaml.safe_load(open(ns.cfg, "rt"))

    # podporujeme string aj list
    primary_tfs: List[str] = (
        [cfg["primaryTimeframe"]]
        if isinstance(cfg["primaryTimeframe"], str)
        else list(cfg["primaryTimeframe"])
    )
    LOG.info("Primary timeframe(s): %s", ", ".join(primary_tfs))

    # pred‑inicializácia HMM (voliteľné – takto sa netrénuje znova v každom procese)
    hmm_model = None
    hmm_scaler = None

    # priprav pool‑tasky
    tasks = []
    for day in _daterange(datetime.fromisoformat(ns.start),
                          datetime.fromisoformat(ns.end)):
        date_str = day.date().isoformat()
        for tf in primary_tfs:
            # Check multiple possible paths for data files
            possible_paths = [
                ns.root_dir / ns.symbol / tf / f"{date_str}.parquet",
                ns.root_dir / ns.symbol / "ohlcv" / tf / f"{date_str}.parquet",
                ns.root_dir / "ohlcv" / tf / f"{date_str}.parquet"
            ]
            
            # Skip this task if no data file exists in any of the possible locations
            if not any(path.exists() for path in possible_paths):
                continue
            tasks.append((
                ns.root_dir, ns.dst_dir, ns.symbol, tf, date_str,
                cfg, ns.overwrite,
                hmm_model, hmm_scaler
            ))

    LOG.info("Celkom %d úloh (dni × TF) – štartujem %d worker‑ov",
             len(tasks), ns.n_workers)

    with mp.get_context("spawn").Pool(processes=ns.n_workers) as pool:
        for msg in pool.imap_unordered(_process, tasks):
            LOG.info(msg)


if __name__ == "__main__":
    main()
