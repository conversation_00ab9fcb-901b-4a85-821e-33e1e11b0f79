#!/usr/bin/env python3
"""
Test script pre overenie NaN opráv v simulate_trading_new.py
"""

import numpy as np
import pandas as pd
import sys
import os

# Pridaj aktuálny adresár do Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_nan_handling():
    """Test NaN handling logiky bez get_state funkcie"""

    print("🧪 Testovanie NaN handling logiky...")

    # Simulácia frame_array s NaN hodnotami
    lookback = 30
    n_features = 48

    frame_array = np.random.randn(lookback, n_features)
    frame_array[5:8, 10:15] = np.nan  # Pridáme NaN hodnoty
    frame_array[15:18, 20:25] = np.inf  # Pridáme Inf hodnoty

    print(f"📊 Test frame_array: {frame_array.shape}")
    print(f"   NaN count: {np.sum(np.isnan(frame_array))}")
    print(f"   Inf count: {np.sum(np.isinf(frame_array))}")

    try:
        # Aplikuj naše <PERSON> fixing
        if np.any(np.isnan(frame_array)) or np.any(np.isinf(frame_array)):
            nan_count = np.sum(np.isnan(frame_array))
            inf_count = np.sum(np.isinf(frame_array))
            print(f"   Fixing NaN: {nan_count}, Inf: {inf_count}")
            frame_array = np.nan_to_num(frame_array, nan=0.0, posinf=5.0, neginf=-5.0)

        # Clipping
        frame_flat_clipped = np.clip(frame_array, -5.0, 5.0).flatten()

        # Test additional features
        additional_features = [1.0, 0.5, 0.2, 1.0, 0.1, -0.1, 0.3]
        additional_features = [0.0 if not np.isfinite(x) else x for x in additional_features]

        # Test time features
        time_feats = [0.5, 0.3, 0.8, 0.1]
        time_feats = [0.0 if not np.isfinite(x) else x for x in time_feats]

        # Spojenie
        state_vector = np.concatenate((
            frame_flat_clipped,
            additional_features,
            time_feats
        )).astype(np.float32)

        print(f"✅ NaN handling úspešne dokončené!")
        print(f"   State vector shape: {state_vector.shape}")
        print(f"   NaN count v state: {np.sum(np.isnan(state_vector))}")
        print(f"   Inf count v state: {np.sum(np.isinf(state_vector))}")
        print(f"   Min value: {np.min(state_vector)}")
        print(f"   Max value: {np.max(state_vector)}")

        if np.any(np.isnan(state_vector)) or np.any(np.isinf(state_vector)):
            print("❌ CHYBA: State vector stále obsahuje NaN/Inf hodnoty!")
            return False
        else:
            print("✅ ÚSPECH: State vector neobsahuje NaN/Inf hodnoty!")
            return True

    except Exception as e:
        print(f"❌ CHYBA pri NaN handling: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_vecnormalize_simulation():
    """Test simulácie VecNormalize s NaN hodnotami"""
    
    print("\n🧪 Testovanie VecNormalize simulácie...")
    
    # Vytvoríme test state s NaN hodnotami
    state_size = 1451
    state_input = np.random.randn(1, state_size).astype(np.float32)
    
    # Pridáme NaN hodnoty
    state_input[0, 100:110] = np.nan
    state_input[0, 500:505] = np.inf
    
    print(f"📊 Test state: {state_input.shape}")
    print(f"   NaN count: {np.sum(np.isnan(state_input))}")
    print(f"   Inf count: {np.sum(np.isinf(state_input))}")
    
    # Simulácia VecNormalize normalizácie (môže vytvoriť NaN)
    # Normalizácia môže vytvoriť NaN ak má nulový std
    mean = np.nanmean(state_input, axis=0, keepdims=True)
    std = np.nanstd(state_input, axis=0, keepdims=True)
    std[std == 0] = 1.0  # Zabráň deleniu nulou
    
    normalized = (state_input - mean) / std
    
    print(f"   Po normalizácii - NaN count: {np.sum(np.isnan(normalized))}")
    print(f"   Po normalizácii - Inf count: {np.sum(np.isinf(normalized))}")
    
    # Aplikuj naše NaN fixing
    if np.any(np.isnan(normalized)) or np.any(np.isinf(normalized)):
        print("   Aplikujem nan_to_num fix...")
        normalized = np.nan_to_num(normalized, nan=0.0, posinf=5.0, neginf=-5.0)
    
    # Clipping
    clipped = np.clip(normalized, -5.0, 5.0).astype(np.float32)
    
    print(f"   Po clipping - NaN count: {np.sum(np.isnan(clipped))}")
    print(f"   Po clipping - Inf count: {np.sum(np.isinf(clipped))}")
    print(f"   Min value: {np.min(clipped)}")
    print(f"   Max value: {np.max(clipped)}")
    
    if np.any(np.isnan(clipped)) or np.any(np.isinf(clipped)):
        print("❌ CHYBA: Clipped state stále obsahuje NaN/Inf hodnoty!")
        return False
    else:
        print("✅ ÚSPECH: Clipped state neobsahuje NaN/Inf hodnoty!")
        return True

if __name__ == "__main__":
    print("🚀 Spúšťam NaN handling testy...\n")
    
    success1 = test_nan_handling()
    success2 = test_vecnormalize_simulation()
    
    print(f"\n📊 VÝSLEDKY TESTOV:")
    print(f"   get_state test: {'✅ PASS' if success1 else '❌ FAIL'}")
    print(f"   VecNormalize test: {'✅ PASS' if success2 else '❌ FAIL'}")
    
    if success1 and success2:
        print("\n🎉 Všetky testy prešli úspešne!")
        sys.exit(0)
    else:
        print("\n💥 Niektoré testy zlyhali!")
        sys.exit(1)
