#!/usr/bin/env python3
"""
Test script to verify API key loading and quota status
"""
import os
import requests
from dotenv import load_dotenv
from config_loader import ConfigLoader

def test_env_loading():
    """Test direct environment variable loading"""
    print("=== Testing Direct Environment Variable Loading ===")
    
    # Load .env file
    load_dotenv()
    
    # Get API key from environment
    api_key = os.getenv('COINAPI_KEY')
    if api_key:
        masked_key = f"{api_key[:8]}...{api_key[-4:]}" if len(api_key) > 12 else "INVALID_KEY"
        print(f"✅ Direct ENV loading: {masked_key}")
    else:
        print("❌ COINAPI_KEY not found in environment")
    
    return api_key

def test_config_loader():
    """Test ConfigLoader API key substitution"""
    print("\n=== Testing ConfigLoader API Key Substitution ===")
    
    try:
        loader = ConfigLoader()
        config = loader.load_config("strategyConfig_scalp_1s_improved.json")
        
        api_key = config.get("coinapi", {}).get("apiKey")
        if api_key:
            if api_key.startswith("${"):
                print(f"❌ Environment variable NOT substituted: {api_key}")
                return None
            else:
                masked_key = f"{api_key[:8]}...{api_key[-4:]}" if len(api_key) > 12 else "INVALID_KEY"
                print(f"✅ ConfigLoader substitution: {masked_key}")
                return api_key
        else:
            print("❌ API key not found in config")
            return None
            
    except Exception as e:
        print(f"❌ ConfigLoader failed: {e}")
        return None

def test_api_quota(api_key):
    """Test CoinAPI quota status"""
    print("\n=== Testing CoinAPI Quota Status ===")
    
    if not api_key:
        print("❌ No API key to test")
        return
    
    try:
        # Test with a minimal request
        url = "https://rest.coinapi.io/v1/exchangerate/USD/EUR"
        headers = {"X-CoinAPI-Key": api_key}
        
        response = requests.get(url, headers=headers, timeout=10)
        
        if response.status_code == 200:
            print("✅ API key is working and has quota remaining")
            data = response.json()
            print(f"✅ Test response: 1 USD = {data.get('rate', 'N/A')} EUR")
        elif response.status_code == 403:
            print("❌ API quota exceeded!")
            try:
                error_data = response.json()
                current_usage = error_data.get('QuotaValueCurrentUsage', 'Unknown')
                quota_limit = error_data.get('QuotaValue', 'Unknown')
                print(f"❌ Usage: {current_usage}/{quota_limit} {error_data.get('QuotaValueUnit', '')}")
            except:
                print(f"❌ Response: {response.text}")
        elif response.status_code == 401:
            print("❌ API key is invalid or unauthorized")
        else:
            print(f"❌ API error: {response.status_code}")
            print(f"❌ Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Request failed: {e}")

def main():
    print("🔍 Testing API Key Loading and Quota Status")
    print("=" * 50)
    
    # Test direct environment loading
    env_api_key = test_env_loading()
    
    # Test config loader
    config_api_key = test_config_loader()
    
    # Verify they match
    if env_api_key and config_api_key:
        if env_api_key == config_api_key:
            print("\n✅ API keys match between ENV and ConfigLoader")
        else:
            print("\n❌ API key MISMATCH between ENV and ConfigLoader!")
            print(f"ENV: {env_api_key[:8]}...")
            print(f"Config: {config_api_key[:8]}...")
    
    # Test quota status
    test_api_key = config_api_key or env_api_key
    test_api_quota(test_api_key)
    
    print("\n" + "=" * 50)
    print("🏁 Test completed")

if __name__ == "__main__":
    main()