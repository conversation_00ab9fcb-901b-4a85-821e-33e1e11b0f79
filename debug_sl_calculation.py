#!/usr/bin/env python3

import pandas as pd
import numpy as np
import sys
import os
sys.path.append('.')

import pandas_ta as ta
import json

def debug_sl_calculation():
    # Load the exact data around the problematic trade
    # Entry: 2025-07-02 01:17:42, Exit: 2025-07-02 01:17:43
    
    parquet_file = "/Users/<USER>/Projects/scalpel_new/parquet_processed/XRPUSDC/1s/2025-07-02.parquet"
    df = pd.read_parquet(parquet_file)
    df['datetime'] = pd.to_datetime(df['timestamp'], unit='s')
    
    # Focus around the problematic trade time
    target_time = pd.Timestamp('2025-07-02 01:17:42', tz='UTC')
    mask = (df['datetime'] >= target_time - pd.Timedelta(minutes=5)) & \
           (df['datetime'] <= target_time + pd.Timedelta(minutes=5))
    
    trade_df = df[mask].copy()
    trade_df = trade_df.reset_index(drop=True)
    
    print("=== DEBUG: SL Calculation for SHORT Position ===")
    print(f"Data around {target_time}:")
    print(trade_df[['datetime', 'open', 'high', 'low', 'close']].head(10))
    
    # Load strategy config
    with open('temp_config_1s.json', 'r') as f:
        config = json.load(f)
    
    # Extract SL configuration parameters
    sl_dist_perc = config.get('sl_dist_perc', 0.01)
    min_sl_dist_atr_mult = config.get('min_sl_dist_atr_mult', 1.0)
    
    print(f"\n=== SL Configuration ===")
    print(f"sl_dist_perc: {sl_dist_perc}")
    print(f"min_sl_dist_atr_mult: {min_sl_dist_atr_mult}")
    
    # Find the exact entry row
    entry_row = trade_df[trade_df['datetime'] == target_time]
    if entry_row.empty:
        print(f"ERROR: No data found for {target_time}")
        return
        
    entry_idx = entry_row.index[0]
    entry_price = entry_row.iloc[0]['close']  # Assuming entry at close price
    
    print(f"\n=== Entry Details ===")
    print(f"Entry time: {target_time}")
    print(f"Entry price: {entry_price}")
    print(f"Entry row index: {entry_idx}")
    
    # Calculate ATR for SL distance
    # We need enough historical data for ATR calculation
    atr_period = 14
    if len(trade_df) < atr_period + entry_idx:
        print("ERROR: Not enough data for ATR calculation")
        return
        
    # Calculate ATR using same method as in simulation
    # Use pandas_ta to calculate ATR up to the entry point
    atr_data = trade_df.iloc[:entry_idx+1].copy()
    atr_series = ta.atr(atr_data['high'], atr_data['low'], atr_data['close'], length=atr_period, min_periods=atr_period)
    current_atr = atr_series.iloc[-1] if not atr_series.isna().iloc[-1] else 0.0
    
    print(f"\n=== ATR Calculation ===")
    print(f"ATR period: {atr_period}")
    print(f"Current ATR: {current_atr}")
    
    # Calculate SL distances exactly as in simulate_trading_new.py
    sl_dist_perc_points = entry_price * sl_dist_perc
    sl_dist_atr_points = current_atr * min_sl_dist_atr_mult if current_atr > 0 else 0
    min_sl_dist_points = max(sl_dist_perc_points, sl_dist_atr_points)
    
    print(f"\n=== SL Distance Calculation ===")
    print(f"sl_dist_perc_points = {entry_price} * {sl_dist_perc} = {sl_dist_perc_points:.5f}")
    print(f"sl_dist_atr_points = {current_atr} * {min_sl_dist_atr_mult} = {sl_dist_atr_points:.5f}")
    print(f"min_sl_dist_points = max({sl_dist_perc_points:.5f}, {sl_dist_atr_points:.5f}) = {min_sl_dist_points:.5f}")
    
    if min_sl_dist_points <= 1e-9:
        fallback = entry_price * 0.005
        print(f"FALLBACK: min_sl_dist_points too small, using {fallback:.5f}")
        min_sl_dist_points = fallback
    
    # Calculate SL for SHORT position (triggered_pos = -1)
    triggered_pos = -1  # SHORT position
    
    # From the code: sl_price = sim_entry_price - min_sl_dist_points if triggered_pos == 1 else sim_entry_price + min_sl_dist_points
    if triggered_pos == 1:
        sl_price = entry_price - min_sl_dist_points  # LONG
    else:
        sl_price = entry_price + min_sl_dist_points  # SHORT
    
    print(f"\n=== Final SL Calculation ===")
    print(f"Position type: {'LONG' if triggered_pos == 1 else 'SHORT'}")
    print(f"triggered_pos: {triggered_pos}")
    print(f"Entry price: {entry_price:.5f}")
    print(f"min_sl_dist_points: {min_sl_dist_points:.5f}")
    
    if triggered_pos == 1:
        print(f"SL calculation for LONG: {entry_price:.5f} - {min_sl_dist_points:.5f} = {sl_price:.5f}")
    else:
        print(f"SL calculation for SHORT: {entry_price:.5f} + {min_sl_dist_points:.5f} = {sl_price:.5f}")
    
    print(f"Calculated SL price: {sl_price:.5f}")
    
    # Compare with the problematic values we know
    known_entry = 2.16599
    known_sl_trigger = 2.15666
    
    print(f"\n=== Comparison with Known Values ===")
    print(f"Our calculated entry: {entry_price:.5f}")
    print(f"Known entry from debug: {known_entry:.5f}")
    print(f"Difference: {abs(entry_price - known_entry):.5f}")
    
    print(f"Our calculated SL: {sl_price:.5f}")
    print(f"Known SL trigger: {known_sl_trigger:.5f}")
    print(f"Difference: {abs(sl_price - known_sl_trigger):.5f}")
    
    # Check if SL is correctly placed
    if triggered_pos == -1:  # SHORT
        if sl_price > entry_price:
            print(f"✅ SL correctly placed ABOVE entry for SHORT position")
        else:
            print(f"❌ SL incorrectly placed BELOW entry for SHORT position")
            print(f"   SL should be > {entry_price:.5f}, but got {sl_price:.5f}")
    
    # Check what might cause the discrepancy
    print(f"\n=== Debugging Discrepancy ===")
    if abs(sl_price - known_sl_trigger) > 0.01:
        print("LARGE DISCREPANCY DETECTED!")
        print("Possible causes:")
        print("1. Different entry price calculation")
        print("2. Different ATR calculation")
        print("3. Different SL distance parameters")
        print("4. Bug in SL placement logic")
        
    # Let's also check if there's a sign error somewhere
    manual_sl_above = entry_price + min_sl_dist_points
    manual_sl_below = entry_price - min_sl_dist_points
    
    print(f"\nManual calculations:")
    print(f"SL above entry: {entry_price:.5f} + {min_sl_dist_points:.5f} = {manual_sl_above:.5f}")
    print(f"SL below entry: {entry_price:.5f} - {min_sl_dist_points:.5f} = {manual_sl_below:.5f}")
    print(f"Known SL trigger: {known_sl_trigger:.5f}")
    
    if abs(manual_sl_below - known_sl_trigger) < 0.001:
        print("🚨 FOUND THE BUG! The SL is being calculated as entry - distance instead of entry + distance for SHORT!")

if __name__ == "__main__":
    debug_sl_calculation()