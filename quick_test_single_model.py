#!/usr/bin/env python3
"""
R<PERSON><PERSON>ly test jedného modelu s rôznymi threshold hodnotami.
"""

import subprocess
import json
from pathlib import Path

def test_single_model(model_name: str, start_date: str, end_date: str):
    """Test jedného modelu s rôznymi threshold hodnotami."""
    
    base_config = "strategyConfig_scalp_1s_debug.json"
    thresholds = [0.005, 0.01, 0.05, 0.1, 0.5]
    
    print(f"🔍 Testovanie modelu: {model_name}")
    print(f"📅 Obdobie: {start_date} - {end_date}")
    
    for threshold in thresholds:
        print(f"\n🎯 Testovanie threshold: {threshold}")
        
        # Načítame a upravíme konfiguráciu
        with open(base_config, 'r') as f:
            config = json.load(f)
        
        config["trainingSettings"]["modelSavePath"] = model_name
        config["tradeParams"]["entryActionThreshold"] = threshold
        config["tradeParams"]["exitActionThreshold"] = threshold
        config["tradeParams"]["longEntryThreshold"] = threshold
        config["tradeParams"]["shortEntryThreshold"] = threshold
        
        # Uložíme dočasnú konfiguráciu
        temp_config = f"temp_config_{threshold}.json"
        with open(temp_config, 'w') as f:
            json.dump(config, f, indent=2)
        
        # Spustíme test
        cmd = [
            "python", "simulate_trading.py",
            "--cfg", temp_config,
            "--start", start_date,
            "--end", end_date,
            "--use-1s-decisions",
            "--log-level", "ERROR"  # Minimálny logging
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
            
            if result.returncode == 0:
                # Skúsime extrahovať základné výsledky z výstupu
                lines = result.stdout.split('\n')
                for line in lines:
                    if "Celkový PnL" in line or "Total Return" in line or "Win Rate" in line:
                        print(f"   {line.strip()}")
                print(f"   ✅ Úspešne dokončené")
            else:
                print(f"   ❌ Zlyhalo")
                
        except subprocess.TimeoutExpired:
            print(f"   ⏰ Timeout")
        except Exception as e:
            print(f"   💥 Chyba: {e}")
        
        # Vyčistíme dočasnú konfiguráciu
        try:
            Path(temp_config).unlink()
        except:
            pass

if __name__ == "__main__":
    # Test s jedným modelom
    test_single_model("sac_1999360", "2025-01-01", "2025-01-02")