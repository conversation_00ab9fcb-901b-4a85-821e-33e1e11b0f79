#!/usr/bin/env python3
"""
Test live trading features loading
"""
import json
import sys
from pathlib import Path

def test_live_trading_features():
    """Test ako live trading načítava features"""
    print("=== TESTING LIVE TRADING FEATURES ===")
    
    # Load config exactly like live_trading.py
    try:
        import live_trading
        config_path = Path("strategyConfig_scalp_1s.json")
        cfg = live_trading.load_config(config_path)
        print(f"✅ Config loaded: {type(cfg)}")
        
    except Exception as e:
        print(f"❌ Error loading config: {e}")
        return False
    
    # Simulate the exact code from live_trading.py
    print("\n--- Simulating live_trading.py feature loading ---")
    
    # This is the exact code from live_trading.py line 851
    EXPECTED_FEATURE_COLS = list(cfg["envSettings"]["feature_columns"])  # Deep copy
    feature_cols = EXPECTED_FEATURE_COLS.copy()  # Working copy
    
    print(f"EXPECTED_FEATURE_COLS: {len(EXPECTED_FEATURE_COLS)} features")
    print(f"feature_cols: {len(feature_cols)} features")
    print(f"First 5: {EXPECTED_FEATURE_COLS[:5]}")
    print(f"Last 5: {EXPECTED_FEATURE_COLS[-5:]}")
    
    # Check if EXPECTED_FEATURE_COLS is being corrupted
    if len(EXPECTED_FEATURE_COLS) != 48:
        print(f"❌ CRITICAL: EXPECTED_FEATURE_COLS is corrupted! Length: {len(EXPECTED_FEATURE_COLS)}")
        print(f"❌ This should be 48! Something is wrong with the config or reference.")
        print(f"❌ First 5: {EXPECTED_FEATURE_COLS[:5]}")
        print(f"❌ Last 5: {EXPECTED_FEATURE_COLS[-5:]}")
        return False
    else:
        print(f"✅ EXPECTED_FEATURE_COLS is correct: {len(EXPECTED_FEATURE_COLS)} features")
    
    # Test dimension calculation
    lookback = cfg["envSettings"].get("state_lookback", 30)
    expected_obs_size = len(EXPECTED_FEATURE_COLS) * lookback + 11
    feats_needed = (expected_obs_size - 11) // lookback
    
    print(f"\nDimension calculation:")
    print(f"  Lookback: {lookback}")
    print(f"  Expected obs size: {expected_obs_size}")
    print(f"  Features needed: {feats_needed}")
    
    # Test the dimension check logic
    if len(EXPECTED_FEATURE_COLS) != feats_needed:
        print(f"⚠️ FEATURE DIMENSION MISMATCH:")
        print(f"   Model expects {feats_needed} features but config has {len(EXPECTED_FEATURE_COLS)}")
        return False
    else:
        print(f"✅ No dimension mismatch: {len(EXPECTED_FEATURE_COLS)} == {feats_needed}")
        return True

def test_indicators_impact():
    """Test či indicators.py ovplyvňuje features"""
    print("\n=== TESTING INDICATORS IMPACT ===")
    
    try:
        from indicators import calculate_and_merge_indicators
        import pandas as pd
        import numpy as np
        
        # Load config
        import live_trading
        config_path = Path("strategyConfig_scalp_1s.json")
        cfg = live_trading.load_config(config_path)
        
        print(f"Config features before indicators: {len(cfg['envSettings']['feature_columns'])}")
        
        # Create dummy data
        timestamps = pd.date_range(start='2025-01-01', periods=100, freq='5min', tz='UTC')
        dummy_data = pd.DataFrame({
            'open': np.random.uniform(1.0, 2.0, 100),
            'high': np.random.uniform(1.1, 2.1, 100),
            'low': np.random.uniform(0.9, 1.9, 100),
            'close': np.random.uniform(1.0, 2.0, 100),
            'volume': np.random.uniform(1000, 10000, 100),
        }, index=timestamps)
        
        data_dict = {'5m': dummy_data}
        
        # Call calculate_and_merge_indicators
        result_df, calculated_features = calculate_and_merge_indicators(
            data_dict, cfg,
            skip_hmm=True,
            hmm_model_external=None,
            hmm_scaler_external=None
        )
        
        print(f"Config features after indicators: {len(cfg['envSettings']['feature_columns'])}")
        print(f"Calculated features: {len(calculated_features)}")
        print(f"Result DataFrame columns: {len(result_df.columns)}")
        
        # Check if config was modified
        if len(cfg['envSettings']['feature_columns']) != 48:
            print(f"❌ Config was modified by indicators! Now has {len(cfg['envSettings']['feature_columns'])} features")
            return False
        else:
            print(f"✅ Config was not modified by indicators")
            return True
            
    except Exception as e:
        print(f"❌ Error testing indicators: {e}")
        return False

def main():
    """Main test function"""
    print("🔍 TESTING LIVE TRADING FEATURES LOADING")
    print("=" * 60)
    
    # Test feature loading
    features_ok = test_live_trading_features()
    
    # Test indicators impact
    indicators_ok = test_indicators_impact()
    
    print("=" * 60)
    print("🏁 TEST RESULTS")
    print("=" * 60)
    
    print(f"Feature loading: {'✅ OK' if features_ok else '❌ FAIL'}")
    print(f"Indicators impact: {'✅ OK' if indicators_ok else '❌ FAIL'}")
    
    if features_ok and indicators_ok:
        print("\n✅ ALL TESTS PASSED!")
        print("The problem must be in the live trading execution flow.")
        print("Check for:")
        print("  1. Code not being updated")
        print("  2. Different execution path")
        print("  3. Runtime modifications")
    else:
        print("\n❌ PROBLEM FOUND!")
        print("Fix the identified issues.")
    
    return features_ok and indicators_ok

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
