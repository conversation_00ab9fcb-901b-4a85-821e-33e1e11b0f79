#!/usr/bin/env python3

import json
import os
import subprocess
import sys
import csv
from datetime import datetime

def main():
    print("🔄 POKRAČOVANIE BATCH COMPARISON")
    print("=" * 50)
    
    # Modely a thresholds
    models = [
        "sac_1199616_steps.zip",
        "sac_1999360_steps.zip", 
        "sac_2799104_steps.zip",
        "sac_3198976_steps.zip",
        "sac_7197696_steps.zip"
    ]
    
    thresholds = [0.001, 0.002, 0.005, 0.01, 0.02, 0.05, 0.1, 0.2, 0.3, 0.5]
    
    # Identifikovanie dokončených testov
    existing_dir = "batch_comparison_20250628_101829"
    if not os.path.exists(existing_dir):
        print(f"❌ Neexistuje adresár: {existing_dir}")
        return
        
    completed_configs = set()
    for filename in os.listdir(existing_dir):
        if filename.startswith("test_config_") and filename.endswith(".json"):
            completed_configs.add(filename)
    
    print(f"✅ Nájdených {len(completed_configs)} dokončen<PERSON>ch testov")
    
    # Identifikovanie chýbajúcich testov
    remaining_tests = []
    for model in models:
        model_name = model.replace("_steps.zip", "").replace(".zip", "")
        for threshold in thresholds:
            config_name = f"test_config_{model_name}_steps_thresh_{threshold}.json"
            if config_name not in completed_configs:
                remaining_tests.append({
                    'model': model,
                    'threshold': threshold,
                    'config_name': config_name
                })
    
    print(f"🔄 Zostáva otestovať: {len(remaining_tests)} kombinácií")
    
    if len(remaining_tests) == 0:
        print("✅ Všetky testy už sú dokončené!")
        return
    
    # Zobrazenie chýbajúcich testov
    print("\n📝 CHÝBAJÚCE TESTY:")
    for i, test in enumerate(remaining_tests, 1):
        model_name = test['model'].replace("_steps.zip", "").replace(".zip", "")
        print(f"   {i:2d}. {model_name} + threshold {test['threshold']}")
    
    # Potvrdenie od používateľa
    response = input(f"\n🚀 Pokračovať s {len(remaining_tests)} testami? (y/n): ").lower()
    if response != 'y':
        print("❌ Prerušené používateľom")
        return
    
    # Načítanie base config
    base_config_path = "strategyConfig_scalp_1s_improved.json"
    if not os.path.exists(base_config_path):
        print(f"❌ Neexistuje base config: {base_config_path}")
        return
        
    with open(base_config_path, 'r') as f:
        base_config = json.load(f)
    
    # Nastavenie dátumov na 3-mesačné obdobie
    base_config["backtestSettings"]["startDate"] = "2025-01-01T00:00:00.000Z"
    base_config["backtestSettings"]["endDate"] = "2025-03-31T23:59:59.000Z"
    
    # Vytvorenie output adresára
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = f"continuation_{timestamp}"
    os.makedirs(output_dir, exist_ok=True)
    
    print(f"📁 Výstupný adresár: {output_dir}")
    
    # Spustenie zostávajúcich testov
    results = []
    
    for i, test in enumerate(remaining_tests, 1):
        model = test['model']
        threshold = test['threshold']
        model_name = model.replace("_steps.zip", "").replace(".zip", "")
        
        print(f"\n📈 Progress: {i}/{len(remaining_tests)}")
        print(f"🚀 Spúšťam backtest: {model_name} s threshold {threshold}")
        
        # Overenie existencie modela
        if not os.path.exists(model):
            print(f"❌ Model neexistuje: {model}")
            continue
            
        vecnorm_file = model.replace("_steps.zip", ".vecnorm.pkl").replace(".zip", ".vecnorm.pkl")
        if not os.path.exists(vecnorm_file):
            print(f"❌ Vecnorm súbor neexistuje: {vecnorm_file}")
            continue
        
        # Úprava konfigurácie
        config = base_config.copy()
        config["trainingSettings"]["modelSavePath"] = model.replace(".zip", "")
        config["rlSettings"]["entryActionThreshold"] = threshold
        config["rlSettings"]["exitActionThreshold"] = threshold  
        config["rlSettings"]["longEntryThreshold"] = threshold
        config["rlSettings"]["shortEntryThreshold"] = threshold
        
        # Uloženie config súboru
        config_filename = f"config_{model_name}_thresh_{threshold}.json"
        config_path = os.path.join(output_dir, config_filename)
        
        with open(config_path, 'w') as f:
            json.dump(config, f, indent=2)
        
        # Spustenie simulácie
        try:
            result = subprocess.run([
                sys.executable, "simulate_trading.py", config_path
            ], capture_output=True, text=True, timeout=1800)  # 30 min timeout
            
            if result.returncode == 0:
                print(f"✅ Backtest dokončený: {model_name} thresh={threshold}")
                
                # Načítanie výsledkov z CSV súborov
                equity_file = f"equity_{model_name}_thresh_{threshold}.csv"
                trades_file = f"trades_{model_name}_thresh_{threshold}.csv"
                
                if os.path.exists(equity_file) and os.path.exists(trades_file):
                    # Spustenie analýzy
                    analysis_result = subprocess.run([
                        sys.executable, "analyze_simulation_results.py", trades_file, equity_file
                    ], capture_output=True, text=True)
                    
                    # Načítanie equity pre výpočet return
                    try:
                        with open(equity_file, 'r') as f:
                            reader = csv.DictReader(f)
                            equity_data = list(reader)
                            if equity_data:
                                initial_equity = float(equity_data[0]['equity'])
                                final_equity = float(equity_data[-1]['equity'])
                                total_return = ((final_equity - initial_equity) / initial_equity) * 100
                                
                                results.append({
                                    'model': model_name,
                                    'threshold': threshold,
                                    'initial_equity': initial_equity,
                                    'final_equity': final_equity,
                                    'total_return_pct': total_return,
                                    'status': 'Success'
                                })
                    except Exception as e:
                        print(f"⚠️  Problém s analýzou equity: {e}")
                        
            else:
                print(f"❌ Backtest zlyhal: {result.stderr}")
                results.append({
                    'model': model_name,
                    'threshold': threshold,
                    'status': 'Failed',
                    'error': result.stderr[:200]
                })
                
        except subprocess.TimeoutExpired:
            print(f"⏰ Timeout pre {model_name} thresh={threshold}")
            results.append({
                'model': model_name,
                'threshold': threshold,
                'status': 'Timeout'
            })
        except Exception as e:
            print(f"❌ Chyba: {e}")
            results.append({
                'model': model_name,
                'threshold': threshold,
                'status': 'Error',
                'error': str(e)
            })
    
    # Uloženie výsledkov
    results_file = os.path.join(output_dir, "continuation_results.csv")
    if results:
        with open(results_file, 'w', newline='') as f:
            writer = csv.DictWriter(f, fieldnames=results[0].keys())
            writer.writeheader()
            writer.writerows(results)
        
        print(f"\n📊 Výsledky uložené do: {results_file}")
        
        # Zobrazenie top výsledkov
        successful_results = [r for r in results if r.get('status') == 'Success' and 'total_return_pct' in r]
        if successful_results:
            successful_results.sort(key=lambda x: x['total_return_pct'], reverse=True)
            
            print(f"\n🏆 TOP 3 NOVÉ VÝSLEDKY:")
            for i, result in enumerate(successful_results[:3], 1):
                print(f"   {i}. {result['model']} | Thresh: {result['threshold']} | Return: {result['total_return_pct']:.2f}%")
    
    print(f"\n✅ Pokračovanie dokončené!")
    print(f"📁 Všetky výsledky v adresári: {output_dir}")

if __name__ == "__main__":
    main()