#!/usr/bin/env python3
"""
Jednoduchý script na kontrolu parquet súborov pomocou pyarrow.
Obchádza problém s pandas/numpy konverziou.
"""

import pyarrow.parquet as pq
import sys
from pathlib import Path

def inspect_parquet(file_path):
    """Skontroluje parquet súbor pomocou pyarrow."""
    try:
        print(f"📁 Kontrolujem súbor: {file_path}")
        
        # Načítaj len metadata
        parquet_file = pq.ParquetFile(file_path)
        schema = parquet_file.schema
        metadata = parquet_file.metadata
        
        print(f"✅ Súbor sa podarilo otvoriť")
        print(f"📊 Počet riadkov: {metadata.num_rows}")
        print(f"📋 Počet stĺpcov: {len(schema)}")
        print()
        
        # Získaj názvy stĺpcov
        columns = [field.name for field in schema]
        
        print('📋 Všetky stĺpce:')
        for i, col in enumerate(columns):
            print(f'  {i+1:2d}. {col}')
        
        # Kontrola kľúčových indikátorov
        key_indicators = ['ADX_14', 'DMP_14', 'DMN_14', 'RSI_14', 'ATR_14', 'EMA_9', 'EMA_21']
        print()
        print('🔍 Kľúčové indikátory:')
        missing_indicators = []
        present_indicators = []
        
        for indicator in key_indicators:
            if indicator in columns:
                print(f'  ✅ {indicator}: PRÍTOMNÝ')
                present_indicators.append(indicator)
            else:
                print(f'  ❌ {indicator}: CHÝBA')
                missing_indicators.append(indicator)
        
        print()
        print(f"📈 Súhrn:")
        print(f"  ✅ Prítomné indikátory: {len(present_indicators)}/{len(key_indicators)}")
        print(f"  ❌ Chýbajúce indikátory: {len(missing_indicators)}/{len(key_indicators)}")
        
        if missing_indicators:
            print(f"  🔴 Chýbajú: {', '.join(missing_indicators)}")
        
        return len(missing_indicators) == 0
        
    except Exception as e:
        print(f"❌ Chyba pri kontrole súboru: {e}")
        return False

def main():
    if len(sys.argv) > 1:
        file_path = sys.argv[1]
    else:
        file_path = "parquet_processed/XRPUSDC/5m/2025-04-10.parquet"
    
    if not Path(file_path).exists():
        print(f"❌ Súbor neexistuje: {file_path}")
        return
    
    success = inspect_parquet(file_path)
    
    if success:
        print("\n🎉 Všetky kľúčové indikátory sú prítomné!")
    else:
        print("\n⚠️  Niektoré indikátory chýbajú - potrebné prepočítanie.")

if __name__ == "__main__":
    main()
