{"symbol": "XRPUSDC", "primaryTimeframe": "5m", "featureParquetDir": "parquet_processed", "dataProvider": {"symbol": "XRPUSDC"}, "coinapi": {"apiKey": "655db671-d87b-4cce-8b6b-1bdcb2a573c9"}, "binance": {"apiKey": "sA3ZJIKaJqbN3DPyLhQNdKpAHwWqFNtTwNjAu3yp2XX2wiztFJXGYJXU4FCk9Brv", "apiSecret": "f46VmE6wmo6tIqY7hHukHl8RqWUNYKaFNsh4ntUeytWwkFosozL6yUuLU1OD8vqt"}, "indicatorSettings": {"stateLookback": 30, "orderFlowFeatures": {"enabled": true, "sourceTf": "trades", "volumeDeltaPeriods": ["30s", "2m"], "tradeCountDeltaPeriods": ["100t", "500t"], "cvdEnabled": true, "cvdResetFrequency": "daily"}, "hmm_5m": {"enabled": true, "tf": "5m", "n_components": 3, "window": 200, "data_source": "volatility"}, "atr_5m": {"enabled": true, "tf": "5m", "basePeriod": 14}, "vwap_5m": {"enabled": true, "tf": "5m"}, "volume": {"enabled": true}, "volumeImbalance_5m": {"enabled": true, "tf": "5m"}, "bollinger_5m": {"enabled": true, "tf": "5m", "basePeriod": 20, "baseStDev": 2.0}, "adx": {"enabled": true, "tf": "5m", "basePeriod": 14}, "rsi_5m": {"enabled": true, "tf": "5m", "basePeriod": 14}, "ema_5m": {"enabled": true, "tf": "5m", "baseFastPeriod": 9, "baseSlowPeriod": 21}}, "riskManagement": {"positionSizingMethod": "RiskPercentage", "riskPerTradePercentage": 2.0, "maxPositionSizePercentEquity": 10.0, "maxDailyRiskPercent": 10.0, "dailyLossLimit": 5.0}, "debug": false, "tradeParams": {"slippagePercentage": 0.03, "feePercentage": 0.05, "entryActionThreshold": 0.5, "exitActionThreshold": 0.5, "longEntryThreshold": 0.2, "shortEntryThreshold": 0.7, "minAtrPercentOfPrice": 0.6, "minSLDistanceATR": 2.5, "minSLDistancePercent": 2, "rrTarget": 2.5, "minTradeValue": 0.02, "minTradeDurationSeconds": 60, "_comment": "Note: Using fixed R:R base of 2.5 in code instead of rrTarget"}, "account": {"initialEquity": 100}, "trailingStopLoss": {"enabled": true, "activateATRMultiplier": 1.0, "trailATRMultiplier": 1.0}, "trainingSettings": {"totalTimesteps": ********, "logDir": "./tensorboard_logs/", "modelSavePath": "./sac_1999360_steps", "n_envs": 256, "_learningRateNote": "Linear decay scheduler 3e-4 -> 5e-5 is hardcoded in agent.py", "bufferSize": 2000000, "learningStarts": 200000, "batchSize": 1024, "evalFreq": 400000, "evalEpisodes": 5, "seed": 42, "trainSplitRatio": 0.9, "netArch": [512, 256], "featureExtractor": "cnn", "featureExtractorKwargs": {"lookback": 30, "n_filters": 128, "kernel": 3, "out_dim": 256}, "sacParams": {"tau": 0.005, "gamma": 0.99, "gradient_steps": 64, "train_freq": [16, "step"], "ent_coef": "auto_0.3", "target_entropy": -2.4, "target_update_interval": 2, "max_grad_norm": 1.0}, "rewardStructure": {"pnlScale": 0.8, "tpBonusBase": 0.0, "slPenalty": -2.5, "bigLossPenalty": -18.0, "holdPenaltyPerStep": -0.0008, "profitableHoldBonus": 0.25, "inactivityPenalty": -0.0005, "underwaterPenalty": -0.0005, "exitBonusMultiplier": 0.2, "tradeCostPenalty": 0.2, "minTradeThreshold": 0.02, "microTradePenalty": -1.0, "episodePenaltyDrawdown": -5.0, "drawdownThresholdPercent": 3.0}}, "backtestSettings": {"startTime": "2025-01-04T00:00:00Z", "endTime": "2025-04-24T23:59:59Z"}, "envSettings": {"state_lookback": 30, "max_ep_len": 1000, "inactivity_limit": 450, "feature_columns": ["open", "high", "low", "close", "volume", "buy_volume", "sell_volume", "trade_count", "vwap", "ATR_14", "RSI_14", "EMA_9", "EMA_21", "ADX_14", "DMP_14", "DMN_14", "volume_delta_30s", "volume_delta_2m", "trade_count_delta_100t", "trade_count_delta_500t", "cvd_reset_daily", "hmm_state_3c_volatility_5m", "VWAP_pta", "bollinger_bands_upper_20_2.0", "bollinger_bands_middle_20_2.0", "bollinger_bands_lower_20_2.0", "bollinger_bands_width_20_2.0", "spread", "mid_price", "tob_imbalance", "depth_imbalance5", "depth_slope5", "ob_bid_vol_l1", "ob_ask_vol_l1", "ob_bid_vol_l2", "ob_ask_vol_l2", "ob_bid_vol_l3", "ob_ask_vol_l3", "ob_bid_vol_l4", "ob_ask_vol_l4", "ob_bid_vol_l5", "ob_ask_vol_l5", "dvol_bid_l1", "dvol_ask_l1", "trade_dir_sum_1s", "trade_skew_1s", "dt_since_buy", "dt_since_sell"]}}