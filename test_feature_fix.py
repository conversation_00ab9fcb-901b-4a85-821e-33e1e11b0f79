#!/usr/bin/env python3
"""
Test opravy feature dimension mismatch
"""
import json
import logging

logging.basicConfig(level=logging.INFO)
log = logging.getLogger("FeatureFix")

def test_feature_fix():
    """Test že oprava funguje správne"""
    log.info("=== TESTING FEATURE FIX ===")
    
    # Load config
    with open('strategyConfig_scalp_1s.json', 'r') as f:
        config = json.load(f)
    
    feature_cols = config['envSettings']['feature_columns']
    lookback = config['envSettings'].get('state_lookback', 30)
    
    log.info(f"Config features: {len(feature_cols)}")
    log.info(f"Lookback: {lookback}")
    
    # Calculate expected observation space
    expected_obs_size = len(feature_cols) * lookback + 11
    log.info(f"Expected obs size: {expected_obs_size}")
    
    # Calculate features needed by model (from observation space)
    feats_needed = (expected_obs_size - 11) // lookback
    log.info(f"Features needed by model: {feats_needed}")
    
    # Test the fix logic
    if len(feature_cols) != feats_needed:
        log.warning(f"⚠️ FEATURE DIMENSION MISMATCH:")
        log.warning(f"   Model expects {feats_needed} features but config has {len(feature_cols)}")
        
        if len(feature_cols) > feats_needed:
            # Too many features - truncate to match model expectations
            log.warning(f"   Truncating {len(feature_cols) - feats_needed} features to match model")
            selected_features = feature_cols[:feats_needed]
            log.info(f"   Using first {feats_needed} features: {selected_features[:3]}...{selected_features[-3:]}")
        else:
            # Too few features - pad with zeros
            log.warning(f"   Padding with {feats_needed - len(feature_cols)} zero features")
            selected_features = feature_cols.copy()
            for i in range(feats_needed - len(feature_cols)):
                selected_features.append(f"pad_feature_{i}")
            log.info(f"   Padded to {len(selected_features)} features")
    else:
        selected_features = feature_cols
        log.info(f"   ✅ Feature count matches model expectations")
    
    log.info(f"Final selected features: {len(selected_features)}")
    
    # Verify the fix
    if len(selected_features) == feats_needed:
        log.info("✅ FIX SUCCESSFUL: Feature count matches model expectations")
        return True
    else:
        log.error(f"❌ FIX FAILED: Expected {feats_needed}, got {len(selected_features)}")
        return False

if __name__ == '__main__':
    success = test_feature_fix()
    if success:
        print("\n🎉 Feature fix is working correctly!")
        print("💡 Live trading should now use the correct number of features.")
    else:
        print("\n❌ Feature fix needs more work.")
