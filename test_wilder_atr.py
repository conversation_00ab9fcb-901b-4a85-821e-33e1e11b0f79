#!/usr/bin/env python3
"""
Test script na porovnanie pandas_ta ATR vs <PERSON><PERSON><PERSON> ATR
"""

import pandas as pd
import numpy as np
from indicators import wilder_atr
import pandas_ta as ta

def test_atr_comparison():
    """Porovná pandas_ta ATR s naš<PERSON>u implementáciou"""
    
    # Vytvoríme testovací dataset
    np.random.seed(42)
    dates = pd.date_range('2025-01-01', periods=100, freq='5min')
    close = 2.5 + np.cumsum(np.random.randn(100) * 0.01)
    high = close + np.random.rand(100) * 0.02
    low = close - np.random.rand(100) * 0.02

    df = pd.DataFrame({
        'high': high,
        'low': low, 
        'close': close
    }, index=dates)

    print("=== Test Wilderovho ATR ===")
    print(f"Dataset: {len(df)} riadkov")
    print(f"Cena range: {close.min():.4f} - {close.max():.4f}")
    
    # Porovnanie pandas_ta vs <PERSON><PERSON><PERSON> ATR
    atr_pandas_ta = ta.atr(df['high'], df['low'], df['close'], length=14)
    atr_wilder = wilder_atr(df['high'], df['low'], df['close'], length=14)

    print('\n=== Porovnanie ATR implementácií ===')
    print('pandas_ta ATR (posledných 10):')
    print(atr_pandas_ta.tail(10).round(6))
    print('\nWilder ATR (posledných 10):')
    print(atr_wilder.tail(10).round(6))
    print('\nRozdiel (posledných 10):')
    print((atr_wilder - atr_pandas_ta).tail(10).round(6))

    # Percentuálny rozdiel
    valid_mask = (atr_pandas_ta.notna()) & (atr_wilder.notna()) & (atr_pandas_ta != 0)
    pct_diff = ((atr_wilder - atr_pandas_ta) / atr_pandas_ta * 100)[valid_mask]
    
    if len(pct_diff) > 0:
        print(f'\nPriemerný percentuálny rozdiel: {pct_diff.mean():.3f}%')
        print(f'Max percentuálny rozdiel: {pct_diff.abs().max():.3f}%')
        print(f'Štandardná odchýlka rozdielov: {pct_diff.std():.3f}%')
    
    # Test s reálnymi XRPUSDC hodnotami
    print('\n=== Test s reálnymi XRPUSDC hodnotami ===')
    real_close = np.array([2.4567, 2.4589, 2.4612, 2.4598, 2.4634, 2.4656, 2.4623, 2.4645, 2.4678, 2.4692,
                          2.4715, 2.4698, 2.4721, 2.4743, 2.4729, 2.4752, 2.4768, 2.4785, 2.4771, 2.4794])
    real_high = real_close + np.random.rand(20) * 0.005
    real_low = real_close - np.random.rand(20) * 0.005
    
    real_df = pd.DataFrame({
        'high': real_high,
        'low': real_low,
        'close': real_close
    })
    
    atr_ta_real = ta.atr(real_df['high'], real_df['low'], real_df['close'], length=14)
    atr_wilder_real = wilder_atr(real_df['high'], real_df['low'], real_df['close'], length=14)
    
    print(f'pandas_ta ATR (posledná hodnota): {atr_ta_real.iloc[-1]:.6f}')
    print(f'Wilder ATR (posledná hodnota): {atr_wilder_real.iloc[-1]:.6f}')
    print(f'Rozdiel: {(atr_wilder_real.iloc[-1] - atr_ta_real.iloc[-1]):.6f}')
    
    # Percentuálna hodnota z ceny
    last_price = real_close[-1]
    atr_pct_ta = (atr_ta_real.iloc[-1] / last_price) * 100
    atr_pct_wilder = (atr_wilder_real.iloc[-1] / last_price) * 100
    
    print(f'\nATR ako % z ceny:')
    print(f'pandas_ta: {atr_pct_ta:.3f}%')
    print(f'Wilder: {atr_pct_wilder:.3f}%')
    
    return atr_wilder_real, atr_ta_real

if __name__ == "__main__":
    test_atr_comparison()
