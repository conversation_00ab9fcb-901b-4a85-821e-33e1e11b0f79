#!/usr/bin/env python3
"""
Test script pre overenie trading_allowed logiky
"""

def test_trading_logic():
    """Test trading_allowed logiky"""
    
    print("🧪 Testovanie trading_allowed logiky...")
    
    # Simulácia scenárov
    scenarios = [
        {
            "name": "Normálny stav",
            "daily_limits_allowed": True,
            "atr_trading_allowed": True,
            "expected_final": True
        },
        {
            "name": "Daily limits hit",
            "daily_limits_allowed": False,
            "atr_trading_allowed": True,
            "expected_final": False
        },
        {
            "name": "ATR filter blocked",
            "daily_limits_allowed": True,
            "atr_trading_allowed": False,
            "expected_final": False
        },
        {
            "name": "<PERSON>ba blokované",
            "daily_limits_allowed": False,
            "atr_trading_allowed": False,
            "expected_final": False
        }
    ]
    
    all_passed = True
    
    for scenario in scenarios:
        daily_limits_allowed = scenario["daily_limits_allowed"]
        atr_trading_allowed = scenario["atr_trading_allowed"]
        expected_final = scenario["expected_final"]
        
        # Simulácia logiky z kódu
        final_trading_allowed = daily_limits_allowed and atr_trading_allowed
        
        passed = final_trading_allowed == expected_final
        status = "✅ PASS" if passed else "❌ FAIL"
        
        print(f"   {scenario['name']}: {status}")
        print(f"     Daily limits: {daily_limits_allowed}, ATR filter: {atr_trading_allowed}")
        print(f"     Expected: {expected_final}, Got: {final_trading_allowed}")
        
        if not passed:
            all_passed = False
    
    return all_passed

def test_daily_limits_reset():
    """Test resetovania daily limits"""
    
    print("\n🧪 Testovanie resetovania daily limits...")
    
    # Simulácia daily reset logiky
    scenarios = [
        {
            "name": "Nový deň",
            "last_day": "2025-07-01",
            "current_day": "2025-07-02",
            "trading_allowed_before": False,
            "expected_after_reset": True
        },
        {
            "name": "Rovnaký deň",
            "last_day": "2025-07-01",
            "current_day": "2025-07-01",
            "trading_allowed_before": False,
            "expected_after_reset": False
        }
    ]
    
    all_passed = True
    
    for scenario in scenarios:
        last_day = scenario["last_day"]
        current_day = scenario["current_day"]
        trading_allowed_before = scenario["trading_allowed_before"]
        expected_after_reset = scenario["expected_after_reset"]
        
        # Simulácia reset logiky
        trading_allowed = trading_allowed_before
        
        if last_day != current_day:
            # Reset daily limits
            trading_allowed = True
        
        passed = trading_allowed == expected_after_reset
        status = "✅ PASS" if passed else "❌ FAIL"
        
        print(f"   {scenario['name']}: {status}")
        print(f"     Last day: {last_day}, Current day: {current_day}")
        print(f"     Before: {trading_allowed_before}, After reset: {trading_allowed}")
        print(f"     Expected: {expected_after_reset}")
        
        if not passed:
            all_passed = False
    
    return all_passed

if __name__ == "__main__":
    print("🚀 Spúšťam test trading_allowed logiky...\n")
    
    success1 = test_trading_logic()
    success2 = test_daily_limits_reset()
    
    print(f"\n📊 VÝSLEDKY TESTOV:")
    print(f"   Trading logic test: {'✅ PASS' if success1 else '❌ FAIL'}")
    print(f"   Daily reset test: {'✅ PASS' if success2 else '❌ FAIL'}")
    
    if success1 and success2:
        print("\n🎉 Všetky testy prešli úspešne!")
        print("\n💡 Oprava:")
        print("   - Oddelené daily_limits_allowed od atr_trading_allowed")
        print("   - final_trading_allowed = daily_limits_allowed AND atr_trading_allowed")
        print("   - Daily limits sa resetujú každý deň")
        print("   - ATR filter sa vyhodnocuje na každom kroku")
    else:
        print("\n💥 Niektoré testy zlyhali!")
