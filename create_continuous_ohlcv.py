import os
import sys
import msgpack
import pandas as pd
import numpy as np
from datetime import datetime, timezone, timedelta
from pathlib import Path
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def parse_iso_datetime(t):
    """Parse ISO datetime string to datetime object"""
    if not isinstance(t, str):
        return None
    try:
        if t.endswith('Z'):
            t = t[:-1] + '+00:00'
        if '.' in t:
            parts = t.split('.')
            if len(parts) > 1:
                micros_part = parts[1].split('+')[0].split('-')[0]
                if len(micros_part) > 6:
                    micros_part = micros_part[:6]
                if '+' in parts[1]:
                    timezone_part = parts[1].split('+')[1]
                    t = parts[0] + '.' + micros_part + '+' + timezone_part
                elif '-' in parts[1]:
                    timezone_part = parts[1].split('-')[1]
                    t = parts[0] + '.' + micros_part + '-' + timezone_part
                else:
                    t = parts[0] + '.' + micros_part + '+00:00'
        d = datetime.fromisoformat(t)
        if d.tzinfo is None or d.tzinfo.utcoffset(d) is None:
            d = d.replace(tzinfo=timezone.utc)
        else:
            d = d.astimezone(timezone.utc)
        return d
    except Exception as e:
        logger.warning(f"Failed to parse datetime '{t}': {e}")
        return None

def create_continuous_ohlcv_from_trades(trades, start_date_str):
    """Create continuous 1s OHLCV bars from trade data"""
    logger.info(f"Creating continuous 1s OHLCV from {len(trades)} trades")
    
    # Parse trades
    parsed_trades = []
    for trade in trades:
        dt = parse_iso_datetime(trade.get('time_exchange'))
        if dt is None:
            continue
        price = float(trade.get('price', 0))
        size = float(trade.get('size', 0))
        taker_side = trade.get('taker_side', 'BUY')
        
        parsed_trades.append({
            'timestamp': dt,
            'price': price,
            'size': size,
            'is_buy': taker_side == 'BUY'
        })
    
    if not parsed_trades:
        logger.error("No valid trades found")
        return None
    
    # Convert to DataFrame
    df_trades = pd.DataFrame(parsed_trades)
    df_trades['timestamp'] = pd.to_datetime(df_trades['timestamp'])
    df_trades = df_trades.sort_values('timestamp')
    
    logger.info(f"Parsed {len(df_trades)} valid trades")
    logger.info(f"First trade: {df_trades.iloc[0]['timestamp']}")
    logger.info(f"Last trade: {df_trades.iloc[-1]['timestamp']}")
    
    # Create continuous 1s timeline for the entire day
    start_date = datetime.strptime(start_date_str, '%Y-%m-%d').replace(tzinfo=timezone.utc)
    end_date = start_date + timedelta(days=1)
    
    # Create 1s timeline
    timeline = pd.date_range(start=start_date, end=end_date, freq='1s')[:-1]  # Exclude end to avoid overlap
    logger.info(f"Created timeline with {len(timeline)} 1s intervals")
    
    # Group trades by 1s intervals
    df_trades['timestamp_1s'] = df_trades['timestamp'].dt.floor('1s')
    
    # Aggregate trades by 1s intervals
    ohlcv_data = []
    
    for ts in timeline:
        # Get trades in this 1s interval
        trades_in_interval = df_trades[df_trades['timestamp_1s'] == ts]
        
        if len(trades_in_interval) == 0:
            # No trades in this interval - use previous close or NaN
            ohlcv_data.append({
                'timestamp': ts,
                'open': np.nan,
                'high': np.nan,
                'low': np.nan,
                'close': np.nan,
                'volume': 0.0,
                'buy_volume': 0.0,
                'sell_volume': 0.0,
                'trade_count': 0,
                'vwap': np.nan
            })
        else:
            # Calculate OHLCV for this interval
            prices = trades_in_interval['price'].values
            sizes = trades_in_interval['size'].values
            
            open_price = prices[0]
            high_price = prices.max()
            low_price = prices.min()
            close_price = prices[-1]
            total_volume = sizes.sum()
            
            buy_volume = trades_in_interval[trades_in_interval['is_buy']]['size'].sum()
            sell_volume = trades_in_interval[~trades_in_interval['is_buy']]['size'].sum()
            
            # Calculate VWAP
            vwap = np.sum(prices * sizes) / total_volume if total_volume > 0 else close_price
            
            ohlcv_data.append({
                'timestamp': ts,
                'open': open_price,
                'high': high_price,
                'low': low_price,
                'close': close_price,
                'volume': total_volume,
                'buy_volume': buy_volume,
                'sell_volume': sell_volume,
                'trade_count': len(trades_in_interval),
                'vwap': vwap
            })
    
    # Convert to DataFrame
    df_ohlcv = pd.DataFrame(ohlcv_data)
    
    # Forward-fill missing OHLC values
    df_ohlcv[['open', 'high', 'low', 'close', 'vwap']] = df_ohlcv[['open', 'high', 'low', 'close', 'vwap']].fillna(method='ffill')
    
    # For the very first values, use the first available price
    first_valid_price = df_trades.iloc[0]['price']
    df_ohlcv[['open', 'high', 'low', 'close', 'vwap']] = df_ohlcv[['open', 'high', 'low', 'close', 'vwap']].fillna(first_valid_price)
    
    logger.info(f"Created continuous OHLCV with {len(df_ohlcv)} 1s bars")
    logger.info(f"Non-zero volume bars: {(df_ohlcv['volume'] > 0).sum()}")
    
    return df_ohlcv

def process_msgpack_file(msgpack_path, output_dir):
    """Process a single msgpack file and create continuous OHLCV"""
    logger.info(f"Processing {msgpack_path}")
    
    # Extract date from filename
    date_str = os.path.basename(msgpack_path).replace('.msgpack', '')
    
    # Load msgpack data
    with open(msgpack_path, 'rb') as f:
        data = msgpack.load(f)
    
    trades = data.get('trades', [])
    if not trades:
        logger.warning(f"No trades found in {msgpack_path}")
        return
    
    # Create continuous OHLCV
    df_ohlcv = create_continuous_ohlcv_from_trades(trades, date_str)
    if df_ohlcv is None:
        return
    
    # Create output directory
    output_path = Path(output_dir) / "ohlcv" / "1s"
    output_path.mkdir(parents=True, exist_ok=True)
    
    # Save as parquet
    output_file = output_path / f"{date_str}.parquet"
    df_ohlcv.to_parquet(output_file, index=False)
    
    logger.info(f"Saved {len(df_ohlcv)} continuous 1s OHLCV bars to {output_file}")

def main():
    # Configuration
    msgpack_dir = "daily_combined_data_coinapi"
    output_dir = "parquet_raw"
    
    # Target dates
    target_dates = [
        "2025-07-01"
    ]
    
    logger.info("=== Creating Continuous 1s OHLCV from Trade Data ===")
    logger.info(f"Source directory: {msgpack_dir}")
    logger.info(f"Output directory: {output_dir}")
    logger.info(f"Target dates: {target_dates}")
    
    for date_str in target_dates:
        msgpack_file = f"{msgpack_dir}/{date_str}.msgpack"
        if os.path.exists(msgpack_file):
            process_msgpack_file(msgpack_file, output_dir)
        else:
            logger.warning(f"Msgpack file not found: {msgpack_file}")
    
    logger.info("=== Processing Complete ===")

if __name__ == "__main__":
    main()