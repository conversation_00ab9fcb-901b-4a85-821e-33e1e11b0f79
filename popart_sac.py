from stable_baselines3.common.utils import polyak_update
import numpy as np
from stable_baselines3.sac.sac import SAC
from popart_sac_policy import PopArtSACPolicy
import torch as th
from typing import Any, Dict, List, Optional, Tuple, Type, Union, Callable

class PopArtSAC(SAC):
    """
    Soft Actor-Critic (SAC) with PopArt normalization for the critic networks.

    This implementation extends the standard SAC algorithm to use PopArt normalization
    for the critic networks, which helps stabilize learning when rewards have varying scales.
    """

    def __init__(
        self,
        policy: Union[str, Type[PopArtSACPolicy]],
        env: Any,
        learning_rate: Union[float, Callable[[float], float]] = 3e-4,
        buffer_size: int = 1000000,
        learning_starts: int = 100,
        batch_size: int = 256,
        tau: float = 0.005,
        gamma: float = 0.99,
        train_freq: Union[int, Tuple[int, str]] = 1,
        gradient_steps: int = 1,
        action_noise: Optional[Any] = None,
        replay_buffer_class: Optional[Any] = None,
        replay_buffer_kwargs: Optional[Dict[str, Any]] = None,
        optimize_memory_usage: bool = False,
        ent_coef: Union[str, float] = "auto",
        target_update_interval: int = 1,
        target_entropy: Union[str, float] = "auto",
        use_sde: bool = False,
        sde_sample_freq: int = -1,
        use_sde_at_warmup: bool = False,
        tensorboard_log: Optional[str] = None,
        policy_kwargs: Optional[Dict[str, Any]] = None,
        verbose: int = 0,
        seed: Optional[int] = None,
        device: Union[th.device, str] = "auto",
        _init_setup_model: bool = True,
    ):
        super().__init__(
            policy=policy,
            env=env,
            learning_rate=learning_rate,
            buffer_size=buffer_size,
            learning_starts=learning_starts,
            batch_size=batch_size,
            tau=tau,
            gamma=gamma,
            train_freq=train_freq,
            gradient_steps=gradient_steps,
            action_noise=action_noise,
            replay_buffer_class=replay_buffer_class,
            replay_buffer_kwargs=replay_buffer_kwargs,
            optimize_memory_usage=optimize_memory_usage,
            ent_coef=ent_coef,
            target_update_interval=target_update_interval,
            target_entropy=target_entropy,
            use_sde=use_sde,
            sde_sample_freq=sde_sample_freq,
            use_sde_at_warmup=use_sde_at_warmup,
            tensorboard_log=tensorboard_log,
            policy_kwargs=policy_kwargs,
            verbose=verbose,
            seed=seed,
            device=device,
            _init_setup_model=_init_setup_model,
        )

        # PopArt layers will be set up in _setup_model, not here!

    def _setup_model(self) -> None:
        super()._setup_model()
        from popart_layer import PopArt
        # Check which Q-network attributes are available
        q_networks = []
        if hasattr(self.critic, 'qf0') and hasattr(self.critic_target, 'qf0'):
            q_networks.append((self.critic.qf0, self.critic_target.qf0))
        if hasattr(self.critic, 'qf1') and hasattr(self.critic_target, 'qf1'):
            q_networks.append((self.critic.qf1, self.critic_target.qf1))
        if hasattr(self.critic, 'qf2') and hasattr(self.critic_target, 'qf2'):
            q_networks.append((self.critic.qf2, self.critic_target.qf2))
        for online, target in q_networks:
            last_in = online[-1].in_features
            pop = PopArt(last_in, 1).to(self.device)
            pop.load_state_dict(online[-1].state_dict(), strict=False)
            online[-1] = pop
            target[-1] = PopArt(last_in, 1).to(self.device)
            target[-1].load_state_dict(pop.state_dict())
        # Store references to PopArt layers for easy access
        self._pop_layers = [net[-1] for net, _ in q_networks]
        # Set a reference to this algorithm in the policy for accessing PopArt layers
        if hasattr(self.policy, 'set_algorithm'):
            self.policy.set_algorithm(self)

    def train(self, gradient_steps: int, batch_size: int = 64) -> None:
        """
        Update policy using the currently gathered rollout buffer.

        This method overrides the standard SAC train method to use PopArt normalization
        for the critic loss calculation.

        Args:
            gradient_steps: Number of gradient steps to take
            batch_size: Batch size for sampling from the replay buffer
        """
        # Switch to train mode (this affects batch norm / dropout)
        self.policy.set_training_mode(True)

        # Update optimizers learning rate
        optimizers = [self.actor.optimizer, self.critic.optimizer]
        if self.ent_coef_optimizer is not None:
            optimizers += [self.ent_coef_optimizer]

        # Update learning rate according to lr schedule
        self._update_learning_rate(optimizers)

        ent_coef_losses, ent_coefs = [], []
        actor_losses, critic_losses = [], []

        for gradient_step in range(gradient_steps):
            # Sample replay buffer
            replay_data = self.replay_buffer.sample(batch_size, env=self._vec_normalize_env)

            # We need to sample because `log_std` may have changed between two gradient steps
            if self.use_sde:
                self.actor.reset_noise()

            # Action by the current actor for the sampled state
            actions_pi, log_prob = self.actor.action_log_prob(replay_data.observations)
            log_prob = log_prob.reshape(-1, 1)

            ent_coef_loss = None
            if self.ent_coef_optimizer is not None and hasattr(self, 'log_alpha'):
                # Important: detach the variable from the graph
                # so we don't change it with other losses
                # see https://github.com/rail-berkeley/softlearning/issues/60
                ent_coef = th.exp(self.log_alpha.detach())
                ent_coef_loss = -(self.log_alpha * (log_prob + self.target_entropy).detach()).mean()
                ent_coef_losses.append(ent_coef_loss.item())
            elif hasattr(self, 'ent_coef_tensor'):
                ent_coef = self.ent_coef_tensor
            else:
                # Fallback to fixed entropy coefficient if no tensor exists
                # Handle "auto_X" format by extracting the initial value
                if isinstance(self.ent_coef, str) and self.ent_coef.startswith("auto"):
                    if "_" in self.ent_coef:
                        initial_value = float(self.ent_coef.split("_")[1])
                    else:
                        initial_value = 0.3  # Default if no value specified
                    ent_coef = th.tensor(initial_value, device=self.device)
                else:
                    ent_coef = th.tensor(float(self.ent_coef), device=self.device)

            ent_coefs.append(ent_coef.item())

            # Optimize entropy coefficient, also called
            # entropy temperature or alpha in the paper
            if ent_coef_loss is not None:
                self.ent_coef_optimizer.zero_grad()
                ent_coef_loss.backward()
                self.ent_coef_optimizer.step()

            with th.no_grad():
                # Select action according to policy
                next_actions, next_log_prob = self.actor.action_log_prob(replay_data.next_observations)
                # Compute the next Q values: min over all critics targets
                next_q_values = self.critic_target(replay_data.next_observations, next_actions)
                # Handle the case where next_q_values is a tuple of tensors
                if isinstance(next_q_values, tuple):
                    # Take the minimum value across all critic networks
                    next_q_values = th.min(th.cat([q_val.unsqueeze(0) for q_val in next_q_values], dim=0), dim=0)[0]
                    # Ensure the shape is correct for further calculations
                    if next_q_values.ndim == 1:
                        next_q_values = next_q_values.reshape(-1, 1)
                else:
                    # If it's already a tensor, apply min along the appropriate dimension
                    next_q_values = th.min(next_q_values, dim=1, keepdim=True)[0]
                # Add entropy term
                next_q_values = next_q_values - ent_coef * next_log_prob.reshape(-1, 1)
                # td error + entropy term
                target_q_values = replay_data.rewards + (1 - replay_data.dones) * self.gamma * next_q_values

                # Update PopArt normalization parameters
                for pop_layer in self._pop_layers:
                    pop_layer.update_parameters(target_q_values)

            # Get current Q-values estimates for each critic network
            current_q_values = self.critic(replay_data.observations, replay_data.actions)

            # Compute critic loss using normalized values
            critic_loss = 0
            for i, q_values in enumerate(current_q_values):
                # Get the appropriate PopArt layer
                pop_layer = self._pop_layers[i]
                # Normalize current Q-values
                norm_q = pop_layer.normalize(q_values)
                # Normalize target values using the same PopArt layer
                norm_targets = pop_layer.normalize(target_q_values)
                # Add to the loss
                critic_loss += 0.5 * th.nn.functional.mse_loss(norm_q, norm_targets)

            critic_losses.append(critic_loss.item())

            # Optimize the critic
            self.critic.optimizer.zero_grad()
            critic_loss.backward()
            self.critic.optimizer.step()

            # Compute actor loss
            q_values_pi = self.critic(replay_data.observations, actions_pi)
            # Handle the case where q_values_pi is a tuple of tensors
            if isinstance(q_values_pi, tuple):
                # Take the minimum value across all critic networks
                min_qf_pi = th.min(th.cat([q_val.unsqueeze(0) for q_val in q_values_pi], dim=0), dim=0)[0]
                # Ensure the shape is correct for the actor loss calculation
                if min_qf_pi.ndim == 1:
                    min_qf_pi = min_qf_pi.reshape(-1, 1)
            else:
                # Use dim=1 to get the minimum across critics, not across batch
                min_qf_pi = th.min(q_values_pi, dim=1, keepdim=True)[0]
            actor_loss = (ent_coef * log_prob - min_qf_pi).mean()
            actor_losses.append(actor_loss.item())

            # Optimize the actor
            self.actor.optimizer.zero_grad()
            actor_loss.backward()
            self.actor.optimizer.step()

            # Update target networks
            if gradient_step % self.target_update_interval == 0:
                polyak_update(self.critic.parameters(), self.critic_target.parameters(), self.tau)

        self._n_updates += gradient_steps

        logger = self.logger
        if logger is not None:
            logger.record("train/n_updates", self._n_updates, exclude="tensorboard")
            logger.record("train/ent_coef", np.mean(ent_coefs))
            logger.record("train/actor_loss", np.mean(actor_losses))
            logger.record("train/critic_loss", np.mean(critic_losses))
            if len(ent_coef_losses) > 0:
                logger.record("train/ent_coef_loss", np.mean(ent_coef_losses))

            # Log PopArt statistics
            logger.record("train/pop_mean_q1", self._pop_layers[0].mean.mean().item())
            logger.record("train/pop_std_q1", self._pop_layers[0].std.mean().item())
            logger.record("train/pop_mean_q2", self._pop_layers[1].mean.mean().item())
            logger.record("train/pop_std_q2", self._pop_layers[1].std.mean().item())
