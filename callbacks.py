"""
Custom callbacks for reinforcement learning training.
"""
import logging
import numpy as np
import torch
from pathlib import Path
from stable_baselines3.common.callbacks import BaseCallback

class EpochGateCallback(BaseCallback):
    """
    Adds decaying noise to actions during training to encourage exploration.
    The noise decays linearly from noise_std_start to noise_std_end over decay_ts timesteps.
    """
    def __init__(self,
                 noise_std_start=1e-4,
                 noise_std_end=0,
                 decay_ts=3_000_000,
                 verbose=0):
        super().__init__(verbose)
        self.noise_std_start = noise_std_start
        self.noise_std_end = noise_std_end
        self.decay_ts = decay_ts
        self.original_action = None

    def _on_step(self) -> bool:
        # Calculate current noise standard deviation
        sigma = max(self.noise_std_end,
                    self.noise_std_start * (1 - self.num_timesteps / self.decay_ts))

        # Only apply noise if sigma > 0
        if sigma > 0:
            # Store the original action function if not already stored
            if self.original_action is None:
                self.original_action = self.model.policy._predict

                # Store callback reference for use in the nested function
                callback_self = self

                # Define the noisy action function
                def noisy_action(observation, deterministic=False):
                    # 1) Call the original predict/forward method
                    result = callback_self.original_action(observation, deterministic=deterministic)

                    # 2) Unpack any length tuple
                    if isinstance(result, tuple):
                        action, *rest = result
                        state = tuple(rest) if rest else None
                    else:
                        action = result
                        state = None
                        rest = []

                    # 3) Add noise in stochastic mode
                    if not deterministic:
                        # Calculate current noise standard deviation
                        current_sigma = max(callback_self.noise_std_end,
                                           callback_self.noise_std_start * (1 - callback_self.num_timesteps / callback_self.decay_ts))

                        # Add noise if sigma > 0
                        if current_sigma > 0:
                            noise = np.random.normal(0, current_sigma, size=action.shape)
                            action = np.clip(action + noise, -1, 1)  # Clip to ensure actions remain in valid range

                            # Log occasionally (less frequently to reduce overhead)
                            if callback_self.num_timesteps % 500000 == 0:
                                logging.info(f"EpochGate: Adding noise σ={current_sigma:.6f}")

                    # 4) Return in the same shape as original
                    if state is None:
                        return action
                    # If there was exactly one extra return, return a 2-tuple
                    if len(rest) == 1:
                        return action, state
                    # If there were multiple, re-splice them back in
                    return (action, *rest)

                # Replace the action function
                self.model.policy._predict = noisy_action
                logging.info(f"EpochGate: Initialized with sigma={sigma:.6f}, decay_ts={self.decay_ts}")
        elif self.original_action is not None:
            # Restore original action function when noise is no longer needed
            self.model.policy._predict = self.original_action
            self.original_action = None
            logging.info("EpochGate: Noise decay complete, restored original action function")

        return True

class DynamicEntropyCallback(BaseCallback):
    """
    Callback to dynamically adjust the entropy coefficient during training.
    This helps with exploration by starting with a higher entropy coefficient
    and gradually decreasing it as training progresses to achieve more deterministic
    actions by the end of training.
    """
    def __init__(self, start_entropy_coef=3e-3, end_entropy_coef=1e-3,
                 decay_steps=5_000_000, verbose=0):
        super().__init__(verbose)
        self.start = start_entropy_coef
        self.end = end_entropy_coef
        self.decay = decay_steps
        self.last = 0

    def _on_step(self):
        if self.num_timesteps - self.last < 10_000:
            return True
        self.last = self.num_timesteps
        progress = min(1.0, self.num_timesteps / self.decay)
        target = self.start + (self.end - self.start) * progress

        # fixed-coef
        coef_t = getattr(self.model, "ent_coef_tensor", None)
        if coef_t is not None:
            coef_t.copy_(torch.tensor(target, device=coef_t.device))
            self.logger.record("train/entropy_coef", target)
            if self.verbose > 0 and progress < 1.0:
                logging.info(f"Updated entropy coefficient to {target:.6f} ({progress*100:.1f}% of decay)")
            return True

        # auto-alpha
        if hasattr(self.model, "target_entropy"):
            # lineárny prechod z -2 → -4 (približné mapovanie)
            self.model.target_entropy = -2.0 + (-2.0 * progress)
            self.logger.record("train/target_entropy", self.model.target_entropy)

            # Also log the actual entropy coefficient if available
            if hasattr(self.model, 'log_alpha'):
                alpha = torch.exp(self.model.log_alpha).item()
                self.logger.record("train/entropy_coef", alpha)
                if self.verbose > 0 and progress < 1.0:
                    logging.info(f"Updated target entropy to {self.model.target_entropy:.4f} (coef: {alpha:.6f}, {progress*100:.1f}% of decay)")
        return True

class PerformanceBasedRefresh(BaseCallback):
    """
    Resets the replay buffer when evaluation performance stagnates.
    Monitors the mean reward from evaluation and resets the buffer after
    a specified number of checks without improvement.
    """
    def __init__(self, patience=4, check_freq=2_000_000, verbose=0):
        super().__init__(verbose)
        self.patience = patience
        self.check_freq = check_freq
        self.best = -np.inf
        self.bad = 0

    def _on_step(self):
        if self.num_timesteps % self.check_freq != 0:
            return True

        # Get the current mean reward from evaluation
        mean_rew = self.model.logger.name_to_value.get("eval/mean_reward", None)
        if mean_rew is None:
            return True

        # Check if performance has improved
        if mean_rew <= self.best:
            self.bad += 1
        else:
            self.best = mean_rew
            self.bad = 0

        # Reset buffer if performance has stagnated for too long
        if self.bad >= self.patience:
            self.model.replay_buffer.reset()
            logging.info(f"PER buffer reset – eval stagnated {self.bad}×")
            self.bad = 0

        return True

class DynamicGradientStepsCallback(BaseCallback):
    """
    Dynamically adjusts gradient steps based on the current learning rate.
    Scales gradient steps inversely with learning rate to maintain consistent
    learning progress as learning rate decreases.
    """
    def __init__(self, base_steps=8, lr_threshold=3e-4, max_steps=64, verbose=0):
        super().__init__(verbose)
        self.base_steps = base_steps
        self.lr_threshold = lr_threshold
        self.max_steps = max_steps
        self.last_update = 0

    def _on_step(self):
        # Only check periodically to avoid excessive logging
        if self.num_timesteps - self.last_update < 10000:
            return True

        # Calculate current learning rate
        progress_remaining = 1.0 - (self.num_timesteps / self.model._total_timesteps)
        lr = self.model.lr_schedule(progress_remaining)

        # Calculate target gradient steps inversely proportional to learning rate
        target = int(np.clip(
            self.base_steps * (self.lr_threshold / lr),
            self.base_steps,
            self.max_steps
        ))

        # Update gradient steps if changed
        if target != self.model.gradient_steps:
            self.model.gradient_steps = target
            logging.info(f"gradient_steps → {target} (lr={lr:.2e})")
            self.last_update = self.num_timesteps

        return True

class PERBetaCallback(BaseCallback):
    """Linear annealing of β from 0.4 → 1.0 to reduce PER bias."""
    def __init__(self, beta_start=0.4, beta_end=1.0,
                 anneal_steps=5_000_000, update_freq=10_000, verbose=0):
        super().__init__(verbose)
        self.beta_start  = beta_start
        self.beta_end    = beta_end
        self.anneal_steps = anneal_steps
        self.update_freq  = update_freq

    def _on_step(self) -> bool:
        if self.n_calls % self.update_freq:
            return True

        progress = min(1.0, self.num_timesteps / self.anneal_steps)
        new_beta  = self.beta_start + progress * (self.beta_end - self.beta_start)

        if hasattr(self.model, 'replay_buffer'):
            self.model.replay_buffer.beta = new_beta

        self.logger.record('replay/beta', new_beta)
        return True

class EntropyFloorCallback(BaseCallback):
    """
    Callback to decay entropy coefficient from initial value to a floor value.
    Ensures the agent becomes less random over time while maintaining minimum exploration.
    """
    def __init__(self, floor=0.02, decay_ts=1_000_000, verbose=0):
        super().__init__(verbose)
        self.floor = floor
        self.decay_ts = decay_ts
        self.initial_alpha = None
        self.last_update = 0

    def _on_step(self) -> bool:
        # Only check periodically to avoid excessive computation
        if self.num_timesteps - self.last_update < 10_000:
            return True

        # Store initial alpha value
        if self.initial_alpha is None and hasattr(self.model, 'log_alpha'):
            self.initial_alpha = torch.exp(self.model.log_alpha).item()
        elif self.initial_alpha is None:
            self.initial_alpha = 0.3  # Default fallback

        # Calculate progress and target alpha
        progress = min(1.0, self.num_timesteps / self.decay_ts)
        target_alpha = max(self.floor, self.initial_alpha * (1 - progress))

        # Update entropy coefficient if auto-tuning is enabled
        if hasattr(self.model, 'log_alpha'):
            # Update the log_alpha parameter directly
            self.model.log_alpha.data.copy_(torch.log(torch.tensor(target_alpha, device=self.model.log_alpha.device)))
            self.logger.record("train/entropy_floor_target", target_alpha)
            
            if self.verbose > 0 and progress < 1.0:
                logging.info(f"Entropy floor: α={target_alpha:.4f} ({progress*100:.1f}% decay)")
        
        self.last_update = self.num_timesteps
        return True

class VecNormSaver(BaseCallback):
    """
    Saves vectorized environment normalization statistics at regular intervals.
    This is useful for preserving normalization statistics at checkpoints
    so they can be loaded alongside the model during inference.
    """
    def __init__(self, venv, save_path, checkpoint_freq=50000, verbose=0):
        super().__init__(verbose)
        self.venv = venv
        self.save_path = Path(save_path)
        self.checkpoint_freq = checkpoint_freq
        # Ensure the save directory exists
        self.save_path.mkdir(parents=True, exist_ok=True)

    def _on_step(self) -> bool:
        # Save VecNormalize stats whenever a checkpoint is saved
        # Check if we're at a checkpoint step
        if self.num_timesteps > 0 and self.num_timesteps % self.checkpoint_freq == 0:
            if not hasattr(self.model, '_last_checkpoint_step') or self.num_timesteps > self.model._last_checkpoint_step:
                vec_path = self.save_path / f"sac_{self.num_timesteps}.vecnorm.pkl"
                try:
                    # Save the vectorized environment normalization statistics
                    self.venv.save(vec_path)
                    if self.verbose > 0:
                        logging.info(f"VecNormSaver: Saved normalization stats to {vec_path}")
                    # Mark this checkpoint as saved
                    self.model._last_checkpoint_step = self.num_timesteps
                except Exception as e:
                    logging.error(f"VecNormSaver: Failed to save normalization stats: {e}")
        return True

class NetSharpeMonitor(BaseCallback):
    """
    Task 3: Monitors net Sharpe ratio after real fees for better performance tracking.
    Calculates Sharpe ratio using episode rewards adjusted for realistic transaction costs.
    """
    def __init__(self, check_freq=50000, window_size=100, fee_rate=0.0005, verbose=0):
        super().__init__(verbose)
        self.check_freq = check_freq
        self.window_size = window_size
        self.fee_rate = fee_rate  # Real trading fees (0.05%)
        self.episode_returns = []
        self.last_check = 0
        
    def _on_step(self) -> bool:
        # Only check periodically to avoid excessive computation
        if self.num_timesteps - self.last_check < self.check_freq:
            return True
        
        # Get episode rewards from the logger
        ep_rew_mean = self.locals.get('infos', [{}])[0].get('episode', {}).get('r', None)
        if ep_rew_mean is not None:
            # Adjust return for realistic transaction costs
            # Estimate trades per episode and apply fee impact
            trades_per_episode = 10  # Reasonable estimate for scalping
            fee_impact = trades_per_episode * self.fee_rate * 2  # Entry + exit fees
            net_return = ep_rew_mean - fee_impact
            
            self.episode_returns.append(net_return)
            
            # Keep only recent episodes for rolling Sharpe calculation
            if len(self.episode_returns) > self.window_size:
                self.episode_returns.pop(0)
        
        # Calculate net Sharpe ratio if we have enough data
        if len(self.episode_returns) >= 10:
            returns_array = np.array(self.episode_returns)
            mean_return = np.mean(returns_array)
            std_return = np.std(returns_array)
            
            # Calculate annualized Sharpe ratio (assuming ~252 trading days)
            if std_return > 0:
                net_sharpe = (mean_return / std_return) * np.sqrt(252)
            else:
                net_sharpe = 0.0
            
            # Log the net Sharpe ratio
            self.logger.record("performance/net_sharpe_ratio", net_sharpe)
            self.logger.record("performance/mean_net_return", mean_return)
            self.logger.record("performance/return_std", std_return)
            
            if self.verbose > 0:
                logging.info(f"Net Sharpe ratio (after fees): {net_sharpe:.2f}")
        
        self.last_check = self.num_timesteps
        return True

class LearningRateStabilizer(BaseCallback):
    """
    Task 4: Implements learning rate freezing when Sharpe ratio stabilizes around 12 ± 0.3.
    Prevents learning rate decay when performance is optimal to maintain stability.
    """
    def __init__(self, target_sharpe=12.0, tolerance=0.3, stability_checks=5, 
                 check_freq=100000, verbose=0):
        super().__init__(verbose)
        self.target_sharpe = target_sharpe
        self.tolerance = tolerance
        self.stability_checks = stability_checks
        self.check_freq = check_freq
        self.sharpe_history = []
        self.stable_count = 0
        self.lr_frozen = False
        self.frozen_lr = None
        self.last_check = 0
        
    def _on_step(self) -> bool:
        # Only check periodically
        if self.num_timesteps - self.last_check < self.check_freq:
            return True
            
        # Get current Sharpe ratio from the logger
        current_sharpe = self.logger.name_to_value.get("performance/net_sharpe_ratio", None)
        
        if current_sharpe is not None:
            self.sharpe_history.append(current_sharpe)
            
            # Keep only recent history for stability analysis
            if len(self.sharpe_history) > self.stability_checks * 2:
                self.sharpe_history.pop(0)
            
            # Check if Sharpe is stable in target range
            if len(self.sharpe_history) >= self.stability_checks:
                recent_sharpes = self.sharpe_history[-self.stability_checks:]
                
                # Check if all recent Sharpe values are in target range
                in_target_range = all(
                    self.target_sharpe - self.tolerance <= s <= self.target_sharpe + self.tolerance
                    for s in recent_sharpes
                )
                
                if in_target_range:
                    self.stable_count += 1
                    
                    # Freeze learning rate if consistently stable
                    if self.stable_count >= 2 and not self.lr_frozen:
                        # Freeze the learning rate at current value
                        progress_remaining = 1.0 - (self.num_timesteps / self.model._total_timesteps)
                        current_lr = self.model.lr_schedule(progress_remaining)
                        self.frozen_lr = current_lr
                        
                        # Replace the learning rate schedule with a constant function
                        self.model.lr_schedule = lambda _: self.frozen_lr
                        self.lr_frozen = True
                        
                        logging.info(f"Learning rate FROZEN at {self.frozen_lr:.2e} - Sharpe stable at {current_sharpe:.2f}")
                        self.logger.record("training/lr_frozen", 1.0)
                        self.logger.record("training/frozen_lr_value", self.frozen_lr)
                    
                    elif self.lr_frozen:
                        # Continue logging frozen status
                        self.logger.record("training/lr_frozen", 1.0)
                        self.logger.record("training/frozen_lr_value", self.frozen_lr)
                else:
                    self.stable_count = 0
                    # Note: We don't unfreeze once frozen to maintain stability
                    if self.lr_frozen:
                        self.logger.record("training/lr_frozen", 1.0)
                        self.logger.record("training/frozen_lr_value", self.frozen_lr)
                
                # Log stability metrics
                self.logger.record("performance/sharpe_stability_count", self.stable_count)
                self.logger.record("performance/in_target_sharpe_range", float(in_target_range))
        
        self.last_check = self.num_timesteps
        return True