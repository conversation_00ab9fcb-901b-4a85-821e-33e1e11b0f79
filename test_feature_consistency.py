#!/usr/bin/env python3
"""
Test konzistencie features medzi simul<PERSON>ciou a live tradingom
"""
import pandas as pd
import numpy as np
import json
from pathlib import Path
import logging

logging.basicConfig(level=logging.INFO)
log = logging.getLogger("FeatureTest")

def test_simulation_features():
    """Test features v simulácii"""
    log.info("=== TESTING SIMULATION FEATURES ===")
    
    # Load config
    with open('strategyConfig_scalp_1s.json', 'r') as f:
        config = json.load(f)
    
    expected_features = config['envSettings']['feature_columns']
    log.info(f"Expected features from config: {len(expected_features)}")
    
    # Check parquet file (ako v simulácii)
    parquet_file = Path('parquet_processed/XRPUSDC/5m/2025-07-06.parquet')
    if not parquet_file.exists():
        log.error("Parquet file not found!")
        return False
    
    df = pd.read_parquet(parquet_file)
    log.info(f"Features in parquet: {len(df.columns)}")
    
    # Check which features are present (ako v simulate_trading_new.py)
    present_features = [col for col in expected_features if col in df.columns]
    missing_features = [col for col in expected_features if col not in df.columns]
    
    log.info(f"Present features: {len(present_features)}")
    log.info(f"Missing features: {len(missing_features)}")
    
    if missing_features:
        log.warning(f"Missing features: {missing_features}")
    
    # This is what simulation actually uses
    simulation_features = present_features
    log.info(f"✅ Simulation uses: {len(simulation_features)} features")
    
    return simulation_features

def test_live_trading_features():
    """Test features v live tradingu"""
    log.info("=== TESTING LIVE TRADING FEATURES ===")
    
    try:
        from indicators import calculate_and_merge_indicators
        
        # Load config
        with open('strategyConfig_scalp_1s.json', 'r') as f:
            config = json.load(f)
        
        expected_features = config['envSettings']['feature_columns']
        
        # Create dummy data like live trading does
        timestamps = pd.date_range(start='2025-01-01', periods=100, freq='5min', tz='UTC')
        dummy_data = pd.DataFrame({
            'open': np.random.uniform(1.0, 2.0, 100),
            'high': np.random.uniform(1.1, 2.1, 100),
            'low': np.random.uniform(0.9, 1.9, 100),
            'close': np.random.uniform(1.0, 2.0, 100),
            'volume': np.random.uniform(1000, 10000, 100),
        }, index=timestamps)
        
        data_dict = {'5m': dummy_data}
        
        # Generate features like live trading
        result_df, calculated_features = calculate_and_merge_indicators(
            data_dict, config,
            skip_hmm=True,
            hmm_model_external=None,
            hmm_scaler_external=None
        )
        
        log.info(f"Live trading generates: {len(result_df.columns)} features")
        log.info(f"Expected from config: {len(expected_features)}")
        
        # Simulate the feature processing from live_trading.py
        feature_cols = expected_features
        
        # Fill missing features with zeros (ako v live_trading.py)
        missing_cols = [col for col in feature_cols if col not in result_df.columns]
        if missing_cols:
            log.warning(f"Missing {len(missing_cols)} columns → filled with zeros: {', '.join(missing_cols)}")
            for col in missing_cols:
                result_df[col] = 0.0
        
        # Extract only the required features in correct order (ako v live_trading.py)
        result_df = result_df[feature_cols].astype("float32")
        
        log.info(f"✅ Live trading after processing: {len(result_df.columns)} features")
        
        return list(result_df.columns)
        
    except Exception as e:
        log.error(f"❌ Live trading feature test failed: {e}")
        return None

def test_model_expectations():
    """Test očakávaní modelu"""
    log.info("=== TESTING MODEL EXPECTATIONS ===")
    
    try:
        from popart_sac import PopArtSAC
        from agent import SimpleCNN1D, SafeReplayBuffer
        
        # Load config
        with open('strategyConfig_scalp_1s.json', 'r') as f:
            config = json.load(f)
        
        feature_cols = config["envSettings"]["feature_columns"]
        training_settings = config["trainingSettings"]
        lookback = config["envSettings"].get("state_lookback", 30)
        
        # Calculate expected observation space
        expected_obs_size = len(feature_cols) * lookback + 11  # +11 for meta features
        
        log.info(f"Expected observation space calculation:")
        log.info(f"   Features: {len(feature_cols)}")
        log.info(f"   Lookback: {lookback}")
        log.info(f"   Meta features: 11")
        log.info(f"   Total expected: {expected_obs_size}")
        
        # Find latest model
        model_files = list(Path(".").glob("sac_*_steps.zip"))
        if not model_files:
            log.error("No model files found!")
            return None
        
        latest_model = sorted(model_files, key=lambda x: int(x.stem.split('_')[1]))[-1]
        log.info(f"Testing model: {latest_model}")
        
        # Setup custom objects
        policy_kwargs = {
            "net_arch": training_settings["netArch"],
            "features_extractor_class": SimpleCNN1D,
            "features_extractor_kwargs": {
                **training_settings["featureExtractorKwargs"],
                "feature_names": feature_cols,
                "meta_len": 11
            }
        }
        
        custom_objects = {
            "buffer_size": training_settings["bufferSize"],
            "policy_kwargs": policy_kwargs,
            "replay_buffer_class": SafeReplayBuffer,
            "learning_rate": 0.0001
        }
        
        # Load model
        model = PopArtSAC.load(latest_model, device="cpu", custom_objects=custom_objects)
        actual_obs_size = model.observation_space.shape[0]
        
        log.info(f"Model actual observation space: {actual_obs_size}")
        
        # Calculate features needed by model
        feats_needed = (actual_obs_size - 11) // lookback
        log.info(f"Features needed by model: {feats_needed}")
        
        return feats_needed
        
    except Exception as e:
        log.error(f"❌ Model test failed: {e}")
        return None

def main():
    """Main test function"""
    log.info("🧪 TESTING FEATURE CONSISTENCY")
    log.info("=" * 60)
    
    # Test simulation features
    simulation_features = test_simulation_features()
    log.info("")
    
    # Test live trading features
    live_features = test_live_trading_features()
    log.info("")
    
    # Test model expectations
    model_feats_needed = test_model_expectations()
    log.info("")
    
    # Compare results
    log.info("=" * 60)
    log.info("🔍 COMPARISON RESULTS")
    log.info("=" * 60)
    
    if simulation_features and live_features and model_feats_needed:
        log.info(f"Simulation features: {len(simulation_features)}")
        log.info(f"Live trading features: {len(live_features)}")
        log.info(f"Model expects: {model_feats_needed}")
        
        # Check consistency
        if len(simulation_features) == len(live_features) == model_feats_needed:
            log.info("✅ ALL CONSISTENT! Features match between simulation, live trading, and model.")
            
            # Check if features are the same
            if simulation_features == live_features:
                log.info("✅ Feature names are identical between simulation and live trading.")
            else:
                log.warning("⚠️ Feature names differ between simulation and live trading.")
                diff_sim = set(simulation_features) - set(live_features)
                diff_live = set(live_features) - set(simulation_features)
                if diff_sim:
                    log.warning(f"   Only in simulation: {diff_sim}")
                if diff_live:
                    log.warning(f"   Only in live trading: {diff_live}")
        else:
            log.error("❌ INCONSISTENT! Feature counts don't match.")
            log.error("   This will cause performance degradation in live trading!")
            
            if len(simulation_features) != model_feats_needed:
                log.error(f"   Simulation vs Model: {len(simulation_features)} vs {model_feats_needed}")
            if len(live_features) != model_feats_needed:
                log.error(f"   Live trading vs Model: {len(live_features)} vs {model_feats_needed}")
    else:
        log.error("❌ Some tests failed. Cannot compare results.")
    
    log.info("=" * 60)

if __name__ == '__main__':
    main()
