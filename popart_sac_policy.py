from stable_baselines3.sac.policies import SACPolicy

class PopArtSACPolicy(SACPolicy):
    """
    SAC policy that works with PopArt normalization.

    This policy doesn't modify the critic networks directly.
    The PopArtSAC algorithm handles the PopArt normalization.
    """

    def set_algorithm(self, alg):
        """
        Set a reference to the algorithm instance.

        Args:
            alg: The algorithm instance
        """
        self.alg = alg
