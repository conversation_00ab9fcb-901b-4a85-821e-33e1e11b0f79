#!/usr/bin/env python3
"""
Fallback data provider that handles CoinAPI quota issues by using Binance WebSocket directly.
Provides seamless failover from CoinAPI to Binance native data streams.
"""

import asyncio
import json
import logging
import time
import threading
from datetime import datetime, timezone, timedelta
from queue import Queue, Empty
from typing import Dict, Any, Optional, Callable
import websockets
from websockets.exceptions import ConnectionClosedError
import requests

log = logging.getLogger("FallbackDataProvider")

class FallbackDataProvider:
    """
    Multi-source data provider with automatic failover.
    Primary: CoinAPI WebSocket
    Fallback: Binance WebSocket
    """
    
    def __init__(self, config: Dict[str, Any], data_queue: Queue):
        """Initialize fallback data provider."""
        self.config = config
        self.data_queue = data_queue
        self.coinapi_key = config.get("coinapi", {}).get("apiKey")
        
        # DEBUG: Log which API key is being used
        if self.coinapi_key:
            masked_key = f"{self.coinapi_key[:8]}...{self.coinapi_key[-4:]}" if len(self.coinapi_key) > 12 else "INVALID_KEY"
            log.info(f"🔍 DEBUG: FallbackDataProvider using CoinAPI key: {masked_key}")
        
        # Validate API key is present
        if not self.coinapi_key or self.coinapi_key.startswith("${"):
            raise ValueError("❌ CoinAPI key not configured! Please set COINAPI_KEY in .env file")
        self.symbol = config.get("dataProvider", {}).get("symbol", "XRPUSDC")
        
        # WebSocket URLs
        self.coinapi_ws_url = "wss://ws.coinapi.io/v1/"
        self.binance_ws_url = "wss://fstream.binance.com/ws/"
        
        # Convert symbol formats
        self.binance_symbol = self._convert_to_binance_symbol(self.symbol)
        self.coinapi_symbol = self._convert_to_coinapi_symbol(self.symbol)
        
        # Connection states per timeframe
        self.coinapi_active = {}
        self.binance_active = {}
        self.primary_failed = {}
        self.reconnect_attempts = {}
        self.max_reconnect_attempts = 5
        
        # Multi-timeframe support
        self.active_timeframes = []
        self.trade_buffer = []  # For 1s aggregation from trade data
        self.last_1s_ohlcv = None
        self.last_5m_ohlcv = None  # For 5m aggregation from trade data
        self.trade_buffer_5m = []  # Buffer for 5m aggregation
        
        # For 1m data, use CoinAPI as requested by user (no need to save quota for testing)
        self.use_rest_for_1m = False  # Disable CoinAPI REST for 1m to save quota
        self.use_binance_for_1m = False  # Use CoinAPI WebSocket for 1m data instead
        self.rest_poll_interval = 15  # seconds - poll every 15s for 1m data (if enabled)
        self.last_1m_poll = 0
        
        log.info(f"FallbackDataProvider initialized:")
        log.info(f"  Original symbol: {self.symbol}")
        log.info(f"  Binance symbol: {self.binance_symbol}")
        log.info(f"  CoinAPI symbol: {self.coinapi_symbol}")
    
    def _convert_to_binance_symbol(self, symbol: str) -> str:
        """Convert symbol to Binance format."""
        if symbol.startswith("BINANCEFTS_PERP_"):
            # Extract from CoinAPI format: BINANCEFTS_PERP_XRP_USDC -> XRPUSDC
            parts = symbol.replace("BINANCEFTS_PERP_", "").split("_")
            return "".join(parts).lower() if len(parts) >= 2 else symbol.lower()
        else:
            # Direct conversion: XRPUSDC -> xrpusdc
            return symbol.replace("/", "").lower()
    
    def _convert_to_coinapi_symbol(self, symbol: str) -> str:
        """Convert symbol to CoinAPI format."""
        if symbol.startswith("BINANCEFTS_PERP_"):
            return symbol
        else:
            # Convert XRPUSDC to BINANCEFTS_PERP_XRP_USDC
            if len(symbol) >= 6:
                # Try to split common quote currencies
                quotes = ["USDT", "USDC", "USD", "BUSD", "BTC", "ETH"]
                for quote in quotes:
                    if symbol.endswith(quote):
                        base = symbol[:-len(quote)]
                        return f"BINANCEFTS_PERP_{base}_{quote}"
            # Fallback: assume first 3 chars are base
            base, quote = symbol[:3], symbol[3:]
            return f"BINANCEFTS_PERP_{base}_{quote}"
    
    def start_streams(self, timeframes: list = ["5m", "1m"]) -> None:
        """Start data streams with fallback logic."""
        log.info(f"💎 Starting CoinAPI-ONLY data streams for timeframes: {timeframes}")
        
        # Initialize per-timeframe states
        self.active_timeframes = timeframes
        for tf in timeframes:
            self.coinapi_active[tf] = False
            self.binance_active[tf] = False
            self.primary_failed[tf] = False
            self.reconnect_attempts[tf] = 0
        
        # For 1s primary timeframe, add 5m for features if not present
        if "1s" in timeframes and "5m" not in timeframes:
            log.info("🔍 Adding 5m timeframe for features (1s decisions require 5m features)")
            timeframes.append("5m")
            self.active_timeframes.append("5m")
            self.coinapi_active["5m"] = False
            self.binance_active["5m"] = False
            self.primary_failed["5m"] = False
            self.reconnect_attempts["5m"] = 0
        
        # STEP 1: Fetch initial 5m data via REST API if needed
        if "5m" in timeframes:
            log.info("🔄 Fetching initial 5m data for indicators...")
            if self.fetch_initial_5m_data():
                log.info("✅ Initial 5m data loaded successfully")
            else:
                log.warning("⚠️ Failed to load initial 5m data - continuing with WebSocket only")
        
        # STEP 2: Start WebSocket streams
        # Use CoinAPI for ALL timeframes (including 1s, even if not officially supported)
        for timeframe in timeframes:
            log.info(f"🚀 Starting CoinAPI stream for {timeframe} timeframe")
            threading.Thread(
                target=self._start_coinapi_stream_thread,
                args=(timeframe,),
                daemon=True
            ).start()
            
            # Also start Binance as fallback (but only if CoinAPI fails)
            threading.Thread(
                target=self._start_binance_stream_thread,
                args=(timeframe,),
                daemon=True
            ).start()
        
        # Give CoinAPI priority and time to establish connections
        time.sleep(3)
        log.info("✅ All CoinAPI streams started")
        
        return
        
        # OLD LOGIC BELOW (DISABLED)
        websocket_timeframes = [tf for tf in timeframes if tf not in ["1m", "1s", "5m"]]
        use_1m_binance_direct = "1m" in timeframes and self.use_binance_for_1m
        use_1m_rest = "1m" in timeframes and self.use_rest_for_1m
        
        if use_1m_binance_direct:
            log.info("💰 Using Binance WebSocket ONLY for 1m data (to save CoinAPI quota)")
            log.info("💰 CoinAPI quota preserved for 5m+ timeframes only")
            # Start Binance WebSocket for 1m data directly
            threading.Thread(
                target=self._start_binance_stream_thread,
                args=("1m",),
                daemon=True
            ).start()
        elif use_1m_rest:
            log.info("🔄 Using REST API polling for 1m data (more stable than WebSocket)")
            # Start REST polling for 1m data
            threading.Thread(
                target=self._start_rest_polling_thread,
                args=("1m",),
                daemon=True
            ).start()
        elif "1m" in timeframes:
            # Use WebSocket for 1m if both REST and Binance direct disabled
            websocket_timeframes.append("1m")
        
        # Start CoinAPI WebSocket streams for other timeframes only
        for tf in websocket_timeframes:
            threading.Thread(
                target=self._start_coinapi_stream_thread,
                args=(tf,),
                daemon=True
            ).start()
        
        # Give CoinAPI a chance to connect
        time.sleep(2)
        
        # Start Binance fallback streams for other timeframes (not 1m if using direct)
        for tf in websocket_timeframes:
            threading.Thread(
                target=self._start_binance_stream_thread,
                args=(tf,),
                daemon=True
            ).start()
    
    def _start_coinapi_stream_thread(self, timeframe: str) -> None:
        """Start CoinAPI WebSocket stream in separate thread."""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self._coinapi_stream(timeframe))
        except Exception as e:
            log.error(f"CoinAPI stream thread failed for {timeframe}: {e}")
        finally:
            loop.close()
    
    def _start_binance_stream_thread(self, timeframe: str) -> None:
        """Start Binance WebSocket stream in separate thread."""
        log.info(f"🚀 Starting Binance WebSocket thread for {timeframe}")
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self._binance_stream(timeframe))
        except Exception as e:
            log.error(f"Binance stream thread failed for {timeframe}: {e}")
        finally:
            loop.close()
    
    def _start_rest_polling_thread(self, timeframe: str) -> None:
        """Start REST API polling for 1m data - more stable than WebSocket."""
        log.info(f"Starting REST API polling for {timeframe} data every {self.rest_poll_interval}s")
        
        while True:
            try:
                current_time = time.time()
                
                # Check if it's time to poll
                if current_time - self.last_1m_poll >= self.rest_poll_interval:
                    self._poll_coinapi_rest(timeframe)
                    self.last_1m_poll = current_time
                
                # Sleep for 1 second before checking again
                time.sleep(1)
                
            except Exception as e:
                log.error(f"REST polling error for {timeframe}: {e}")
                time.sleep(5)  # Wait before retrying
                
    def fetch_initial_5m_data(self) -> bool:
        """Fetch initial 5m data via REST API for indicators."""
        try:
            log.info("🔄 Fetching initial 5m data from CoinAPI REST API...")
            
            # CoinAPI REST URL for recent 5m data
            url = f"https://rest.coinapi.io/v1/ohlcv/{self.coinapi_symbol}/history"
            headers = {"X-CoinAPI-Key": self.coinapi_key}
            
            # Get more historical data - at least 300 bars (25 hours) for reliable indicators
            end_time = datetime.now(timezone.utc)
            start_time = end_time - timedelta(hours=25)
            
            # Format time properly for CoinAPI (remove microseconds, use Z suffix)
            params = {
                "period_id": "5MIN",
                "time_start": start_time.strftime("%Y-%m-%dT%H:%M:%S.000Z"),
                "time_end": end_time.strftime("%Y-%m-%dT%H:%M:%S.000Z"),
                "limit": 300
            }
            
            log.info(f"🔍 Fetching 5m data from {params['time_start']} to {params['time_end']}")
            
            response = requests.get(url, headers=headers, params=params, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                
                log.info(f"✅ Fetched {len(data)} initial 5m bars from CoinAPI REST")
                
                # Process each bar and add to queue
                for bar in data:
                    converted = {
                        "type": "ohlcv",
                        "time_period_start": bar["time_period_start"],
                        "time_period_end": bar["time_period_end"],
                        "price_open": float(bar["price_open"]),
                        "price_high": float(bar["price_high"]),
                        "price_low": float(bar["price_low"]),
                        "price_close": float(bar["price_close"]),
                        "volume_traded": float(bar["volume_traded"])
                    }
                    
                    self.data_queue.put(("ohlcv", converted, "coinapi_rest", "5m"))
                    
                return True
                
            else:
                log.error(f"❌ Failed to fetch 5m data: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            log.error(f"💥 Error fetching initial 5m data: {e}")
            return False
    
    def _poll_coinapi_rest(self, timeframe: str) -> None:
        """Poll CoinAPI REST endpoint for recent 1m data with Binance fallback."""
        try:
            # CoinAPI REST URL for recent data
            url = f"https://rest.coinapi.io/v1/ohlcv/{self.coinapi_symbol}/history"
            headers = {"X-CoinAPI-Key": self.coinapi_key}
            
            # Get last 30 seconds of 1m data
            params = {
                "period_id": "1MIN",
                "time_start": datetime.now(timezone.utc).replace(second=0, microsecond=0).isoformat(),
                "limit": 30
            }
            
            response = requests.get(url, headers=headers, params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                
                for bar in data:
                    # Convert to CoinAPI WebSocket format
                    converted = {
                        "type": "ohlcv",
                        "time_period_start": bar["time_period_start"],
                        "time_period_end": bar["time_period_end"],
                        "price_open": float(bar["price_open"]),
                        "price_high": float(bar["price_high"]),
                        "price_low": float(bar["price_low"]),
                        "price_close": float(bar["price_close"]),
                        "volume_traded": float(bar["volume_traded"])
                    }
                    
                    self.data_queue.put(("ohlcv", converted, "coinapi_rest", timeframe))
                
                log.debug(f"📊 REST API: Fetched {len(data)} 1m bars via REST")
                
            elif response.status_code == 403:
                log.error("❌ CoinAPI quota exceeded! Switching to Binance REST fallback for 1m data")
                self.primary_failed = True
                self._poll_binance_rest(timeframe)  # Fallback to Binance REST
                
            elif response.status_code == 429:
                log.warning("⚠️ CoinAPI rate limited in REST polling, backing off...")
                time.sleep(60)  # Back off for 1 minute
            else:
                log.warning(f"⚠️ REST API error: {response.status_code} - {response.text}")
                if response.status_code in [401, 403]:  # Authentication/quota issues
                    log.error("🔄 Switching to Binance REST fallback due to CoinAPI issues")
                    self.primary_failed = True
                    self._poll_binance_rest(timeframe)
                
        except requests.exceptions.RequestException as e:
            log.error(f"💥 REST API request failed: {e}")
            log.info("🔄 Trying Binance REST fallback...")
            self._poll_binance_rest(timeframe)
        except Exception as e:
            log.error(f"💥 REST polling error: {e}")
            
    def _poll_binance_rest(self, timeframe: str) -> None:
        """Poll Binance REST endpoint for recent 1m data as fallback."""
        try:
            # Binance klines endpoint for 1m data
            url = "https://fapi.binance.com/fapi/v1/klines"
            params = {
                "symbol": self.binance_symbol.upper(),
                "interval": "1m",
                "limit": 30
            }
            
            response = requests.get(url, params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                
                for kline in data:
                    # Convert Binance kline to CoinAPI format
                    open_time = int(kline[0])
                    close_time = int(kline[6])
                    
                    converted = {
                        "type": "ohlcv",
                        "time_period_start": datetime.fromtimestamp(open_time/1000, tz=timezone.utc).isoformat(),
                        "time_period_end": datetime.fromtimestamp(close_time/1000, tz=timezone.utc).isoformat(),
                        "price_open": float(kline[1]),
                        "price_high": float(kline[2]),
                        "price_low": float(kline[3]),
                        "price_close": float(kline[4]),
                        "volume_traded": float(kline[5])
                    }
                    
                    self.data_queue.put(("ohlcv", converted, "binance_rest", timeframe))
                
                log.debug(f"📊 Binance REST: Fetched {len(data)} 1m bars via Binance REST fallback")
                
            else:
                log.error(f"💥 Binance REST API error: {response.status_code} - {response.text}")
                
        except requests.exceptions.RequestException as e:
            log.error(f"💥 Binance REST API request failed: {e}")
        except Exception as e:
            log.error(f"💥 Binance REST polling error: {e}")
    
    async def _coinapi_stream(self, timeframe: str) -> None:
        """CoinAPI WebSocket stream with quota handling."""
        while self.reconnect_attempts[timeframe] < self.max_reconnect_attempts:
            try:
                log.info(f"🔍 Connecting to CoinAPI WebSocket for {timeframe}...")
                log.info(f"🔍 CoinAPI URL: {self.coinapi_ws_url}")
                log.info(f"🔍 CoinAPI Symbol: {self.coinapi_symbol}")
                
                async with websockets.connect(
                    self.coinapi_ws_url,
                    ping_interval=30,
                    ping_timeout=10,
                ) as ws:
                    # Subscribe to data - CoinAPI doesn't support 1s intervals directly
                    # Use trade and book data for 1s, ohlcv for higher timeframes
                    if timeframe == "1s":
                        subscribe_msg = {
                            "type": "hello",
                            "apikey": self.coinapi_key,
                            "heartbeat": True,
                            "subscribe_data_type": ["trade", "book"],
                            "subscribe_filter_symbol_id": [self.coinapi_symbol],
                            "subscribe_data_format": "JSON"
                        }
                    else:
                        # For 5m and other timeframes, use ohlcv
                        # CoinAPI period_id format: 1MIN, 5MIN, 1HRS, 1DAY
                        period_id = timeframe.replace('m', 'MIN').replace('h', 'HRS').replace('d', 'DAY')
                        if timeframe == "5m":
                            period_id = "5MIN"
                        elif timeframe == "1m":
                            period_id = "1MIN"
                        elif timeframe == "1h":
                            period_id = "1HRS"
                        elif timeframe == "1d":
                            period_id = "1DAY"
                        
                        subscribe_msg = {
                            "type": "hello",
                            "apikey": self.coinapi_key,
                            "heartbeat": True,
                            "subscribe_data_type": ["ohlcv"],
                            "subscribe_filter_symbol_id": [self.coinapi_symbol],
                            "subscribe_filter_period_id": [period_id],
                            "subscribe_data_format": "JSON"
                        }
                        
                        log.info(f"🔍 CoinAPI 5m subscription with period_id: {period_id}")
                    
                    log.info(f"🔍 CoinAPI subscription message: {json.dumps(subscribe_msg, indent=2)}")
                    await ws.send(json.dumps(subscribe_msg))
                    log.info(f"✅ CoinAPI {timeframe} subscription sent")
                    
                    self.coinapi_active[timeframe] = True
                    self.reconnect_attempts[timeframe] = 0  # Reset on successful connection
                    
                    message_count = 0
                    async for message in ws:
                        try:
                            message_count += 1
                            log.info(f"📥 CoinAPI {timeframe} message #{message_count}: {str(message)[:200]}...")
                            
                            data = json.loads(message)
                            log.info(f"📊 CoinAPI {timeframe} parsed data: {str(data)[:200]}...")
                            
                            self._process_coinapi_message(data, timeframe)
                            
                        except Exception as e:
                            log.error(f"CoinAPI message processing error: {e}")
                            log.error(f"Raw message: {message}")
                            
            except ConnectionClosedError as e:
                self.coinapi_active[timeframe] = False
                if "1008" in str(e) and "quota" in str(e).lower():
                    log.error(f"CoinAPI quota exceeded for {timeframe}: {e}")
                    self.primary_failed[timeframe] = True
                    log.info(f"Switching to Binance fallback for {timeframe}")
                    break  # Don't retry for quota issues
                else:
                    log.warning(f"CoinAPI connection closed for {timeframe}, retrying: {e}")
                    
            except Exception as e:
                self.coinapi_active[timeframe] = False
                log.error(f"CoinAPI stream error for {timeframe}: {e}")
                
            # Exponential backoff for reconnection
            self.reconnect_attempts[timeframe] += 1
            wait_time = min(2 ** self.reconnect_attempts[timeframe], 60)
            log.info(f"CoinAPI reconnecting in {wait_time}s (attempt {self.reconnect_attempts[timeframe]})")
            await asyncio.sleep(wait_time)
        
        log.error(f"CoinAPI max reconnection attempts reached for {timeframe}")
        self.primary_failed[timeframe] = True
    
    async def _binance_stream(self, timeframe: str) -> None:
        """Binance WebSocket stream as fallback."""
        # Convert timeframe to Binance format
        binance_interval = self._convert_timeframe_to_binance(timeframe)
        
        # Use actual symbol with interval
        streams = [
            f"{self.binance_symbol.upper()}@kline_{binance_interval}"
        ]
        
        # Give CoinAPI time to establish for ALL timeframes
        await asyncio.sleep(10)  # Wait longer for CoinAPI to establish
        
        # Start Binance stream ONLY if CoinAPI fails (fallback only)
        should_start = (not self.coinapi_active.get(timeframe, False) or self.primary_failed.get(timeframe, False))
        
        if should_start:
            log.info(f"🚀 Starting Binance stream for {timeframe} (coinapi_active={self.coinapi_active.get(timeframe, False)}, primary_failed={self.primary_failed.get(timeframe, False)})")
            
            while True:
                try:
                    # Single stream format
                    stream_url = f"wss://fstream.binance.com/ws/{streams[0]}"
                    
                    log.info(f"🔍 Connecting to Binance WebSocket: {stream_url}")
                    
                    async with websockets.connect(stream_url) as ws:
                        self.binance_active[timeframe] = True
                        log.info(f"✅ Binance {timeframe} stream connected successfully")
                        
                        message_count = 0
                        async for message in ws:
                            try:
                                message_count += 1
                                
                                # Log less frequently for high-frequency streams
                                if timeframe == "1s" and message_count % 10 == 0:
                                    log.debug(f"📥 Binance 1s message #{message_count}")
                                elif timeframe != "1s":
                                    log.debug(f"📥 Binance {timeframe} message #{message_count}")
                                
                                data = json.loads(message)
                                self._process_binance_message(data, timeframe)
                                
                            except Exception as e:
                                log.error(f"Binance message processing error: {e}")
                                log.error(f"Raw message that failed: {message}")
                                
                except Exception as e:
                    self.binance_active[timeframe] = False
                    log.error(f"Binance stream error for {timeframe}: {e}")
                    await asyncio.sleep(5)
    
    def _convert_timeframe_to_binance(self, timeframe: str) -> str:
        """Convert timeframe to Binance format."""
        timeframe_map = {
            "1s": "1s",
            "1m": "1m", 
            "5m": "5m",
            "15m": "15m",
            "1h": "1h",
            "4h": "4h",
            "1d": "1d"
        }
        return timeframe_map.get(timeframe, "5m")
    
    def _process_coinapi_message(self, data: Dict[str, Any], timeframe: str) -> None:
        """Process CoinAPI WebSocket message."""
        msg_type = data.get("type")
        
        if msg_type == "ohlcv":
            log.info(f"📊 CoinAPI OHLCV received for {timeframe}: {data.get('price_close', 'N/A')}")
            self.data_queue.put(("ohlcv", data, "coinapi", timeframe))
        elif msg_type == "trade":
            log.info(f"📊 CoinAPI trade received for {timeframe}: price={data.get('price', 'N/A')}, size={data.get('size', 'N/A')}")
            
            # Aggregate trades for all timeframes that need OHLCV data
            if timeframe == "1s":
                self._aggregate_trade_to_1s_ohlcv(data)
            
            # CRITICAL: Also aggregate trades for 5m timeframe (CoinAPI doesn't send live 5m OHLCV)
            if "5m" in self.active_timeframes:
                self._aggregate_trade_to_5m_ohlcv(data)
            
            # Send raw trade data as well
            self.data_queue.put(("trade", data, "coinapi", timeframe))
        elif msg_type == "book":
            log.info(f"📊 CoinAPI orderbook received for {timeframe}")
            self.data_queue.put(("orderbook", data, "coinapi", timeframe))
        elif msg_type == "heartbeat" or msg_type == "hearbeat":
            # CoinAPI heartbeat - ignore silently (typo in CoinAPI: "hearbeat" instead of "heartbeat")
            pass
        elif msg_type == "error":
            log.error(f"❌ CoinAPI error for {timeframe}: {data}")
        else:
            log.warning(f"🔍 Unknown CoinAPI message type '{msg_type}' for {timeframe}: {str(data)[:100]}...")
    
    def _aggregate_trade_to_1s_ohlcv(self, trade_data: Dict[str, Any]) -> None:
        """Aggregate trade data into 1s OHLCV bars."""
        try:
            price = float(trade_data.get("price", 0))
            size = float(trade_data.get("size", 0))
            timestamp = trade_data.get("time_exchange", trade_data.get("time_coinapi", ""))
            
            if not price or not timestamp:
                log.warning(f"⚠️ Invalid trade data: price={price}, timestamp={timestamp}")
                return
            
            # Parse timestamp - handle CoinAPI microsecond format
            if isinstance(timestamp, str):
                # Handle CoinAPI format: '2025-07-08T15:26:29.9090000Z'
                if timestamp.endswith('Z'):
                    # Remove Z and add UTC timezone
                    timestamp = timestamp[:-1] + '+00:00'
                # Handle microsecond precision (7 digits) - truncate to 6 digits
                if '.' in timestamp and len(timestamp.split('.')[1].split('+')[0]) > 6:
                    parts = timestamp.split('.')
                    microseconds = parts[1].split('+')[0][:6]  # Keep only 6 digits
                    timezone_part = '+' + parts[1].split('+')[1] if '+' in parts[1] else ''
                    timestamp = parts[0] + '.' + microseconds + timezone_part
                trade_time = datetime.fromisoformat(timestamp)
            else:
                trade_time = datetime.fromtimestamp(timestamp / 1000, tz=timezone.utc)
            
            # Round to nearest second
            current_second = trade_time.replace(microsecond=0)
            
            # Check if we need to create a new 1s bar
            if self.last_1s_ohlcv is None or self.last_1s_ohlcv["time"] != current_second:
                # Send previous bar if exists
                if self.last_1s_ohlcv is not None:
                    ohlcv_data = {
                        "type": "ohlcv",
                        "time_period_start": self.last_1s_ohlcv["time"].isoformat(),
                        "time_period_end": self.last_1s_ohlcv["time"].isoformat(),
                        "price_open": self.last_1s_ohlcv["open"],
                        "price_high": self.last_1s_ohlcv["high"],
                        "price_low": self.last_1s_ohlcv["low"],
                        "price_close": self.last_1s_ohlcv["close"],
                        "volume_traded": self.last_1s_ohlcv["volume"]
                    }
                    log.info(f"📊 1s OHLCV from trades: ${ohlcv_data['price_close']:.6f} vol={ohlcv_data['volume_traded']:.2f}")
                    self.data_queue.put(("ohlcv", ohlcv_data, "coinapi_agg", "1s"))
                
                # Create new 1s bar
                self.last_1s_ohlcv = {
                    "time": current_second,
                    "open": price,
                    "high": price,
                    "low": price,
                    "close": price,
                    "volume": size
                }
            else:
                # Update existing bar
                self.last_1s_ohlcv["high"] = max(self.last_1s_ohlcv["high"], price)
                self.last_1s_ohlcv["low"] = min(self.last_1s_ohlcv["low"], price)
                self.last_1s_ohlcv["close"] = price
                self.last_1s_ohlcv["volume"] += size
                
        except Exception as e:
            log.error(f"❌ Trade aggregation error: {e}")
            log.error(f"Trade data: {trade_data}")
    
    def _aggregate_trade_to_5m_ohlcv(self, trade_data: Dict[str, Any]) -> None:
        """Aggregate trade data into 5m OHLCV bars - CRITICAL for live 5m data."""
        try:
            price = float(trade_data.get("price", 0))
            size = float(trade_data.get("size", 0))
            timestamp = trade_data.get("time_exchange", trade_data.get("time_coinapi", ""))
            
            if not price or not timestamp:
                log.warning(f"⚠️ Invalid trade data for 5m: price={price}, timestamp={timestamp}")
                return
            
            # Parse timestamp - handle CoinAPI microsecond format
            if isinstance(timestamp, str):
                # Handle CoinAPI format: '2025-07-08T15:26:29.9090000Z'
                if timestamp.endswith('Z'):
                    timestamp = timestamp[:-1] + '+00:00'
                # Handle microsecond precision (7 digits) - truncate to 6 digits
                if '.' in timestamp and len(timestamp.split('.')[1].split('+')[0]) > 6:
                    parts = timestamp.split('.')
                    microseconds = parts[1].split('+')[0][:6]
                    timezone_part = '+' + parts[1].split('+')[1] if '+' in parts[1] else ''
                    timestamp = parts[0] + '.' + microseconds + timezone_part
                trade_time = datetime.fromisoformat(timestamp)
            else:
                trade_time = datetime.fromtimestamp(timestamp / 1000, tz=timezone.utc)
            
            # Round to nearest 5-minute boundary
            minute = trade_time.minute
            rounded_minute = (minute // 5) * 5
            current_5m = trade_time.replace(minute=rounded_minute, second=0, microsecond=0)
            
            # Check if we need to create a new 5m bar
            if self.last_5m_ohlcv is None or self.last_5m_ohlcv["time"] != current_5m:
                # Send previous bar if exists
                if self.last_5m_ohlcv is not None:
                    ohlcv_data = {
                        "type": "ohlcv",
                        "time_period_start": self.last_5m_ohlcv["time"].isoformat(),
                        "time_period_end": (self.last_5m_ohlcv["time"] + timedelta(minutes=5)).isoformat(),
                        "price_open": self.last_5m_ohlcv["open"],
                        "price_high": self.last_5m_ohlcv["high"],
                        "price_low": self.last_5m_ohlcv["low"],
                        "price_close": self.last_5m_ohlcv["close"],
                        "volume_traded": self.last_5m_ohlcv["volume"]
                    }
                    log.info(f"📊 NEW 5m DATA from trades: ${ohlcv_data['price_close']:.6f} vol={ohlcv_data['volume_traded']:.2f} time={ohlcv_data['time_period_start']}")
                    self.data_queue.put(("ohlcv", ohlcv_data, "coinapi_agg", "5m"))
                
                # Create new 5m bar
                self.last_5m_ohlcv = {
                    "time": current_5m,
                    "open": price,
                    "high": price,
                    "low": price,
                    "close": price,
                    "volume": size
                }
                log.info(f"🆕 Created new 5m bar starting at {current_5m}")
            else:
                # Update existing 5m bar
                self.last_5m_ohlcv["high"] = max(self.last_5m_ohlcv["high"], price)
                self.last_5m_ohlcv["low"] = min(self.last_5m_ohlcv["low"], price)
                self.last_5m_ohlcv["close"] = price
                self.last_5m_ohlcv["volume"] += size
                
        except Exception as e:
            log.error(f"❌ 5m Trade aggregation error: {e}")
            log.error(f"Trade data: {trade_data}")
    
    def _process_binance_message(self, data: Dict[str, Any], timeframe: str) -> None:
        """Process Binance WebSocket message."""
        log.info(f"🔍 RAW Binance message received: {str(data)[:200]}...")
        
        # Handle both single-stream and multi-stream formats
        if "stream" in data:
            # Multi-stream format: {"stream": "xrpusdt@kline_1m", "data": {...}}
            stream = data["stream"]
            stream_data = data["data"]
            log.info(f"🔍 Processing multi-stream: {stream}")
            
            if "@kline_" in stream:
                self._process_kline_data(stream_data, timeframe, stream)
            elif "@trade" in stream:
                self._process_trade_data(stream_data, timeframe)
                
        elif "e" in data and data["e"] == "kline":
            # Single-stream format: {"e": "kline", "E": ..., "s": "XRPUSDT", "k": {...}}
            log.info(f"🔍 Processing single-stream kline for {data.get('s', 'UNKNOWN')}")
            self._process_kline_data(data, timeframe, f"{data.get('s', 'UNKNOWN')}@kline")
            
        elif "e" in data and data["e"] == "trade":
            # Single-stream trade format
            self._process_trade_data(data, timeframe)
            
        else:
            log.warning(f"🔍 Unknown message format: {str(data)[:100]}...")

    def _process_kline_data(self, message_data: Dict[str, Any], timeframe: str, stream_name: str) -> None:
        """Process kline data from either single or multi-stream format."""
        
        # Extract kline data based on message format
        if "k" in message_data:
            # Single-stream format: data contains 'k' directly
            kline = message_data["k"]
        else:
            # Multi-stream format: data IS the kline
            kline = message_data.get("k", message_data)
        
        if kline:
            is_closed = kline.get('x', False)  # 'x' indicates if kline is closed
            
            # For 1s and 1m timeframes: process ALL klines (real-time updates)
            # For higher timeframes: process only closed klines (avoid duplicates)
            should_process = (timeframe in ["1s", "1m"]) or is_closed
            
            if should_process:
                # For 1s timeframe, use current time for real-time processing
                if timeframe == "1s":
                    # Use current time rounded to nearest second for consistency
                    current_time = datetime.now(timezone.utc).replace(microsecond=0)
                    start_time = current_time.isoformat()
                    end_time = current_time.isoformat()
                else:
                    # Convert timestamps to ISO format for CoinAPI compatibility
                    start_time = datetime.fromtimestamp(int(kline["t"]) / 1000, tz=timezone.utc).isoformat()
                    end_time = datetime.fromtimestamp(int(kline["T"]) / 1000, tz=timezone.utc).isoformat()
                
                converted = {
                    "type": "ohlcv",
                    "time_period_start": start_time,
                    "time_period_end": end_time,
                    "price_open": float(kline["o"]),
                    "price_high": float(kline["h"]),
                    "price_low": float(kline["l"]),
                    "price_close": float(kline["c"]),
                    "volume_traded": float(kline["v"])
                }
                
                status = "CLOSED" if is_closed else "LIVE"
                
                # Log less frequently for 1s timeframe to reduce noise
                if timeframe == "1s":
                    # Initialize counter if not exists
                    if not hasattr(self, '_kline_message_count'):
                        self._kline_message_count = 0
                    self._kline_message_count += 1
                    
                    # Log every 10th message for 1s
                    if self._kline_message_count % 10 == 0:
                        log.info(f"📊 Binance 1s OHLCV [{status}] #{self._kline_message_count}: price=${converted['price_close']:.6f}")
                else:
                    log.info(f"📊 Binance OHLCV [{status}]: {timeframe} price=${converted['price_close']:.6f} time={start_time}")
                
                self.data_queue.put(("ohlcv", converted, "binance", timeframe))

    def _process_trade_data(self, message_data: Dict[str, Any], timeframe: str) -> None:
        """Process trade data from Binance."""
        converted = {
            "type": "trade",
            "time_exchange": message_data["T"],
            "price": float(message_data["p"]),
            "size": float(message_data["q"]),
            "taker_side": "BUY" if message_data["m"] else "SELL"
        }
        self.data_queue.put(("trade", converted, "binance", timeframe))
    
    def get_status(self) -> Dict[str, Any]:
        """Get current connection status."""
        # Calculate overall status from all timeframes
        any_coinapi_active = any(self.coinapi_active.values())
        any_binance_active = any(self.binance_active.values())
        any_primary_failed = any(self.primary_failed.values())
        
        return {
            "coinapi_active": self.coinapi_active,
            "binance_active": self.binance_active,
            "primary_failed": self.primary_failed,
            "reconnect_attempts": self.reconnect_attempts,
            "active_source": "binance" if (any_binance_active and not any_coinapi_active) else "coinapi",
            "rest_polling_1m": self.use_rest_for_1m,
            "last_1m_poll": self.last_1m_poll,
            "poll_interval": self.rest_poll_interval,
            "active_timeframes": self.active_timeframes,
            "overall_status": {
                "any_coinapi_active": any_coinapi_active,
                "any_binance_active": any_binance_active,
                "any_primary_failed": any_primary_failed,
                "last_1s_ohlcv": self.last_1s_ohlcv is not None,
                "last_5m_ohlcv": self.last_5m_ohlcv is not None,
                "5m_aggregation_active": "5m" in self.active_timeframes and self.last_5m_ohlcv is not None
            }
        }

# Example usage
if __name__ == "__main__":
    # Test the fallback provider
    import logging
    logging.basicConfig(level=logging.INFO)
    
    config = {
        "coinapi": {"apiKey": "test_key"},
        "dataProvider": {"symbol": "XRPUSDC"}
    }
    
    test_queue = Queue()
    provider = FallbackDataProvider(config, test_queue)
    provider.start_streams(["5m"])
    
    # Monitor for 30 seconds
    for _ in range(30):
        try:
            message = test_queue.get(timeout=1)
            print(f"Received: {message[0]} from {message[2]}")
        except Empty:
            pass
        
        status = provider.get_status()
        print(f"Status: {status}")