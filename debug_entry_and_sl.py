#!/usr/bin/env python3

import pandas as pd
import numpy as np
from datetime import datetime, timezone

# Load and analyze the specific problematic trade
print("=== DEBUG ENTRY AND SL CALCULATION ===")
print("Analyzing the specific SL exit anomaly...")

# Read trading simulation log data
# We need to find the entry for this specific SHORT position that exited at 01:17:43

# Simulate the key parameters that would have been used
# Based on the simulation log:
print("\nFrom simulation log:")
print("EXIT 347.5110 u S @ 2.15730 (R: SL, Trig: 2.15666)")
print("PnL: 3.02")

# Reverse engineer the entry price
position_size = 347.5110
exit_price = 2.15730
position = -1  # SHORT
reported_pnl = 3.02

# PnL formula: pnl = position_size * (exit_price - entry_price) * position
# For SHORT: pnl = position_size * (exit_price - entry_price) * (-1)
# Solve for entry_price:
# reported_pnl = position_size * (exit_price - entry_price) * (-1)
# reported_pnl = -position_size * (exit_price - entry_price)
# reported_pnl = -position_size * exit_price + position_size * entry_price
# position_size * entry_price = reported_pnl + position_size * exit_price
# entry_price = (reported_pnl + position_size * exit_price) / position_size

calculated_entry_price = (reported_pnl + position_size * exit_price) / position_size
print(f"\nReverse-engineered entry price: {calculated_entry_price:.5f}")

# Verify the calculation
verify_pnl = position_size * (exit_price - calculated_entry_price) * position
print(f"Verification PnL: {verify_pnl:.2f} (should match {reported_pnl:.2f})")

# Now analyze the SL logic
sl_trigger = 2.15666
print(f"\nSL Analysis:")
print(f"Entry price: {calculated_entry_price:.5f}")
print(f"SL trigger:  {sl_trigger:.5f}")
print(f"Exit price:  {exit_price:.5f}")

# For SHORT position, SL should be ABOVE entry
sl_distance_from_entry = sl_trigger - calculated_entry_price
print(f"\nSL distance from entry: {sl_distance_from_entry:.5f}")
if sl_distance_from_entry > 0:
    print("✅ SL is ABOVE entry (correct for SHORT)")
else:
    print("❌ SL is BELOW entry (WRONG for SHORT)")

# Check the price movement
price_movement = exit_price - calculated_entry_price
print(f"\nPrice movement from entry to exit: {price_movement:.5f}")
if price_movement > 0:
    print("📈 Price went UP from entry to exit")
    print("For SHORT: Price UP = LOSS expected")
else:
    print("📉 Price went DOWN from entry to exit")
    print("For SHORT: Price DOWN = GAIN expected")

# Check if the PnL sign is correct
if position == -1:  # SHORT
    if price_movement > 0:  # Price went up
        expected_pnl_sign = "NEGATIVE (loss)"
    else:  # Price went down
        expected_pnl_sign = "POSITIVE (gain)"
else:  # LONG
    if price_movement > 0:  # Price went up
        expected_pnl_sign = "POSITIVE (gain)"
    else:  # Price went down
        expected_pnl_sign = "NEGATIVE (loss)"

actual_pnl_sign = "POSITIVE" if reported_pnl > 0 else "NEGATIVE"
print(f"\nPnL Analysis:")
print(f"Expected PnL sign: {expected_pnl_sign}")
print(f"Actual PnL sign:   {actual_pnl_sign}")
print(f"Actual PnL value:  ${reported_pnl:.2f}")

if expected_pnl_sign.startswith(actual_pnl_sign):
    print("✅ PnL sign is CORRECT")
else:
    print("❌ PnL sign is WRONG")

# Simulate correct SL placement for comparison
print(f"\n=== CORRECT SL SIMULATION ===")
# Assume typical parameters
min_sl_distance_percent = 0.02  # 2%
min_sl_distance_points = calculated_entry_price * min_sl_distance_percent

correct_sl_for_short = calculated_entry_price + min_sl_distance_points
print(f"Correct SL for SHORT should be: {correct_sl_for_short:.5f}")
print(f"Actual SL trigger was:         {sl_trigger:.5f}")
print(f"Difference: {abs(correct_sl_for_short - sl_trigger):.5f}")

if abs(correct_sl_for_short - sl_trigger) < 0.0001:
    print("✅ SL placement seems mathematically correct")
else:
    print("❌ SL placement is mathematically incorrect")

print(f"\n=== CONCLUSION ===")
if sl_distance_from_entry < 0:
    print("🚨 PRIMARY ISSUE: SL is placed BELOW entry for SHORT position")
    print("   This violates basic trading logic for SHORT positions")
    print("   Fix: Ensure SL is placed ABOVE entry for SHORT positions")
elif expected_pnl_sign.startswith(actual_pnl_sign):
    print("🤔 MYSTERY: SL placement seems correct, PnL sign seems correct")
    print("   Need to investigate further - possibly a different issue")
else:
    print("🚨 PRIMARY ISSUE: PnL calculation has wrong sign")
    print("   Fix: Check PnL formula in simulate_trading_new.py")