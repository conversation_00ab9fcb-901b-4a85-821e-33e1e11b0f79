#!/usr/bin/env python3
"""
Skript na kontrolu vypočítaných features v parquet súboroch.
"""

import pandas as pd
import sys

def check_features(file_path):
    """Skontroluje obsah parquet súboru s features."""
    try:
        # Načítanie súboru
        df = pd.read_parquet(file_path)
        
        print(f"📊 Súbor: {file_path}")
        print(f"📈 Počet riadkov: {len(df)}")
        print(f"📋 Počet stĺpcov: {len(df.columns)}")
        print()
        
        # Kontrola kľúčových indikátorov
        key_indicators = ['ADX_14', 'DMP_14', 'DMN_14', 'RSI_14', 'ATR_14', 'EMA_9', 'EMA_21']
        print('🔍 Kľúčové indikátory:')
        for indicator in key_indicators:
            if indicator in df.columns:
                valid_count = df[indicator].notna().sum()
                print(f'  ✅ {indicator}: {valid_count}/{len(df)} hodnôt ({valid_count/len(df)*100:.1f}%)')
            else:
                print(f'  ❌ {indicator}: CHÝBA')
        
        print()
        print('📋 Všetky stĺpce:')
        for i, col in enumerate(df.columns):
            print(f'  {i+1:2d}. {col}')
        
        print()
        print('📊 Prvých 5 riadkov kľúčových indikátorov:')
        if all(ind in df.columns for ind in key_indicators):
            print(df[key_indicators].head())
        else:
            print('Niektoré indikátory chýbajú')
            
        print()
        print('📊 Posledných 5 riadkov kľúčových indikátorov:')
        if all(ind in df.columns for ind in key_indicators):
            print(df[key_indicators].tail())
        else:
            print('Niektoré indikátory chýbajú')
            
        return True
        
    except Exception as e:
        print(f"❌ Chyba pri načítaní súboru {file_path}: {e}")
        return False

if __name__ == "__main__":
    if len(sys.argv) > 1:
        file_path = sys.argv[1]
    else:
        file_path = "parquet_processed/XRPUSDC/1s/2025-04-01.parquet"
    
    check_features(file_path)
