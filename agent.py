from __future__ import annotations
import json, logging, os, sys, time, multiprocessing as mp, copy, traceback
from contextlib import contextmanager
from datetime import datetime, timedelta, timezone
from pathlib import Path
from typing import List, Dict, Any, Callable

for v in ("OMP_NUM_THREADS", "OPENBLAS_NUM_THREADS",
          "MKL_NUM_THREADS", "NUMEXPR_NUM_THREADS"):
    os.environ[v] = "1"

import torch
torch.set_num_threads(1)  # Will be overridden later for main process
torch.set_num_interop_threads(1)

import numpy as np
import pandas as pd
import torch.nn as nn
import torch.nn.utils as nn_utils
from tqdm.auto import tqdm

from stable_baselines3 import SAC
from stable_baselines3.common.callbacks import (
    BaseCallback, CheckpointCallback, EvalCallback
)
from callbacks import (
    EpochGateCallback,
    PerformanceBasedRefresh,
    DynamicEntropyCallback,
    DynamicGradientStepsCallback,
    PERBetaCallback,
    EntropyFloorCallback
)
from trade_logger import TradeLoggerCallback
from stable_baselines3.common.env_util import make_vec_env
from stable_baselines3.common.torch_layers import BaseFeaturesExtractor
from stable_baselines3.common.vec_env import DummyVecEnv, SubprocVecEnv, VecNormalize
from stable_baselines3.common.buffers import ReplayBuffer
from popart_sac_policy import PopArtSACPolicy
from popart_sac import PopArtSAC

try:
    from stable_baselines3.common.vec_env import ShmemVecEnv as VecEnvCls
except ImportError:
    VecEnvCls = SubprocVecEnv

from scalping_env import ScalpingEnv

try:
    from trade_logger import TradeLoggerCallback
    TRADE_LOGGER_HAS_APPEND = True
except (ImportError, TypeError):
    try:
        from trade_logger_compat import TradeLoggerCallback
        TRADE_LOGGER_HAS_APPEND = False
        logging.info("Using compatibility version of TradeLoggerCallback without append parameter")
    except ImportError:
        logging.error("Failed to import TradeLoggerCallback from either module")
        raise

HAVE_PER = False
try:
    from sb3_contrib.common.replay_buffer import PrioritizedReplayBuffer
    HAVE_PER = True
except ModuleNotFoundError:
    try:
        from sb3_contrib.common.buffers import PrioritizedReplayBuffer
        HAVE_PER = True
    except ModuleNotFoundError:
        print("sb3-contrib missing – using normal ReplayBuffer")

class SafeReplayBuffer(ReplayBuffer):
    def __init__(self,*a,**kw):
        kw["handle_timeout_termination"]=False
        super().__init__(*a,**kw)

if HAVE_PER:
    class SafePrioritizedReplayBuffer(PrioritizedReplayBuffer):
        def __init__(self,*a,**kw):
            kw["handle_timeout_termination"]=False
            kw.setdefault("n_step",5)
            super().__init__(*a,**kw)

        def _sample_proportional(self, batch_size):
            indices = []
            p_total = self._it_sum.sum()
            segment = p_total / batch_size

            for i in range(batch_size):
                mass = np.random.random() * segment + i * segment
                idx = self._it_sum.find_prefixsum_idx(mass)
                indices.append(idx)

            return indices

        def update_priorities(self, idxes, priorities):
            weighted_priorities = np.abs(priorities) + 1.0
            super().update_priorities(idxes, weighted_priorities)

LOG_FILE = "training.log"
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s %(levelname)-5s %(message)s",
    handlers=[logging.StreamHandler(sys.stdout), logging.FileHandler(LOG_FILE, "w")],
)

@contextmanager
def phase(name: str):
    logging.info("▶ %s", name)
    t0 = time.perf_counter()
    yield
    logging.info("✔ %s (%.1fs)", name, time.perf_counter() - t0)

mp.set_start_method("forkserver", force=True)

import multiprocessing
NUM_CPU_TOTAL = multiprocessing.cpu_count()
NUM_ENVS = 64  # Reduced from 256 to 64 for better efficiency
MAIN_TORCH_THREADS = max(1, NUM_CPU_TOTAL - NUM_ENVS)
torch.set_num_threads(MAIN_TORCH_THREADS)

for v in ("OMP_NUM_THREADS", "OPENBLAS_NUM_THREADS",
          "MKL_NUM_THREADS", "NUMEXPR_NUM_THREADS"):
    os.environ[v] = str(MAIN_TORCH_THREADS)

def _dt(s:str)->datetime:
    return datetime.fromisoformat(s.replace("Z","+00:00")).astimezone(timezone.utc)

def linear_lr(a:float,b:float)->Callable[[float],float]:
    return lambda p: b + (a-b)*p

def _find_parquet_dir(root: Path, symbol: str, tf: str) -> Path | None:
    """
    Vráti prvý existujúci adresár so súbormi TF.
      root/{symbol}/{tf}
      root/{symbol}/ohlcv/{tf}
    """
    candidates = [
        root / symbol / tf,
        root / symbol / "ohlcv" / tf,
    ]
    for c in candidates:
        if c.exists():
            logging.info("Parquet dir: %s", c)
            return c
    logging.error("Parquet dir NOT found. Tried:\n  %s",
                  "\n  ".join(map(str, candidates)))
    return None

def prepare_df(cfg, start, end):
    symbol = cfg["symbol"]
    tf     = cfg["primaryTimeframe"]

    root = Path(os.environ.get("PARQUET_DIR", cfg.get("featureParquetDir", "parquet"))).expanduser()
    tf_dir = next((d for d in (root/symbol/tf, root/symbol/"ohlcv"/tf) if d.exists()), None)
    if tf_dir is None:
        raise FileNotFoundError(f"No parquet dir under {root}")

    warm = start - timedelta(hours=24)

    from joblib import Parallel, delayed

    def load_single_day(fp):
        if fp.exists():
            df = pd.read_parquet(fp, engine="pyarrow")
            if "timestamp" in df.columns:
                df.set_index("timestamp", inplace=True)
            df.index = pd.to_datetime(df.index, utc=True)
            return df
        return None

    file_paths = []
    cur = warm.date()
    while cur <= end.date():
        file_paths.append(tf_dir / f"{cur}.parquet")
        cur += timedelta(days=1)

    dfs = Parallel(n_jobs=8)(delayed(load_single_day)(fp) for fp in file_paths)

    dfs_filtered = [df for df in dfs if df is not None]
    if not dfs_filtered:
        raise RuntimeError("no parquet files in range")

    df_all = pd.concat(dfs_filtered).sort_index().loc[warm:end]
    feats  = cfg["envSettings"]["feature_columns"]

    missing = [c for c in feats if c not in df_all.columns]
    if missing:
        logging.warning("Missing %d columns → filled with zeros: %s",
                        len(missing), ", ".join(missing))
        df_all = df_all.copy()
        for col in missing:
            df_all[col] = 0.0

    df_all = df_all.copy()
    df_all[feats] = df_all[feats].ffill()

    vol_cols = [c for c in feats if "vol" in c]
    df_all[vol_cols] = np.log1p(df_all[vol_cols])

    df_all.replace([np.inf, -np.inf], np.nan, inplace=True)
    df_all = df_all[np.isfinite(df_all[feats]).all(axis=1)]
    return df_all.loc[start:end, feats].astype("float32")

class CheckpointWithVecNormalizeCallback(BaseCallback):
    def __init__(self, save_freq: int, save_path: str, name_prefix: str, venv, verbose: int = 0):
        super().__init__(verbose)
        self.save_freq = save_freq
        self.save_path = Path(save_path)
        self.name_prefix = name_prefix
        self.venv = venv
        self.save_path.mkdir(parents=True, exist_ok=True)
    
    def _on_step(self) -> bool:
        if self.n_calls % self.save_freq == 0:
            # Save model
            model_path = self.save_path / f"{self.name_prefix}_{self.num_timesteps}_steps.zip"
            self.model.save(model_path)
            
            # Save VecNormalize
            vecnorm_path = self.save_path / f"{self.name_prefix}_{self.num_timesteps}.vecnorm.pkl"
            self.venv.save(vecnorm_path)
            
            logging.info(f"Checkpoint saved: {model_path} & {vecnorm_path}")
        return True

class ClipCallback(BaseCallback):
    def __init__(self,max_norm:float): super().__init__(); self.max=max_norm
    def _on_step(self): nn_utils.clip_grad_norm_(self.model.policy.parameters(),self.max); return True

class TqdmCallback(BaseCallback):
    def __init__(self,total:int,update:int=8_000):
        super().__init__(); self.tot=total; self.upd=update; self.last=0
    def _on_training_start(self): self.pb=tqdm(total=self.tot,desc="training",unit="ts")
    def _on_step(self):
        if self.num_timesteps-self.last>=self.upd:
            self.pb.update(self.num_timesteps-self.last); self.last=self.num_timesteps
        return True
    def _on_training_end(self): self.pb.update(self.num_timesteps-self.last); self.pb.close()


class ResidualBlock(nn.Module):
    def __init__(self, channels, kernel_size, dropout_rate=0.1):
        super().__init__()
        self.conv1 = nn.Conv1d(channels, channels, kernel_size=kernel_size, padding="same")
        self.bn1 = nn.BatchNorm1d(channels)
        self.relu1 = nn.ReLU()
        self.dropout1 = nn.Dropout(dropout_rate)
        self.conv2 = nn.Conv1d(channels, channels, kernel_size=kernel_size, padding="same")
        self.bn2 = nn.BatchNorm1d(channels)
        self.relu2 = nn.ReLU()
        self.dropout2 = nn.Dropout(dropout_rate)

    def forward(self, x):
        residual = x
        out = self.conv1(x)
        out = self.bn1(out)
        out = self.relu1(out)
        out = self.dropout1(out)
        out = self.conv2(out)
        out = self.bn2(out)
        out += residual
        out = self.relu2(out)
        out = self.dropout2(out)
        return out

class SimpleCNN1D(BaseFeaturesExtractor):
    def __init__(self, obs_space, *, lookback:int, n_filters:int, kernel:int, out_dim:int, feature_names=None, meta_len:int=2):
        flat=obs_space.shape[0]; n_feat=(flat-meta_len)//lookback
        super().__init__(obs_space,out_dim)
        self.look,self.n_feat,self.meta=lookback,n_feat,meta_len
        self.feature_names = feature_names

        self.conv_in = nn.Conv1d(n_feat, n_filters, kernel_size=kernel, padding="same")
        self.relu_in = nn.ReLU()
        self.res_block1 = ResidualBlock(n_filters, kernel)
        self.res_block2 = ResidualBlock(n_filters, kernel)
        self.pool = nn.AdaptiveAvgPool1d(1)
        self.book_tilt_embed = nn.Embedding(10, 16)
        self.layer_norm = nn.LayerNorm(n_filters)  # Add layer norm after CNN
        self.head = nn.Sequential(
            nn.Flatten(),
            nn.Linear(n_filters+meta_len+16, out_dim*2),
            nn.BatchNorm1d(out_dim*2),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(out_dim*2, out_dim),
            nn.ReLU()
        )

    def forward(self,x):
        seq,rest=x[:,:-self.meta],x[:,-self.meta:]
        seq=seq.view(-1,self.look,self.n_feat).permute(0,2,1)

        x = self.relu_in(self.conv_in(seq))
        x = self.res_block1(x)
        x = self.res_block2(x)
        cnn_out = self.pool(x).squeeze(-1)
        cnn_out = self.layer_norm(cnn_out)  # Apply layer norm after pooling

        last_step = seq[:, :, -1]
        bid_vols = []
        ask_vols = []

        if self.feature_names:
            bid_vols_indices = [i for i, name in enumerate(self.feature_names)
                              if name.startswith("ob_bid_vol_l")]
            ask_vols_indices = [i for i, name in enumerate(self.feature_names)
                              if name.startswith("ob_ask_vol_l")]
            bid_vols = sorted(bid_vols_indices)[:5]
            ask_vols = sorted(ask_vols_indices)[:5]

        if not bid_vols or not ask_vols:
            logging.warning("OB columns not found by name, using fallback indices")
            num_book_levels = 5
            ask_start_index = self.n_feat - num_book_levels
            bid_start_index = self.n_feat - 2 * num_book_levels

            if bid_start_index >= 0:
                bid_vols = list(range(bid_start_index, bid_start_index + num_book_levels))
                ask_vols = list(range(ask_start_index, ask_start_index + num_book_levels))
            else:
                bid_vols = []
                ask_vols = []

        if bid_vols and ask_vols:
            vol_values_bid = last_step[:, bid_vols]
            vol_values_ask = last_step[:, ask_vols]
            all_vol_values = torch.cat([vol_values_bid, vol_values_ask], dim=1)
            book_tilt = torch.argmax(all_vol_values, dim=1)
            book_tilt = torch.clamp(book_tilt, 0, 9)
            tilt_embedding = self.book_tilt_embed(book_tilt)
        else:
            tilt_embedding = self.book_tilt_embed(torch.zeros(seq.size(0), dtype=torch.long, device=seq.device))

        return self.head(torch.cat([cnn_out, rest, tilt_embedding], 1))

def main() -> None:
    import argparse
    parser = argparse.ArgumentParser()
    parser.add_argument("--config", default="strategyConfig_scalp.json", help="Config file path")
    args = parser.parse_args()
    
    cfg = json.loads(Path(args.config).read_text())

    start = _dt(cfg["backtestSettings"]["startTime"])
    end = _dt(cfg["backtestSettings"]["endTime"])

    with phase("load data"):
        df = prepare_df(cfg, start, end)  # type: ignore – function defined above

    split = int(len(df) * cfg["trainingSettings"]["trainSplitRatio"])
    df_tr, df_val = df.iloc[:split], df.iloc[split:]
    logging.info("train=%d  val=%d", len(df_tr), len(df_val))

    from stable_baselines3.common.vec_env import SubprocVecEnv as VecEnvCls  # late import

    with phase("env"):
        global NUM_ENVS
        NUM_ENVS = cfg["trainingSettings"]["n_envs"]

        MAIN_TORCH_THREADS = max(1, NUM_CPU_TOTAL - NUM_ENVS)
        torch.set_num_threads(MAIN_TORCH_THREADS)
        logging.info(f"Using {NUM_ENVS} environments with {MAIN_TORCH_THREADS} threads for main process")

        venv = make_vec_env(
            lambda: ScalpingEnv(df_tr, cfg),
            n_envs=NUM_ENVS,
            vec_env_cls=VecEnvCls,
            seed=cfg["trainingSettings"]["seed"],
        )
        venv = VecNormalize(venv, norm_reward=False, norm_obs=True, clip_reward=50.0, training=True)

        val_env = DummyVecEnv([lambda: ScalpingEnv(df_val, cfg)])
        val_env = VecNormalize(val_env, norm_reward=False, norm_obs=True, clip_reward=50.0, training=False)

        val_env.norm_obs = venv.norm_obs
        val_env.norm_reward = venv.norm_reward
        val_env.clip_obs = venv.clip_obs
        val_env.clip_reward = venv.clip_reward
        val_env.obs_rms = copy.deepcopy(venv.obs_rms)
        val_env.ret_rms = copy.deepcopy(venv.ret_rms)

    total_ts = cfg["trainingSettings"]["totalTimesteps"]

    def sgdr_lr(initial_lr=1e-3, min_lr=2e-4, warmup_steps=50_000,
                initial_restart_period=750_000, restart_mult=2, total_timesteps=None):
        """
        SGDR (Stochastic Gradient Descent with Warm Restarts) learning rate scheduler.

        Args:
            initial_lr: Initial learning rate after warmup
            min_lr: Minimum learning rate
            warmup_steps: Number of steps for linear warmup
            initial_restart_period: Initial period for cosine annealing cycle
            restart_mult: Multiplier for restart period after each cycle
            total_timesteps: Total number of timesteps for training

        Returns:
            Learning rate schedule function that takes progress_remaining as input
        """
        if total_timesteps is None:
            total_timesteps = total_ts

        def schedule(progress_remaining: float) -> float:
            current_step = int((1.0 - progress_remaining) * total_timesteps)

            if current_step < warmup_steps:
                return min_lr + (initial_lr - min_lr) * (current_step / warmup_steps)

            cycle_step = current_step - warmup_steps

            cycle = 0
            cycle_start = 0
            period = initial_restart_period

            while cycle_start + period <= cycle_step:
                cycle_start += period
                cycle += 1
                period *= restart_mult

            cycle_progress = (cycle_step - cycle_start) / period
            cosine_decay = 0.5 * (1 + np.cos(np.pi * cycle_progress))

            lr = min_lr + (initial_lr - min_lr) * cosine_decay

            if cycle_step > 0 and (cycle_step - cycle_start) < 1000:
                logging.info(f"SGDR: Cycle {cycle+1}, period={period}, lr={lr:.6f}")

            return lr

        return schedule

    # Linear learning rate schedule instead of SGDR
    def linear_lr_schedule(initial_lr=3e-4, final_lr=5e-5):
        """Linear decay from initial_lr to final_lr over total training"""
        def schedule(progress_remaining: float) -> float:
            # progress_remaining goes from 1.0 to 0.0
            # We want lr to go from initial_lr to final_lr
            progress_done = 1.0 - progress_remaining
            lr = initial_lr - (initial_lr - final_lr) * progress_done
            return lr
        return schedule
    
    lr_schedule = linear_lr_schedule(initial_lr=3e-4, final_lr=5e-5)
    logging.info("Using linear LR decay: 3e-4 -> 5e-5 over %d timesteps", total_ts)

    feats = cfg["envSettings"]["feature_columns"]

    replay_buffer_cls = SafePrioritizedReplayBuffer if HAVE_PER else SafeReplayBuffer

    model_kw: Dict[str, Any] = dict(
        policy=PopArtSACPolicy,
        env=venv,
        learning_rate=lr_schedule,
        buffer_size=cfg["trainingSettings"]["bufferSize"],
        learning_starts=cfg["trainingSettings"]["learningStarts"],
        batch_size=cfg["trainingSettings"]["batchSize"],
        tau=cfg["trainingSettings"]["sacParams"]["tau"],
        gamma=cfg["trainingSettings"]["sacParams"]["gamma"],
        train_freq=tuple(cfg["trainingSettings"]["sacParams"]["train_freq"]),
        gradient_steps=cfg["trainingSettings"]["sacParams"]["gradient_steps"],
        ent_coef=cfg["trainingSettings"]["sacParams"]["ent_coef"],
        target_entropy=-2.4,  # Updated for declining entropy setup
        target_update_interval=cfg["trainingSettings"]["sacParams"]["target_update_interval"],
        seed=cfg["trainingSettings"]["seed"],
        verbose=1,
        tensorboard_log=cfg["trainingSettings"]["logDir"],
        device="auto",  # Let it choose GPU if available
        optimize_memory_usage=True,
        policy_kwargs=dict(
            net_arch=cfg["trainingSettings"]["netArch"],
            features_extractor_class=SimpleCNN1D,
            features_extractor_kwargs={
                **cfg["trainingSettings"]["featureExtractorKwargs"],
                "feature_names": feats,
                "meta_len": 11,  # 7 original + 4 time features
            },
        ),
        replay_buffer_class=replay_buffer_cls,
    )

    if HAVE_PER:
        model_kw.update({
            "replay_buffer_kwargs": {
                "alpha": 0.6,
                "beta_start": 0.4,
                "beta_schedule": "linear",
                "prioritize_newest": True,  # Ensure recent data gets sampled
                "n_step": 5,
                "loss_penalty": 0.2  # Down-weight trades with small |net|
            }
        })

    from stable_baselines3.common.vec_env import VecNormalize as VN

    with phase("model"):
        model: PopArtSAC = PopArtSAC(**model_kw)

    class EquityLoggerCallback(BaseCallback):
        def __init__(self, verbose=0):
            super().__init__(verbose)
            self.equity_values = []
            self.prev_equity = {}
            self.max_equity = {}
            self.log_freq = 1000
            self.episode_starts = {}
            # Tracking high-DD environments
            self.high_dd_envs = set()
            self.dd_warning_threshold = 2.0  # 2% DD threshold
            self.last_dd_check = 0

        def _on_step(self) -> bool:
            if self.n_calls % self.log_freq == 0:
                equity_diffs = []
                drawdowns = []
                current_high_dd_envs = []

                for env_idx in range(self.training_env.num_envs):
                    try:
                        eq = self.training_env.get_attr("equity_total", indices=[env_idx])[0]
                        max_eq = self.training_env.get_attr("max_equity", indices=[env_idx])[0]
                        prev_max = self.max_equity.get(env_idx, 0)
                        if max_eq < prev_max * 0.9:
                            self.episode_starts[env_idx] = self.n_calls
                            self.prev_equity[env_idx] = eq
                        self.logger.record(f"custom/equity_env_{env_idx}", eq)
                        self.logger.record(f"custom/max_equity_env_{env_idx}", max_eq)
                        self.max_equity[env_idx] = max_eq
                        if max_eq > 0:
                            drawdown = (max_eq - eq) / max_eq * 100
                            drawdowns.append(drawdown)
                            self.logger.record(f"custom/drawdown_env_{env_idx}", drawdown)
                            
                            # Sledovanie high-DD environment-ov
                            if drawdown > self.dd_warning_threshold:
                                current_high_dd_envs.append(env_idx)
                                if env_idx not in self.high_dd_envs:
                                    self.high_dd_envs.add(env_idx)
                                    logging.warning(f"High DD Alert: Environment {env_idx} reached {drawdown:.2f}% drawdown")
                                    
                        episode_start = self.episode_starts.get(env_idx, 0)
                        if env_idx in self.prev_equity and (self.n_calls - episode_start) > self.log_freq:
                            prev_eq = self.prev_equity[env_idx]
                            diff = eq - prev_eq
                            equity_diffs.append(diff)
                        self.prev_equity[env_idx] = eq
                    except (AttributeError, IndexError, ValueError):
                        pass
                        
                # Log problematic environments every 50k timesteps
                if self.n_calls % 50000 == 0 and self.high_dd_envs:
                    logging.info(f"Environments with >2% DD (total {len(self.high_dd_envs)}): {sorted(list(self.high_dd_envs))}")
                    self.logger.record("custom/high_dd_env_count", len(self.high_dd_envs))
                if equity_diffs:
                    self.logger.record("custom/equity_diff_mean_full", np.mean(equity_diffs))
                if drawdowns:
                    self.logger.record("custom/drawdown_mean", np.mean(drawdowns))
                if hasattr(self.model, 'log_alpha'):
                    alpha = torch.exp(self.model.log_alpha).item()
                    self.logger.record("train/alpha", alpha)
                elif hasattr(self.model, 'ent_coef_tensor'):
                    self.logger.record("train/alpha", self.model.ent_coef_tensor.item())
                if hasattr(self.model, '_pop_layers'):
                    pop_layers = self.model._pop_layers
                    for i, pop_layer in enumerate(pop_layers):
                        if hasattr(pop_layer, 'mean') and hasattr(pop_layer, 'std'):
                            self.logger.record(f"popart/mean_q{i+1}", pop_layer.mean.mean().item())
                            self.logger.record(f"popart/std_q{i+1}", pop_layer.std.mean().item())
            return True

    class ValidationEarlyStoppingCallback(BaseCallback):
        """
        Advanced early stopping based on validation performance with trading-specific metrics.
        """
        def __init__(self, validation_env, check_freq: int = 50000,
                     patience: int = 3, min_delta: float = 0.01,
                     metric: str = "win_rate", verbose: int = 1):
            super().__init__(verbose)
            self.validation_env = validation_env
            self.check_freq = check_freq
            self.patience = patience
            self.min_delta = min_delta
            self.metric = metric  # "win_rate", "profit_factor", "sharpe"
            self.best_metric = -np.inf
            self.patience_counter = 0
            self.should_stop = False
            self.validation_history = []
            
        def _on_step(self) -> bool:
            if self.n_calls % self.check_freq == 0:
                # Run validation
                n_episodes = 10
                episode_rewards = []
                trades_data = []
                
                for _ in range(n_episodes):
                    obs = self.validation_env.reset()
                    done = False
                    episode_reward = 0
                    
                    while not done:
                        action, _ = self.model.predict(obs, deterministic=True)
                        obs, reward, done, info = self.validation_env.step(action)
                        episode_reward += reward[0]
                    
                    episode_rewards.append(episode_reward)
                    # Collect trading metrics from environment
                    if hasattr(self.validation_env.envs[0], 'trades'):
                        trades_data.extend(self.validation_env.envs[0].trades)
                
                # Calculate trading metrics
                if trades_data:
                    df_trades = pd.DataFrame(trades_data)
                    winning_trades = len(df_trades[df_trades['pnl'] > 0])
                    total_trades = len(df_trades)
                    win_rate = winning_trades / total_trades if total_trades > 0 else 0
                    
                    profit_trades = df_trades[df_trades['pnl'] > 0]['pnl'].sum()
                    loss_trades = abs(df_trades[df_trades['pnl'] < 0]['pnl'].sum())
                    profit_factor = profit_trades / loss_trades if loss_trades > 0 else float('inf')
                    
                    total_pnl = df_trades['pnl'].sum()
                    pnl_std = df_trades['pnl'].std()
                    sharpe = total_pnl / pnl_std if pnl_std > 0 else 0
                else:
                    win_rate = 0
                    profit_factor = 0
                    sharpe = 0
                
                # Select metric
                if self.metric == "win_rate":
                    current_metric = win_rate
                elif self.metric == "profit_factor":
                    current_metric = profit_factor
                elif self.metric == "sharpe":
                    current_metric = sharpe
                else:
                    current_metric = np.mean(episode_rewards)
                
                self.validation_history.append({
                    'timestep': self.num_timesteps,
                    'win_rate': win_rate,
                    'profit_factor': profit_factor,
                    'sharpe': sharpe,
                    'mean_reward': np.mean(episode_rewards)
                })
                
                # Log metrics
                self.logger.record("validation/win_rate", win_rate)
                self.logger.record("validation/profit_factor", profit_factor)
                self.logger.record("validation/sharpe", sharpe)
                self.logger.record("validation/mean_reward", np.mean(episode_rewards))
                
                # Early stopping logic
                if current_metric > self.best_metric + self.min_delta:
                    self.best_metric = current_metric
                    self.patience_counter = 0
                    if self.verbose:
                        logging.info(f"New best validation {self.metric}: {current_metric:.4f} at {self.num_timesteps} steps")
                else:
                    self.patience_counter += 1
                    if self.verbose:
                        logging.info(f"Validation {self.metric}: {current_metric:.4f} (best: {self.best_metric:.4f}) - patience: {self.patience_counter}/{self.patience}")
                
                if self.patience_counter >= self.patience:
                    logging.warning(f"Early stopping triggered! No improvement in {self.metric} for {self.patience} evaluations")
                    self.should_stop = True
                    return False
                    
            return True

    class AdaptiveLearningRateCallback(BaseCallback):
        """
        Adaptively adjust learning rate based on validation performance.
        """
        def __init__(self, initial_lr: float = 3e-4, decay_factor: float = 0.8,
                     patience: int = 2, min_lr: float = 1e-6, verbose: int = 1):
            super().__init__(verbose)
            self.initial_lr = initial_lr
            self.decay_factor = decay_factor
            self.patience = patience
            self.min_lr = min_lr
            self.current_lr = initial_lr
            self.best_performance = -np.inf
            self.patience_counter = 0
            self.lr_schedule = []
            
        def _on_step(self) -> bool:
            # This works with ValidationEarlyStoppingCallback to adjust LR
            return True
            
        def adjust_lr(self, current_performance: float):
            """Called by ValidationEarlyStoppingCallback to adjust learning rate"""
            if current_performance > self.best_performance:
                self.best_performance = current_performance
                self.patience_counter = 0
            else:
                self.patience_counter += 1
                
                if self.patience_counter >= self.patience:
                    old_lr = self.current_lr
                    self.current_lr = max(self.current_lr * self.decay_factor, self.min_lr)
                    
                    if self.current_lr != old_lr:
                        # Adjust learning rate in the model
                        if hasattr(self.model, 'policy') and hasattr(self.model.policy, 'optimizer'):
                            for param_group in self.model.policy.optimizer.param_groups:
                                param_group['lr'] = self.current_lr
                        
                        self.lr_schedule.append({
                            'timestep': self.num_timesteps,
                            'old_lr': old_lr,
                            'new_lr': self.current_lr
                        })
                        
                        if self.verbose:
                            logging.info(f"Learning rate adjusted: {old_lr:.2e} -> {self.current_lr:.2e}")
                    
                    self.patience_counter = 0

    class DynamicDropoutCallback(BaseCallback):
        """
        Dynamically adjust dropout based on overtraining signals.
        """
        def __init__(self, base_dropout: float = 0.1, max_dropout: float = 0.5,
                     check_freq: int = 25000, verbose: int = 1):
            super().__init__(verbose)
            self.base_dropout = base_dropout
            self.max_dropout = max_dropout
            self.check_freq = check_freq
            self.current_dropout = base_dropout
            self.performance_history = []
            
        def _on_step(self) -> bool:
            if self.n_calls % self.check_freq == 0:
                # Check for overtraining signals
                if len(self.performance_history) >= 3:
                    recent_performance = self.performance_history[-3:]
                    if all(recent_performance[i] < recent_performance[i-1] for i in range(1, len(recent_performance))):
                        # Performance is declining - increase dropout
                        old_dropout = self.current_dropout
                        self.current_dropout = min(self.current_dropout * 1.2, self.max_dropout)
                        
                        if self.current_dropout != old_dropout and hasattr(self.model, 'policy'):
                            # Apply dropout to model if possible
                            if hasattr(self.model.policy, 'mlp_extractor'):
                                for module in self.model.policy.mlp_extractor.modules():
                                    if isinstance(module, torch.nn.Dropout):
                                        module.p = self.current_dropout
                            
                            if self.verbose:
                                logging.info(f"Dropout increased: {old_dropout:.3f} -> {self.current_dropout:.3f}")
            
            return True
            
        def update_performance(self, performance: float):
            """Called by other callbacks to update performance history"""
            self.performance_history.append(performance)
            if len(self.performance_history) > 10:
                self.performance_history.pop(0)

    class ModelEnsembleCallback(BaseCallback):
        """
        Maintain ensemble of best models to prevent overtraining.
        """
        def __init__(self, save_freq: int = 100000, max_models: int = 5, verbose: int = 1):
            super().__init__(verbose)
            self.save_freq = save_freq
            self.max_models = max_models
            self.model_performances = []
            self.model_paths = []
            
        def _on_step(self) -> bool:
            if self.n_calls % self.save_freq == 0:
                # Save current model state
                model_path = f"ensemble_model_{self.num_timesteps}.zip"
                self.model.save(model_path)
                
                # This would be updated by ValidationEarlyStoppingCallback
                current_performance = getattr(self, 'current_performance', 0)
                
                self.model_paths.append(model_path)
                self.model_performances.append(current_performance)
                
                # Keep only best models
                if len(self.model_paths) > self.max_models:
                    # Remove worst performing model
                    worst_idx = np.argmin(self.model_performances)
                    removed_path = self.model_paths.pop(worst_idx)
                    self.model_performances.pop(worst_idx)
                    
                    # Clean up file
                    if os.path.exists(removed_path):
                        os.remove(removed_path)
                        
                    if self.verbose:
                        logging.info(f"Ensemble updated: removed {removed_path}")
                        
            return True

    class AntiOvertrainingCoordinator(BaseCallback):
        """
        Coordinates communication between anti-overtraining callbacks.
        """
        def __init__(self, validation_callback, adaptive_lr_callback, dropout_callback, ensemble_callback, verbose: int = 1):
            super().__init__(verbose)
            self.validation_callback = validation_callback
            self.adaptive_lr_callback = adaptive_lr_callback
            self.dropout_callback = dropout_callback
            self.ensemble_callback = ensemble_callback
            
        def _on_step(self) -> bool:
            # Check if validation callback has new results
            if hasattr(self.validation_callback, 'validation_history') and self.validation_callback.validation_history:
                latest_result = self.validation_callback.validation_history[-1]
                current_performance = latest_result.get('win_rate', 0)
                
                # Update adaptive learning rate callback
                if hasattr(self.adaptive_lr_callback, 'adjust_lr'):
                    self.adaptive_lr_callback.adjust_lr(current_performance)
                
                # Update dropout callback
                if hasattr(self.dropout_callback, 'update_performance'):
                    self.dropout_callback.update_performance(current_performance)
                
                # Update ensemble callback with current performance
                if hasattr(self.ensemble_callback, 'current_performance'):
                    self.ensemble_callback.current_performance = current_performance
            
            # Check if we should stop training
            if hasattr(self.validation_callback, 'should_stop') and self.validation_callback.should_stop:
                if self.verbose:
                    logging.warning("AntiOvertrainingCoordinator: Training will be stopped due to overtraining prevention")
                return False
                
            return True

    class ConsoleMonitorCallback(BaseCallback):
        """
        Live štatistiky v konzole (buffer, FPS, LR).
        Ktorý TqdmCallback má zdobiť určíš argumentom `pbar_cb`.
        """
        def __init__(self, pbar_cb: "TqdmCallback", every: int = 2_000, verbose: int = 0):
            super().__init__(verbose)
            self.pbar_cb = pbar_cb
            self.every   = every
            self._t0     = time.time()
            self._last_ts = 0

        def _format_stats(self) -> str:
            buf = getattr(self.model, "replay_buffer", None)
            if buf is None:
                fill, beta = 0.0, None
            else:
                fill = (getattr(buf, "next_idx", getattr(buf, "pos", 0))) / buf.buffer_size
                beta = getattr(buf, "beta", None)
            now  = time.time()
            fps  = (self.num_timesteps - self._last_ts) / max(now - self._t0, 1e-6)
            self._t0, self._last_ts = now, self.num_timesteps
            lr = None
            if hasattr(self.model, "lr_schedule"):
                prog = 1.0 - self.num_timesteps / self.model._total_timesteps
                lr = self.model.lr_schedule(prog)
            parts = [f"buf:{fill:5.1%}",
                    f"fps:{fps:6.0f}"]
            if lr is not None:   parts.append(f"lr:{lr:.2e}")
            if beta is not None: parts.append(f"β:{beta:.2f}")
            return " | ".join(parts)

        def _on_step(self) -> bool:
            if self.n_calls % self.every:
                return True

            txt = self._format_stats()
            pb = getattr(self.pbar_cb, "pb", None)
            if pb is not None:
                pb.set_postfix_str(txt, refresh=False)
            else:
                from tqdm import tqdm as _tqdm
                _tqdm.write(txt)
            return True
    tqdm_cb = TqdmCallback(cfg["trainingSettings"]["totalTimesteps"])

    def clone_vec_normalize_env(env):
        new_env = DummyVecEnv([lambda: ScalpingEnv(df_val, cfg)])
        new_env = VecNormalize(
            new_env,
            norm_obs=env.norm_obs,
            norm_reward=env.norm_reward,
            clip_obs=env.clip_obs,
            clip_reward=env.clip_reward,
            gamma=env.gamma,
            epsilon=env.epsilon,
            training=env.training
        )
        new_env.obs_rms = copy.deepcopy(env.obs_rms)
        new_env.ret_rms = copy.deepcopy(env.ret_rms)
        return new_env

    # Create separate validation environment for early stopping
    validation_env = clone_vec_normalize_env(val_env)
    
    # Initialize anti-overtraining callbacks
    validation_callback = ValidationEarlyStoppingCallback(
        validation_env=validation_env,
        check_freq=20000,  # Check every 20k steps (reduced from 50k)
        patience=4,        # Stop if no improvement for 4 evaluations
        min_delta=0.02,    # Minimum improvement threshold
        metric="profit_factor", # Primary metric to monitor
        verbose=1
    )
    
    adaptive_lr_callback = AdaptiveLearningRateCallback(
        initial_lr=1e-3,   # Match SGDR initial LR
        decay_factor=0.8,
        patience=2,
        min_lr=1e-6,
        verbose=1
    )
    
    dropout_callback = DynamicDropoutCallback(
        base_dropout=0.1,
        max_dropout=0.5,
        check_freq=25000,
        verbose=1
    )
    
    ensemble_callback = ModelEnsembleCallback(
        save_freq=100000,
        max_models=5,
        verbose=1
    )
    
    # Create coordinator to manage all anti-overtraining callbacks
    coordinator = AntiOvertrainingCoordinator(
        validation_callback=validation_callback,
        adaptive_lr_callback=adaptive_lr_callback,
        dropout_callback=dropout_callback,
        ensemble_callback=ensemble_callback,
        verbose=1
    )

    callbacks: List[BaseCallback] = [
        ClipCallback(0.2),
        CheckpointWithVecNormalizeCallback(
            save_freq=cfg["trainingSettings"]["evalFreq"] // cfg["trainingSettings"]["n_envs"],
            save_path="./chk",
            name_prefix="sac",
            venv=venv,
        ),
        tqdm_cb,
        ConsoleMonitorCallback(tqdm_cb, every=2_000),
        TradeLoggerCallback("trades.csv", window=500, initial_equity=cfg["account"]["initialEquity"],
                           **({"append": True} if TRADE_LOGGER_HAS_APPEND else {})),
        PerformanceBasedRefresh(patience=2, check_freq=1_000_000),
        DynamicGradientStepsCallback(base_steps=32, lr_threshold=5e-5, max_steps=128),  # Increased for later training phase
        PERBetaCallback(update_freq=10000, verbose=1) if HAVE_PER else None,
        EntropyFloorCallback(floor=0.02, decay_ts=1_000_000),  # Declining entropy setup
        DynamicEntropyCallback(start_entropy_coef=0.30, end_entropy_coef=0.01, decay_steps=total_ts, verbose=1),  # Extended entropy decay over full 15M timesteps with floor
        EpochGateCallback(noise_std_start=1e-4, noise_std_end=0, decay_ts=3_000_000),
        EquityLoggerCallback(),
        # ANTI-OVERTRAINING CALLBACKS
        validation_callback,    # Advanced early stopping based on trading metrics
        adaptive_lr_callback,   # Dynamic learning rate adjustment
        dropout_callback,       # Dynamic dropout adjustment
        ensemble_callback,      # Model ensemble maintenance
        coordinator,            # Coordinates all anti-overtraining mechanisms
        EvalCallback(
            clone_vec_normalize_env(val_env),
            best_model_save_path="./best_stochastic",
            log_path="./eval_logs_stochastic",
            eval_freq=cfg["trainingSettings"]["evalFreq"],
            n_eval_episodes=cfg["trainingSettings"]["evalEpisodes"] // 2,
            deterministic=False
        ),
        EvalCallback(
            val_env,
            best_model_save_path="./best_deterministic",
            log_path="./eval_logs_deterministic",
            eval_freq=cfg["trainingSettings"]["evalFreq"],
            n_eval_episodes=cfg["trainingSettings"]["evalEpisodes"] // 2,
            deterministic=True,
            callback_on_new_best=None  # Early stopping handled by ValidationEarlyStoppingCallback
        ),
    ]
    callbacks = [cb for cb in callbacks if cb is not None]

    with phase("train"):
        logging.info("🚀 Starting training with advanced anti-overtraining mechanisms:")
        logging.info("  ✓ ValidationEarlyStoppingCallback - monitors win_rate with patience=3")
        logging.info("  ✓ AdaptiveLearningRateCallback - adjusts LR on stagnation")
        logging.info("  ✓ DynamicDropoutCallback - increases dropout on overtraining")
        logging.info("  ✓ ModelEnsembleCallback - maintains best model ensemble")
        logging.info("  ✓ AntiOvertrainingCoordinator - manages callback communication")
        
        model.learn(total_ts, callback=callbacks, log_interval=400)

    with phase("save"):
        model_path = Path(cfg["trainingSettings"]["modelSavePath"])
        model_path.parent.mkdir(parents=True, exist_ok=True)
        if model_path.suffix != ".zip":
            model_path = model_path.with_suffix(".zip")

        model.save(model_path)
        vecnorm_path = model_path.with_suffix(".vecnorm.pkl")
        VN.save(venv, vecnorm_path)
        
        # Save comprehensive training summary with anti-overtraining results
        training_summary = {
            'model_path': str(model_path),
            'vecnorm_path': str(vecnorm_path),
            'final_timesteps': model.num_timesteps,
            'total_planned_timesteps': total_ts,
            'early_stopped': validation_callback.should_stop,
            'best_validation_win_rate': validation_callback.best_metric,
            'validation_history': validation_callback.validation_history,
            'lr_adjustments': getattr(adaptive_lr_callback, 'lr_schedule', []),
            'ensemble_models': getattr(ensemble_callback, 'model_paths', []),
            'final_dropout': getattr(dropout_callback, 'current_dropout', 0.1),
            'training_completed_at': datetime.now().isoformat()
        }
        
        summary_path = model_path.with_suffix('.training_summary.json')
        with open(summary_path, 'w') as f:
            json.dump(training_summary, f, indent=2, default=str)
        
        logging.info("📊 TRAINING COMPLETED SUCCESSFULLY!")
        logging.info("Model saved → %s", model_path)
        logging.info("VecNormalize saved → %s", vecnorm_path)
        logging.info("Training summary → %s", summary_path)
        
        if validation_callback.should_stop:
            logging.info("🛑 Training stopped early at %d/%d steps (%.1f%% complete)",
                        model.num_timesteps, total_ts,
                        100 * model.num_timesteps / total_ts)
            logging.info("🎯 Best validation win rate: %.4f", validation_callback.best_metric)
        else:
            logging.info("✅ Training completed full duration: %d steps", model.num_timesteps)
            
        if hasattr(adaptive_lr_callback, 'lr_schedule') and adaptive_lr_callback.lr_schedule:
            logging.info("📈 Learning rate was adjusted %d times", len(adaptive_lr_callback.lr_schedule))
            
        if hasattr(ensemble_callback, 'model_paths') and ensemble_callback.model_paths:
            logging.info("🏆 Ensemble contains %d best models", len(ensemble_callback.model_paths))

if __name__ == "__main__":
    main()