#!/usr/bin/env python3

import pandas as pd
import numpy as np
from datetime import datetime
import pytz

print("🔍 ANALÝZA OPRAVENÉHO SL EXITU")
print("="*60)

# Data z logu
exit_time = "2025-07-02 01:19:45+00:00"
exit_price = 2.16980
sl_trigger = 2.16915
pnl = -1.33
position_size = 347.5110
position = -1  # SHORT

print(f"Exit Time: {exit_time}")
print(f"Exit Price: {exit_price}")
print(f"SL Trigger: {sl_trigger}")
print(f"PnL: {pnl}")
print(f"Position Size: {position_size}")
print(f"Position Type: {'SHORT' if position == -1 else 'LONG'}")

print(f"\n🧮 VYPOČÍTANÁ ENTRY CENA Z PnL:")
# PnL = position_size * (exit_price - entry_price) * position
# -1.33 = 347.5110 * (2.16980 - entry_price) * (-1)
# -1.33 = 347.5110 * (entry_price - 2.16980)
# entry_price = 2.16980 - 1.33/347.5110

entry_price_calc = exit_price - (pnl / (position_size * position))
print(f"Entry Price (calculated): {entry_price_calc:.6f}")

print(f"\n✅ OVERENIE PnL KALKULÁCIE:")
pnl_check = position_size * (exit_price - entry_price_calc) * position
print(f"PnL check: {position_size:.4f} * ({exit_price:.5f} - {entry_price_calc:.6f}) * ({position}) = {pnl_check:.2f}")
print(f"PnL from log: {pnl}")
print(f"Match: {'✅' if abs(pnl_check - pnl) < 0.01 else '❌'}")

print(f"\n🎯 SL LOGIKA ANALÝZA:")
print(f"Entry Price: {entry_price_calc:.6f}")
print(f"SL Trigger: {sl_trigger:.5f}")
print(f"Exit Price: {exit_price:.5f}")

sl_distance = sl_trigger - entry_price_calc
print(f"\nSL distance from entry: {sl_distance:.6f}")
if position == -1:  # SHORT
    if sl_distance > 0:
        print("✅ SL je VYŠŠIE ako entry (SPRÁVNE pre SHORT)")
    else:
        print("❌ SL je NIŽŠIE ako entry (NESPRÁVNE pre SHORT)")

print(f"\n🤔 PREČO MÁ SHORT NEGATÍVNY PnL?")
print(f"Exit price ({exit_price:.5f}) vs Entry price ({entry_price_calc:.6f})")
price_change = exit_price - entry_price_calc
print(f"Price change: {price_change:.6f}")
if position == -1:  # SHORT
    if price_change > 0:
        print("✅ Cena STÚPLA → SHORT má STRATU (normálne)")
        print("✅ SL sa spustil správne (cena prešla nad SL)")
    else:
        print("❌ Cena KLESLA → SHORT by mal mať ZISK")

print(f"\n📊 OVERENIE Z 1S DÁT:")
try:
    # Load 1s data for verification
    df_1s = pd.read_parquet('/Users/<USER>/Projects/scalpel_new/parquet_processed/XRPUSDC/1s/2025-07-02.parquet')
    df_1s['timestamp'] = pd.to_datetime(df_1s['timestamp'])
    df_1s = df_1s.set_index('timestamp')
    
    # Find the exit bar
    exit_dt = pd.to_datetime(exit_time)
    
    # Get data around exit time (±30 seconds)
    start_time = exit_dt - pd.Timedelta(seconds=30)
    end_time = exit_dt + pd.Timedelta(seconds=30)
    
    exit_data = df_1s[start_time:end_time]
    print(f"Našiel som {len(exit_data)} 1s barov okolo exit času")
    
    # Find exact exit bar
    exit_bar = df_1s.loc[exit_dt] if exit_dt in df_1s.index else None
    if exit_bar is not None:
        print(f"Exit bar data:")
        print(f"  Time: {exit_dt}")
        print(f"  OHLC: {exit_bar['open']:.5f} / {exit_bar['high']:.5f} / {exit_bar['low']:.5f} / {exit_bar['close']:.5f}")
        print(f"  Exit price from log: {exit_price:.5f}")
        
        # Check if exit price is within the bar's range
        if exit_bar['low'] <= exit_price <= exit_bar['high']:
            print("✅ Exit price je v rámci H/L tohto baru")
        else:
            print("❌ Exit price MIMO H/L tohto baru!")
            
        # Check if SL trigger makes sense
        if exit_bar['high'] >= sl_trigger:
            print("✅ High tohto baru dosiahol SL trigger")
        else:
            print(f"❌ High ({exit_bar['high']:.5f}) nedosiahol SL trigger ({sl_trigger:.5f})")
    else:
        print("❌ Nenašiel som exact exit bar")
        
except Exception as e:
    print(f"❌ Chyba pri načítaní dát: {e}")

print(f"\n🎯 ZÁVER:")
print("✅ SL korekcia funguje - SL je teraz správne nad entry")
print("✅ SHORT pozícia má negatívny PnL pri SL exit - TO JE SPRÁVNE!")
print("✅ Obchodovanie teraz funguje reálne - strata keď má byť strata")