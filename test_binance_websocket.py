#!/usr/bin/env python3
"""
Direct test of Binance WebSocket to diagnose the issue.
"""

import asyncio
import json
import websockets
import logging

logging.basicConfig(level=logging.INFO)
log = logging.getLogger("WebSocketTest")

async def test_binance_websocket():
    """Test Binance WebSocket directly."""
    
    # Test different timeframes to see what works
    test_urls = [
        "wss://fstream.binance.com/ws/xrpusdc@kline_1m",   # Try 1m first
        "wss://fstream.binance.com/ws/xrpusdt@kline_1m",   # Compare USDT
        "wss://fstream.binance.com/ws/xrpusdc@kline_1s",   # Test 1s
        "wss://fstream.binance.com/ws/xrpusdc@kline_5m",   # Test 5m
        "wss://fstream.binance.com/ws/xrpusdt@kline_1s",   # Compare USDT 1s
    ]
    
    for url in test_urls:
        log.info(f"🧪 Testing URL: {url}")
        
        try:
            async with websockets.connect(url, ping_interval=30, ping_timeout=10) as ws:
                log.info(f"✅ Connected to: {url}")
                
                # Wait for messages with timeout
                message_count = 0
                try:
                    for i in range(5):  # Wait for up to 5 messages or timeout
                        message = await asyncio.wait_for(ws.recv(), timeout=10.0)
                        message_count += 1
                        data = json.loads(message)
                        
                        # Log key info from kline data
                        if "k" in data:
                            kline = data["k"]
                            symbol = kline.get("s", "")
                            interval = kline.get("i", "")
                            open_time = kline.get("t", 0)
                            close_time = kline.get("T", 0)
                            is_closed = kline.get("x", False)
                            price = kline.get("c", "0")
                            log.info(f"📥 Message {message_count}: {symbol} {interval} price={price} closed={is_closed}")
                        else:
                            log.info(f"📥 Message {message_count}: {str(data)[:200]}...")
                        
                        if message_count >= 2:  # Stop after 2 messages
                            break
                            
                except asyncio.TimeoutError:
                    log.warning(f"⏰ Timeout waiting for messages from {url}")
                
                if message_count == 0:
                    log.error(f"❌ No messages received from {url}")
                else:
                    log.info(f"✅ SUCCESS: Received {message_count} messages from {url}")
                    return  # Success! Stop testing other URLs
                    
        except Exception as e:
            log.error(f"❌ Failed to connect to {url}: {e}")
    
    log.info("🏁 WebSocket test completed")

if __name__ == "__main__":
    asyncio.run(test_binance_websocket())