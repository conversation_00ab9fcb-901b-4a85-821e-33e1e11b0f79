#!/usr/bin/env python3
"""
Test CoinAPI WebSocket for 1s data support
"""
import asyncio
import json
import logging
import os
import websockets
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO)
log = logging.getLogger("CoinAPITest")

async def test_coinapi_1s():
    """Test CoinAPI WebSocket for 1s OHLCV data."""
    # Get API key from environment
    api_key = os.environ.get("COINAPI_KEY")
    if not api_key:
        log.error("❌ COINAPI_KEY not set in environment")
        return
        
    symbol = "BINANCEFTS_PERP_XRP_USDC"
    url = "wss://ws.coinapi.io/v1/"
    
    log.info(f"🧪 Testing CoinAPI 1s stream for {symbol}")
    log.info(f"🔗 URL: {url}")
    
    try:
        async with websockets.connect(url, ping_interval=30, ping_timeout=10) as ws:
            # Subscribe to 1s data
            subscribe_msg = {
                "type": "hello",
                "apikey": api_key,
                "heartbeat": True,
                "subscribe_data_type": ["ohlcv"],
                "subscribe_filter_symbol_id": [symbol],
                "subscribe_data_format": "JSON",
                "interval": "1s"  # Test 1s interval
            }
            
            await ws.send(json.dumps(subscribe_msg))
            log.info("📤 Subscription sent for 1s interval")
            
            message_count = 0
            start_time = datetime.now()
            
            async for message in ws:
                try:
                    data = json.loads(message)
                    msg_type = data.get("type")
                    
                    if msg_type == "ohlcv":
                        message_count += 1
                        close_price = data.get("price_close", "N/A")
                        time_end = data.get("time_period_end", "N/A")
                        log.info(f"📥 1s OHLCV #{message_count}: price={close_price} time={time_end}")
                        
                        if message_count >= 5:  # Get at least 5 messages
                            log.info(f"✅ SUCCESS: Received {message_count} 1s OHLCV messages")
                            break
                            
                    elif msg_type == "heartbeat":
                        log.debug("💓 Heartbeat received")
                        
                    elif msg_type == "error":
                        log.error(f"❌ CoinAPI error: {data}")
                        break
                        
                    else:
                        log.info(f"📥 Other message: {msg_type}")
                        
                    # Timeout after 60 seconds
                    if (datetime.now() - start_time).total_seconds() > 60:
                        log.warning("⏰ Test timeout - no 1s data received in 60 seconds")
                        break
                        
                except Exception as e:
                    log.error(f"💥 Message processing error: {e}")
                    log.error(f"Raw message: {message}")
                    
    except Exception as e:
        log.error(f"💥 WebSocket connection error: {e}")

if __name__ == "__main__":
    asyncio.run(test_coinapi_1s())