#!/usr/bin/env python3
"""
Test script pre overenie variance calculation bez celej simulácie
"""

import numpy as np
import logging

# Nastavenie logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
log = logging.getLogger(__name__)

def test_variance_calculation():
    """Test variance calculation s rôznymi hodnotami"""
    
    log.info("🧪 TESTING VARIANCE CALCULATION")
    
    # Simulujeme rôzne typy numpy arrays
    test_cases = [
        # Case 1: Simple float values
        (np.array([0.5]), np.array([0.3])),
        # Case 2: Multiple values arrays  
        (np.array([0.1, 0.2, 0.3]), np.array([0.15, 0.25, 0.35])),
        # Case 3: Negative values
        (np.array([-0.2]), np.array([-0.1])),
        # Case 4: Zero values
        (np.array([0.0]), np.array([0.0])),
        # Case 5: Large values
        (np.array([1.234567]), np.array([1.234568])),
    ]
    
    for i, (action_raw1, action_raw2) in enumerate(test_cases):
        log.info(f"--- Test Case {i+1} ---")
        log.info(f"action_raw1: {action_raw1}")
        log.info(f"action_raw2: {action_raw2}")
        
        try:
            # Pôvodný problémový kód
            # var_entry = abs(action_raw1[0] - action_raw2[0])
            # var_entry_float = float(var_entry)  # Toto hadzalo TypeError
            
            # Opravený kód s .flatten()[0] - robustný prístup
            var_entry = float(np.asarray(np.abs(action_raw1[0] - action_raw2[0])).flatten()[0])
            var_exit = float(np.asarray(np.abs(action_raw1[0] - action_raw2[0])).flatten()[0]) if len(action_raw1) > 3 else 0.0
            
            log.info(f"✅ SUCCESS: var_entry={var_entry:.6f}, var_exit={var_exit:.6f}")
            log.info(f"🔢 TYPES: var_entry type={type(var_entry)}, var_exit type={type(var_exit)}")
            
            # Simulujeme pôvodný problémový logging
            log.info(f"🤖 AGENT_VARIANCE: entry={var_entry:.6f}, exit={var_exit:.6f}")
            
        except Exception as e:
            log.error(f"❌ ERROR: {e}")
            log.error(f"Exception type: {type(e)}")
        
        log.info("")

if __name__ == "__main__":
    test_variance_calculation()